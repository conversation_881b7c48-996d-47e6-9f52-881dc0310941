{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductCard/productCard.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"productCard-module__UIKE7W__actionButtons\",\n  \"addToCartBtn\": \"productCard-module__UIKE7W__addToCartBtn\",\n  \"addToCartSection\": \"productCard-module__UIKE7W__addToCartSection\",\n  \"cartIcon\": \"productCard-module__UIKE7W__cartIcon\",\n  \"collection\": \"productCard-module__UIKE7W__collection\",\n  \"imageContainer\": \"productCard-module__UIKE7W__imageContainer\",\n  \"inStock\": \"productCard-module__UIKE7W__inStock\",\n  \"loading\": \"productCard-module__UIKE7W__loading\",\n  \"outOfStock\": \"productCard-module__UIKE7W__outOfStock\",\n  \"outOfStockOverlay\": \"productCard-module__UIKE7W__outOfStockOverlay\",\n  \"price\": \"productCard-module__UIKE7W__price\",\n  \"priceContainer\": \"productCard-module__UIKE7W__priceContainer\",\n  \"productCard\": \"productCard-module__UIKE7W__productCard\",\n  \"productDescription\": \"productCard-module__UIKE7W__productDescription\",\n  \"productImage\": \"productCard-module__UIKE7W__productImage\",\n  \"productInfo\": \"productCard-module__UIKE7W__productInfo\",\n  \"productName\": \"productCard-module__UIKE7W__productName\",\n  \"quantity\": \"productCard-module__UIKE7W__quantity\",\n  \"quantityBtn\": \"productCard-module__UIKE7W__quantityBtn\",\n  \"quantitySelector\": \"productCard-module__UIKE7W__quantitySelector\",\n  \"spin\": \"productCard-module__UIKE7W__spin\",\n  \"stockInfo\": \"productCard-module__UIKE7W__stockInfo\",\n  \"viewDetailsBtn\": \"productCard-module__UIKE7W__viewDetailsBtn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductCard/ProductCard.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { Product } from '@/services/types/entities';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport styles from './productCard.module.css';\r\n\r\ninterface ProductCardProps {\r\n  product: Product;\r\n  showAddToCart?: boolean;\r\n  showViewDetails?: boolean;\r\n}\r\n\r\nconst ProductCard: React.FC<ProductCardProps> = ({\r\n  product,\r\n  showAddToCart = true,\r\n  showViewDetails = true,\r\n}) => {\r\n  const { addToCart, state } = useCart();\r\n  const [isAddingToCart, setIsAddingToCart] = useState(false);\r\n  const [quantity, setQuantity] = useState(1);\r\n\r\n  const handleAddToCart = async () => {\r\n    try {\r\n      setIsAddingToCart(true);\r\n      await addToCart(product.id, quantity);\r\n      // You could add a toast notification here\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n      // You could add error notification here\r\n    } finally {\r\n      setIsAddingToCart(false);\r\n    }\r\n  };\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n    }).format(price);\r\n  };\r\n\r\n  const mainImage = product.images && product.images.length > 0 \r\n    ? product.images[0] \r\n    : '/images/placeholder-product.jpg';\r\n\r\n  const isInStock = product.stock > 0;\r\n\r\n  return (\r\n    <div className={styles.productCard}>\r\n      {/* Product Image */}\r\n      <div className={styles.imageContainer}>\r\n        <img\r\n          src={mainImage}\r\n          alt={product.name}\r\n          className={styles.productImage}\r\n        />\r\n        {!isInStock && (\r\n          <div className={styles.outOfStockOverlay}>\r\n            <span>Out of Stock</span>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Info */}\r\n      <div className={styles.productInfo}>\r\n        <h3 className={styles.productName}>{product.name}</h3>\r\n        \r\n        {product.description && (\r\n          <p className={styles.productDescription}>\r\n            {product.description.length > 100 \r\n              ? `${product.description.substring(0, 100)}...` \r\n              : product.description}\r\n          </p>\r\n        )}\r\n\r\n        <div className={styles.priceContainer}>\r\n          <span className={styles.price}>{formatPrice(product.price)}</span>\r\n          {product.collection && (\r\n            <span className={styles.collection}>{product.collection.name}</span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Stock Info */}\r\n        <div className={styles.stockInfo}>\r\n          {isInStock ? (\r\n            <span className={styles.inStock}>\r\n              {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}\r\n            </span>\r\n          ) : (\r\n            <span className={styles.outOfStock}>Out of Stock</span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Action Buttons */}\r\n        <div className={styles.actionButtons}>\r\n          {showViewDetails && (\r\n            <Link href={`/products/${product.id}`} className={styles.viewDetailsBtn}>\r\n              View Details\r\n            </Link>\r\n          )}\r\n          \r\n          {showAddToCart && isInStock && (\r\n            <div className={styles.addToCartSection}>\r\n              <div className={styles.quantitySelector}>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\r\n                  className={styles.quantityBtn}\r\n                  disabled={quantity <= 1}\r\n                >\r\n                  -\r\n                </button>\r\n                <span className={styles.quantity}>{quantity}</span>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}\r\n                  className={styles.quantityBtn}\r\n                  disabled={quantity >= product.stock}\r\n                >\r\n                  +\r\n                </button>\r\n              </div>\r\n              \r\n              <button\r\n                onClick={handleAddToCart}\r\n                disabled={isAddingToCart || state.isLoading}\r\n                className={styles.addToCartBtn}\r\n              >\r\n                {isAddingToCart ? (\r\n                  <span className={styles.loading}>Adding...</span>\r\n                ) : (\r\n                  <>\r\n                    <svg className={styles.cartIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                      <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\r\n                    </svg>\r\n                    Add to Cart\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductCard;\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AACA;AAEA;AACA;AANA;;;;;;AAcA,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACvB;IACC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,kBAAkB;QACtB,IAAI;YACF,kBAAkB;YAClB,MAAM,UAAU,QAAQ,EAAE,EAAE;QAC5B,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,wCAAwC;QAC1C,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;IAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;IAElC,qBACE,8OAAC;QAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;0BAEhC,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBACC,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;oBAE/B,CAAC,2BACA,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,iBAAiB;kCACtC,cAAA,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;kCAChC,8OAAC;wBAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,QAAQ,IAAI;;;;;;oBAE/C,QAAQ,WAAW,kBAClB,8OAAC;wBAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,QAAQ,WAAW,CAAC,MAAM,GAAG,MAC1B,GAAG,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC7C,QAAQ,WAAW;;;;;;kCAI3B,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,8OAAC;gCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY,QAAQ,KAAK;;;;;;4BACxD,QAAQ,UAAU,kBACjB,8OAAC;gCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,QAAQ,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,0BACC,8OAAC;4BAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,QAAQ,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;;;;iDAGjE,8OAAC;4BAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;4BACjC,iCACC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;0CAAE;;;;;;4BAK1E,iBAAiB,2BAChB,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;kDACrC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;0DACrC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;gDAClD,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY;0DACvB;;;;;;0DAGD,8OAAC;gDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;0DAAG;;;;;;0DACnC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,WAAW;gDAC9D,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY,QAAQ,KAAK;0DACpC;;;;;;;;;;;;kDAKH,8OAAC;wCACC,SAAS;wCACT,UAAU,kBAAkB,MAAM,SAAS;wCAC3C,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;kDAE7B,+BACC,8OAAC;4CAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAAE;;;;;iEAEjC;;8DACE,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;oDAAE,SAAQ;oDAAY,MAAK;oDAAO,QAAO;8DACtE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;gDACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;uCAEe", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductGrid/productGrid.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"emptyContainer\": \"productGrid-module__d3162q__emptyContainer\",\n  \"emptyIcon\": \"productGrid-module__d3162q__emptyIcon\",\n  \"emptyMessage\": \"productGrid-module__d3162q__emptyMessage\",\n  \"emptyTitle\": \"productGrid-module__d3162q__emptyTitle\",\n  \"loadingButton\": \"productGrid-module__d3162q__loadingButton\",\n  \"loadingCard\": \"productGrid-module__d3162q__loadingCard\",\n  \"loadingContainer\": \"productGrid-module__d3162q__loadingContainer\",\n  \"loadingContent\": \"productGrid-module__d3162q__loadingContent\",\n  \"loadingDescription\": \"productGrid-module__d3162q__loadingDescription\",\n  \"loadingGrid\": \"productGrid-module__d3162q__loadingGrid\",\n  \"loadingImage\": \"productGrid-module__d3162q__loadingImage\",\n  \"loadingPrice\": \"productGrid-module__d3162q__loadingPrice\",\n  \"loadingTitle\": \"productGrid-module__d3162q__loadingTitle\",\n  \"productGrid\": \"productGrid-module__d3162q__productGrid\",\n  \"pulse\": \"productGrid-module__d3162q__pulse\",\n  \"shimmer\": \"productGrid-module__d3162q__shimmer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductGrid/ProductGrid.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Product } from '@/services/types/entities';\r\nimport ProductCard from '../ProductCard/ProductCard';\r\nimport styles from './productGrid.module.css';\r\n\r\ninterface ProductGridProps {\r\n  products: Product[];\r\n  isLoading?: boolean;\r\n  showAddToCart?: boolean;\r\n  showViewDetails?: boolean;\r\n  emptyMessage?: string;\r\n}\r\n\r\nconst ProductGrid: React.FC<ProductGridProps> = ({\r\n  products,\r\n  isLoading = false,\r\n  showAddToCart = true,\r\n  showViewDetails = true,\r\n  emptyMessage = 'No products found.',\r\n}) => {\r\n  if (isLoading) {\r\n    return (\r\n      <div className={styles.loadingContainer}>\r\n        <div className={styles.loadingGrid}>\r\n          {Array.from({ length: 6 }).map((_, index) => (\r\n            <div key={index} className={styles.loadingCard}>\r\n              <div className={styles.loadingImage}></div>\r\n              <div className={styles.loadingContent}>\r\n                <div className={styles.loadingTitle}></div>\r\n                <div className={styles.loadingDescription}></div>\r\n                <div className={styles.loadingPrice}></div>\r\n                <div className={styles.loadingButton}></div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (products.length === 0) {\r\n    return (\r\n      <div className={styles.emptyContainer}>\r\n        <div className={styles.emptyIcon}>\r\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\r\n            <path d=\"M16 16s-1.5-2-4-2-4 2-4 2\"/>\r\n            <line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"/>\r\n            <line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"/>\r\n          </svg>\r\n        </div>\r\n        <h3 className={styles.emptyTitle}>No Products Found</h3>\r\n        <p className={styles.emptyMessage}>{emptyMessage}</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={styles.productGrid}>\r\n      {products.map((product) => (\r\n        <ProductCard\r\n          key={product.id}\r\n          product={product}\r\n          showAddToCart={showAddToCart}\r\n          showViewDetails={showViewDetails}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductGrid;\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAeA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,eAAe,oBAAoB,EACpC;IACC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;sBACrC,cAAA,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;0BAC/B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wBAAgB,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;0CAC5C,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0CACnC,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;;;;;;kDACzC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;;;;;;;;;;;;uBAN9B;;;;;;;;;;;;;;;IAapB;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,8OAAC;oBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8BAC9B,cAAA,8OAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,8OAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAO,IAAG;;;;;;0CACjC,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAQ,IAAG;;;;;;;;;;;;;;;;;8BAGvC,8OAAC;oBAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8BAAE;;;;;;8BAClC,8OAAC;oBAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;8BAAG;;;;;;;;;;;;IAG1C;IAEA,qBACE,8OAAC;QAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;kBAC/B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4JAAA,CAAA,UAAW;gBAEV,SAAS;gBACT,eAAe;gBACf,iBAAiB;eAHZ,QAAQ,EAAE;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"activeThumbnail\": \"productImageGallery-module__AcqG6a__activeThumbnail\",\n  \"closeZoomButton\": \"productImageGallery-module__AcqG6a__closeZoomButton\",\n  \"imageCounter\": \"productImageGallery-module__AcqG6a__imageCounter\",\n  \"imageGallery\": \"productImageGallery-module__AcqG6a__imageGallery\",\n  \"mainImage\": \"productImageGallery-module__AcqG6a__mainImage\",\n  \"mainImageContainer\": \"productImageGallery-module__AcqG6a__mainImageContainer\",\n  \"navButton\": \"productImageGallery-module__AcqG6a__navButton\",\n  \"nextButton\": \"productImageGallery-module__AcqG6a__nextButton\",\n  \"prevButton\": \"productImageGallery-module__AcqG6a__prevButton\",\n  \"thumbnail\": \"productImageGallery-module__AcqG6a__thumbnail\",\n  \"thumbnailContainer\": \"productImageGallery-module__AcqG6a__thumbnailContainer\",\n  \"thumbnailGrid\": \"productImageGallery-module__AcqG6a__thumbnailGrid\",\n  \"thumbnailImage\": \"productImageGallery-module__AcqG6a__thumbnailImage\",\n  \"zoomIndicator\": \"productImageGallery-module__AcqG6a__zoomIndicator\",\n  \"zoomOverlay\": \"productImageGallery-module__AcqG6a__zoomOverlay\",\n  \"zoomed\": \"productImageGallery-module__AcqG6a__zoomed\",\n  \"zoomedImage\": \"productImageGallery-module__AcqG6a__zoomedImage\",\n  \"zoomedImageContainer\": \"productImageGallery-module__AcqG6a__zoomedImageContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductImageGallery/ProductImageGallery.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport styles from './productImageGallery.module.css';\r\n\r\ninterface ProductImageGalleryProps {\r\n  images: string[];\r\n  productName: string;\r\n}\r\n\r\nconst ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ \r\n  images, \r\n  productName \r\n}) => {\r\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\r\n  const [isZoomed, setIsZoomed] = useState(false);\r\n\r\n  // Use placeholder if no images provided\r\n  const galleryImages = images.length > 0 \r\n    ? images \r\n    : ['/images/placeholder-product.jpg'];\r\n\r\n  const currentImage = galleryImages[selectedImageIndex];\r\n\r\n  const handleThumbnailClick = (index: number) => {\r\n    setSelectedImageIndex(index);\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  const handleMainImageClick = () => {\r\n    setIsZoomed(!isZoomed);\r\n  };\r\n\r\n  const handlePrevImage = () => {\r\n    setSelectedImageIndex((prev) => \r\n      prev === 0 ? galleryImages.length - 1 : prev - 1\r\n    );\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  const handleNextImage = () => {\r\n    setSelectedImageIndex((prev) => \r\n      prev === galleryImages.length - 1 ? 0 : prev + 1\r\n    );\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  return (\r\n    <div className={styles.imageGallery}>\r\n      {/* Main Image Display */}\r\n      <div className={styles.mainImageContainer}>\r\n        <img\r\n          src={currentImage}\r\n          alt={`${productName} - Image ${selectedImageIndex + 1}`}\r\n          className={`${styles.mainImage} ${isZoomed ? styles.zoomed : ''}`}\r\n          onClick={handleMainImageClick}\r\n        />\r\n        \r\n        {/* Navigation Arrows */}\r\n        {galleryImages.length > 1 && (\r\n          <>\r\n            <button\r\n              className={`${styles.navButton} ${styles.prevButton}`}\r\n              onClick={handlePrevImage}\r\n              aria-label=\"Previous image\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M15 18L9 12L15 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            \r\n            <button\r\n              className={`${styles.navButton} ${styles.nextButton}`}\r\n              onClick={handleNextImage}\r\n              aria-label=\"Next image\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M9 18L15 12L9 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </>\r\n        )}\r\n\r\n        {/* Image Counter */}\r\n        {galleryImages.length > 1 && (\r\n          <div className={styles.imageCounter}>\r\n            {selectedImageIndex + 1} / {galleryImages.length}\r\n          </div>\r\n        )}\r\n\r\n        {/* Zoom Indicator */}\r\n        <div className={styles.zoomIndicator}>\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\r\n            <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n            <line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n            <line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n          </svg>\r\n          Click to zoom\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thumbnail Gallery */}\r\n      {galleryImages.length > 1 && (\r\n        <div className={styles.thumbnailContainer}>\r\n          <div className={styles.thumbnailGrid}>\r\n            {galleryImages.map((image, index) => (\r\n              <button\r\n                key={index}\r\n                className={`${styles.thumbnail} ${\r\n                  index === selectedImageIndex ? styles.activeThumbnail : ''\r\n                }`}\r\n                onClick={() => handleThumbnailClick(index)}\r\n                aria-label={`View image ${index + 1}`}\r\n              >\r\n                <img\r\n                  src={image}\r\n                  alt={`${productName} thumbnail ${index + 1}`}\r\n                  className={styles.thumbnailImage}\r\n                />\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Zoom Overlay */}\r\n      {isZoomed && (\r\n        <div className={styles.zoomOverlay} onClick={() => setIsZoomed(false)}>\r\n          <div className={styles.zoomedImageContainer}>\r\n            <img\r\n              src={currentImage}\r\n              alt={`${productName} - Zoomed view`}\r\n              className={styles.zoomedImage}\r\n            />\r\n            <button\r\n              className={styles.closeZoomButton}\r\n              onClick={() => setIsZoomed(false)}\r\n              aria-label=\"Close zoom view\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductImageGallery;\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AACA;AAHA;;;;AAUA,MAAM,sBAA0D,CAAC,EAC/D,MAAM,EACN,WAAW,EACZ;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wCAAwC;IACxC,MAAM,gBAAgB,OAAO,MAAM,GAAG,IAClC,SACA;QAAC;KAAkC;IAEvC,MAAM,eAAe,aAAa,CAAC,mBAAmB;IAEtD,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,uBAAuB;QAC3B,YAAY,CAAC;IACf;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,IAAI,cAAc,MAAM,GAAG,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,cAAc,MAAM,GAAG,IAAI,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,YAAY;;0BAEjC,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACvC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,YAAY,SAAS,EAAE,qBAAqB,GAAG;wBACvD,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,uLAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;wBACjE,SAAS;;;;;;oBAIV,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC;gCACC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,uLAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCACC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,uLAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;oBAQxB,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,YAAY;;4BAChC,qBAAqB;4BAAE;4BAAI,cAAc,MAAM;;;;;;;kCAKpD,8OAAC;wBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,8OAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAI,QAAO;wCAAe,aAAY;;;;;;kDAChE,8OAAC;wCAAK,GAAE;wCAAqB,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACjF,8OAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACzF,8OAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;4BACrF;;;;;;;;;;;;;YAMT,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,kBAAkB;0BACvC,cAAA,8OAAC;oBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,aAAa;8BACjC,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;4BAEC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAC9B,UAAU,qBAAqB,uLAAA,CAAA,UAAM,CAAC,eAAe,GAAG,IACxD;4BACF,SAAS,IAAM,qBAAqB;4BACpC,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;sCAErC,cAAA,8OAAC;gCACC,KAAK;gCACL,KAAK,GAAG,YAAY,WAAW,EAAE,QAAQ,GAAG;gCAC5C,WAAW,uLAAA,CAAA,UAAM,CAAC,cAAc;;;;;;2BAV7B;;;;;;;;;;;;;;;YAmBd,0BACC,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,WAAW;gBAAE,SAAS,IAAM,YAAY;0BAC7D,cAAA,8OAAC;oBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,oBAAoB;;sCACzC,8OAAC;4BACC,KAAK;4BACL,KAAK,GAAG,YAAY,cAAc,CAAC;4BACnC,WAAW,uLAAA,CAAA,UAAM,CAAC,WAAW;;;;;;sCAE/B,8OAAC;4BACC,WAAW,uLAAA,CAAA,UAAM,CAAC,eAAe;4BACjC,SAAS,IAAM,YAAY;4BAC3B,cAAW;sCAEX,cAAA,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,8OAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACxF,8OAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG;uCAEe", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"productSpecifications-module__J4NrIa__active\",\n  \"careContent\": \"productSpecifications-module__J4NrIa__careContent\",\n  \"careSection\": \"productSpecifications-module__J4NrIa__careSection\",\n  \"description\": \"productSpecifications-module__J4NrIa__description\",\n  \"detailsContent\": \"productSpecifications-module__J4NrIa__detailsContent\",\n  \"downloadLink\": \"productSpecifications-module__J4NrIa__downloadLink\",\n  \"downloadLinks\": \"productSpecifications-module__J4NrIa__downloadLinks\",\n  \"downloadSection\": \"productSpecifications-module__J4NrIa__downloadSection\",\n  \"featureList\": \"productSpecifications-module__J4NrIa__featureList\",\n  \"inStock\": \"productSpecifications-module__J4NrIa__inStock\",\n  \"outOfStock\": \"productSpecifications-module__J4NrIa__outOfStock\",\n  \"section\": \"productSpecifications-module__J4NrIa__section\",\n  \"sectionContent\": \"productSpecifications-module__J4NrIa__sectionContent\",\n  \"sectionHeader\": \"productSpecifications-module__J4NrIa__sectionHeader\",\n  \"shareButton\": \"productSpecifications-module__J4NrIa__shareButton\",\n  \"shareButtons\": \"productSpecifications-module__J4NrIa__shareButtons\",\n  \"shareLabel\": \"productSpecifications-module__J4NrIa__shareLabel\",\n  \"shareSection\": \"productSpecifications-module__J4NrIa__shareSection\",\n  \"specGrid\": \"productSpecifications-module__J4NrIa__specGrid\",\n  \"specLabel\": \"productSpecifications-module__J4NrIa__specLabel\",\n  \"specRow\": \"productSpecifications-module__J4NrIa__specRow\",\n  \"specValue\": \"productSpecifications-module__J4NrIa__specValue\",\n  \"specificationsContainer\": \"productSpecifications-module__J4NrIa__specificationsContainer\",\n  \"tag\": \"productSpecifications-module__J4NrIa__tag\",\n  \"tagContainer\": \"productSpecifications-module__J4NrIa__tagContainer\",\n  \"toggleIcon\": \"productSpecifications-module__J4NrIa__toggleIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductSpecifications/ProductSpecifications.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Product } from '@/services/types/entities';\r\nimport styles from './productSpecifications.module.css';\r\n\r\ninterface ProductSpecificationsProps {\r\n  product: Product;\r\n}\r\n\r\nconst ProductSpecifications: React.FC<ProductSpecificationsProps> = ({ product }) => {\r\n  const [activeSection, setActiveSection] = useState<string | null>('specifications');\r\n\r\n  const toggleSection = (section: string) => {\r\n    setActiveSection(activeSection === section ? null : section);\r\n  };\r\n\r\nconst hasSpecifications = product.productSpecifications &&\r\n  Object.values(product.productSpecifications).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDetails = product.productDetails &&\r\n  Object.values(product.productDetails).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDownloadableContent = product.downloadableContent &&\r\n  Object.values(product.downloadableContent).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\n\r\n  return (\r\n    <div className={styles.specificationsContainer}>\r\n      {/* Product Specifications Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'specifications' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('specifications')}\r\n        >\r\n          <span>Product Specifications</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'specifications' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'specifications' && hasSpecifications && (\r\n        \r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.specGrid}>\r\n              {product.productSpecifications?.material && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Material:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.material}</span>\r\n                </div>\r\n              )}\r\n \r\n              {product.productSpecifications?.dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.dimensions}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.totalWeight && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Total Weight:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.totalWeight}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.weightWithWater && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Weight With Water:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.weightWithWater}</span>\r\n                </div>\r\n              )}\r\n{/* ---- -------------------------------------------------------------------------------------------------- */}\r\n\r\n              {/* {product.productSpecifications?.base_Dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Base Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.base_Dimensions}</span>\r\n                </div>\r\n              )} */}\r\n\r\n              {/* {product.productSpecifications?.photographed_In && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Photographed In:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.photographed_In}</span>\r\n                </div>\r\n              )} */}\r\n\r\n              {/* {product.productSpecifications?.pieces && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Pieces:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.pieces}</span>\r\n                </div>\r\n              )} */}\r\n              {product.productSpecifications?.waterVolume && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Water Volume:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.waterVolume}</span>\r\n                </div>\r\n              )}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Collection:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.collection?.name || 'Not specified'}\r\n                </span>\r\n              </div> */}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Product Code:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.productCode || `P-${product.id.toString().padStart(3, '0')}-AS`}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Stock Status:</span>\r\n                <span className={`${styles.specValue} ${\r\n                  product.stock > 0 ? styles.inStock : styles.outOfStock\r\n                }`}>\r\n                  {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {product.tags && product.tags.length > 0 && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Tags:</span>\r\n                  <span className={styles.specValue}>\r\n                    <div className={styles.tagContainer}>\r\n                      {product.tags.map((tag, index) => (\r\n                        <span key={index} className={styles.tag}>\r\n                          {tag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Details Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'details' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('details')}\r\n        >\r\n          <span>Product Details</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'details' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'details' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.detailsContent}>\r\n              {product.description && (\r\n                <p className={styles.description}>{product.description}</p>\r\n              )}\r\n\r\n              {hasDetails && (\r\n                <div className={styles.specGrid}>\r\n                  {product.productDetails?.upc && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>UPC:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.upc}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.indoorUseOnly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Indoor Use Only:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.indoorUseOnly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assemblyRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assembly Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assemblyRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.easeOfAssembly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ease of Assembly:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.easeOfAssembly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assistanceRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assistance Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assistanceRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.splashLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Splash Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.splashLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundType && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Type:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundType}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.replacementPumpKit && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Replacement Pump Kit:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.replacementPumpKit}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.electricalCordLength && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Electrical Cord Length:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.electricalCordLength}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.pumpSize && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Pump Size:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.pumpSize}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.shipMethod && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ship Method:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.shipMethod}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.drainage_Info && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Drainage Info:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.drainage_Info}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Top && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Top:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Top}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Bottom && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Bottom:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Bottom}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Height && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Height:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Height}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.factory_Code && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Factory Code:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.factory_Code}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.catalogPage && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Catalog Page:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.catalogPage}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Care Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'care' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('care')}\r\n        >\r\n          <span>Product Care and Downloadable Content</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'care' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'care' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.careContent}>\r\n              {hasDownloadableContent && (\r\n                <div className={styles.downloadSection}>\r\n                  <h4>Downloadable Content:</h4>\r\n                  <div className={styles.downloadLinks}>\r\n                    {product.downloadableContent?.care && (\r\n                      <a\r\n                        href={product.downloadableContent.care}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📄 Care Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.productInstructions && (\r\n                      <a\r\n                        href={product.downloadableContent.productInstructions}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📋 Product Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.cad && (\r\n                      <a\r\n                        href={product.downloadableContent.cad}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📐 CAD Files\r\n                      </a>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}              \r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Share Section */}\r\n      <div className={styles.shareSection}>\r\n        <span className={styles.shareLabel}>Share</span>\r\n        <div className={styles.shareButtons}>\r\n          <button className={styles.shareButton} aria-label=\"Share on Facebook\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n            </svg>\r\n          </button>\r\n          \r\n          <button className={styles.shareButton} aria-label=\"Share on Pinterest\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-12.014C24.007 5.36 18.641.001 12.017.001z\"/>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductSpecifications;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAUA,MAAM,wBAA8D,CAAC,EAAE,OAAO,EAAE;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,gBAAgB,CAAC;QACrB,iBAAiB,kBAAkB,UAAU,OAAO;IACtD;IAEF,MAAM,oBAAoB,QAAQ,qBAAqB,IACrD,OAAO,MAAM,CAAC,QAAQ,qBAAqB,EAAE,IAAI,CAC/C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,aAAa,QAAQ,cAAc,IACvC,OAAO,MAAM,CAAC,QAAQ,cAAc,EAAE,IAAI,CACxC,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,yBAAyB,QAAQ,mBAAmB,IACxD,OAAO,MAAM,CAAC,QAAQ,mBAAmB,EAAE,IAAI,CAC7C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAIzF,qBACE,8OAAC;QAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,uBAAuB;;0BAE5C,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,mBAAmB,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IACrD;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,mBAAmB,MAAM;;;;;;;;;;;;oBAI/C,kBAAkB,oBAAoB,mCAErC,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,QAAQ;;gCAC5B,QAAQ,qBAAqB,EAAE,0BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,QAAQ;;;;;;;;;;;;gCAI7E,QAAQ,qBAAqB,EAAE,4BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,UAAU;;;;;;;;;;;;gCAI/E,QAAQ,qBAAqB,EAAE,6BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCAIhF,QAAQ,qBAAqB,EAAE,iCAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gCAyBpF,QAAQ,qBAAqB,EAAE,6BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCA2BhF,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAC/B,cAAA,8OAAC;gDAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAChC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;wDAAiB,WAAW,2LAAA,CAAA,UAAM,CAAC,GAAG;kEACpC;uDADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc7B,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,YAAY,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,YAAY,MAAM;;;;;;;;;;;;oBAIxC,kBAAkB,2BACjB,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;;gCAClC,QAAQ,WAAW,kBAClB,8OAAC;oCAAE,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;8CAAG,QAAQ,WAAW;;;;;;gCAGvD,4BACC,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,QAAQ;;wCAC5B,QAAQ,cAAc,EAAE,qBACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,GAAG;;;;;;;;;;;;wCAIjE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,kCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,gBAAgB;;;;;;;;;;;;wCAI9E,QAAQ,cAAc,EAAE,gCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,cAAc;;;;;;;;;;;;wCAI5E,QAAQ,cAAc,EAAE,oCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,6BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;wCAIzE,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,2BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,SAAS;;;;;;;;;;;;wCAIvE,QAAQ,cAAc,EAAE,oCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,sCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,oBAAoB;;;;;;;;;;;;wCAIlF,QAAQ,cAAc,EAAE,0BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,QAAQ;;;;;;;;;;;;wCAItE,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAGxE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,8BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,YAAY;;;;;;;;;;;;wCAG1E,QAAQ,cAAc,EAAE,6BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtF,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,SAAS,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC3C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,SAAS,MAAM;;;;;;;;;;;;oBAIrC,kBAAkB,wBACjB,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;sCAC/B,wCACC,8OAAC;gCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,eAAe;;kDACpC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,aAAa;;4CACjC,QAAQ,mBAAmB,EAAE,sBAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,IAAI;gDACtC,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qCAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,mBAAmB;gDACrD,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qBAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,GAAG;gDACrC,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,8OAAC;wBAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCACpC,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,8OAAC;gCAAO,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAIZ,8OAAC;gCAAO,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"availability\": \"relatedProducts-module__zj5_XW__availability\",\n  \"disabled\": \"relatedProducts-module__zj5_XW__disabled\",\n  \"imageContainer\": \"relatedProducts-module__zj5_XW__imageContainer\",\n  \"inStock\": \"relatedProducts-module__zj5_XW__inStock\",\n  \"outOfStock\": \"relatedProducts-module__zj5_XW__outOfStock\",\n  \"outOfStockOverlay\": \"relatedProducts-module__zj5_XW__outOfStockOverlay\",\n  \"price\": \"relatedProducts-module__zj5_XW__price\",\n  \"priceContainer\": \"relatedProducts-module__zj5_XW__priceContainer\",\n  \"productCard\": \"relatedProducts-module__zj5_XW__productCard\",\n  \"productCode\": \"relatedProducts-module__zj5_XW__productCode\",\n  \"productDetails\": \"relatedProducts-module__zj5_XW__productDetails\",\n  \"productImage\": \"relatedProducts-module__zj5_XW__productImage\",\n  \"productInfo\": \"relatedProducts-module__zj5_XW__productInfo\",\n  \"productLink\": \"relatedProducts-module__zj5_XW__productLink\",\n  \"productName\": \"relatedProducts-module__zj5_XW__productName\",\n  \"productsContainer\": \"relatedProducts-module__zj5_XW__productsContainer\",\n  \"productsGrid\": \"relatedProducts-module__zj5_XW__productsGrid\",\n  \"relatedProducts\": \"relatedProducts-module__zj5_XW__relatedProducts\",\n  \"scrollButton\": \"relatedProducts-module__zj5_XW__scrollButton\",\n  \"scrollControls\": \"relatedProducts-module__zj5_XW__scrollControls\",\n  \"sectionHeader\": \"relatedProducts-module__zj5_XW__sectionHeader\",\n  \"sectionTitle\": \"relatedProducts-module__zj5_XW__sectionTitle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/RelatedProducts/RelatedProducts.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { Product } from '@/services/types/entities';\r\nimport styles from './relatedProducts.module.css';\r\n\r\ninterface RelatedProductsProps {\r\n  products: Product[];\r\n}\r\n\r\nconst RelatedProducts: React.FC<RelatedProductsProps> = ({ products }) => {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\r\n  const [canScrollRight, setCanScrollRight] = useState(true);\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(price);\r\n  };\r\n\r\n  const updateScrollButtons = () => {\r\n    if (scrollContainerRef.current) {\r\n      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;\r\n      setCanScrollLeft(scrollLeft > 0);\r\n      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);\r\n    }\r\n  };\r\n\r\n  const scrollLeft = () => {\r\n    if (scrollContainerRef.current) {\r\n      const cardWidth = 320; // Card width + gap\r\n      scrollContainerRef.current.scrollBy({ left: -cardWidth, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (scrollContainerRef.current) {\r\n      const cardWidth = 320; // Card width + gap\r\n      scrollContainerRef.current.scrollBy({ left: cardWidth, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const scrollContainer = scrollContainerRef.current;\r\n    if (scrollContainer) {\r\n      updateScrollButtons();\r\n      scrollContainer.addEventListener('scroll', updateScrollButtons);\r\n      return () => scrollContainer.removeEventListener('scroll', updateScrollButtons);\r\n    }\r\n  }, [products]);\r\n\r\n  if (!products || products.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={styles.relatedProducts}>\r\n      <div className={styles.sectionHeader}>\r\n        <h2 className={styles.sectionTitle}>You May Also Like</h2>\r\n        \r\n        {products.length > 3 && (\r\n          <div className={styles.scrollControls}>\r\n            <button\r\n              className={`${styles.scrollButton} ${!canScrollLeft ? styles.disabled : ''}`}\r\n              onClick={scrollLeft}\r\n              disabled={!canScrollLeft}\r\n              aria-label=\"Scroll left\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M15 18L9 12L15 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            \r\n            <button\r\n              className={`${styles.scrollButton} ${!canScrollRight ? styles.disabled : ''}`}\r\n              onClick={scrollRight}\r\n              disabled={!canScrollRight}\r\n              aria-label=\"Scroll right\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M9 18L15 12L9 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div \r\n        className={styles.productsContainer}\r\n        ref={scrollContainerRef}\r\n      >\r\n        <div className={styles.productsGrid}>\r\n          {products.map((product) => {\r\n            const mainImage = product.images && product.images.length > 0 \r\n              ? product.images[0] \r\n              : '/images/placeholder-product.jpg';\r\n            \r\n            const isInStock = product.stock > 0;\r\n\r\n            return (\r\n              <div key={product.id} className={styles.productCard}>\r\n                <Link href={`/products/${product.id}`} className={styles.productLink}>\r\n                  <div className={styles.imageContainer}>\r\n                    <img\r\n                      src={mainImage}\r\n                      alt={product.name}\r\n                      className={styles.productImage}\r\n                    />\r\n                    {!isInStock && (\r\n                      <div className={styles.outOfStockOverlay}>\r\n                        <span>Out of Stock</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  <div className={styles.productInfo}>\r\n                    <h3 className={styles.productName}>{product.name}</h3>\r\n                    \r\n                    <div className={styles.productDetails}>\r\n                      <span className={styles.productCode}>\r\n                        P-{product.id.toString().padStart(3, '0')}-AS\r\n                      </span>\r\n                      \r\n                      <div className={styles.availability}>\r\n                        {isInStock ? (\r\n                          <span className={styles.inStock}>\r\n                            Available in 14 Colors And 3 sizes\r\n                          </span>\r\n                        ) : (\r\n                          <span className={styles.outOfStock}>\r\n                            Currently Out of Stock\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className={styles.priceContainer}>\r\n                      <span className={styles.price}>{formatPrice(product.price)}</span>\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RelatedProducts;\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,4CAA4C;;;;AAG5C;AACA;AAEA;AALA;;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,sBAAsB;QAC1B,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,mBAAmB,OAAO;YAC3E,iBAAiB,aAAa;YAC9B,kBAAkB,aAAa,cAAc,cAAc;QAC7D;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM,CAAC;gBAAW,UAAU;YAAS;QAC7E;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAW,UAAU;YAAS;QAC5E;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,mBAAmB,OAAO;QAClD,IAAI,iBAAiB;YACnB;YACA,gBAAgB,gBAAgB,CAAC,UAAU;YAC3C,OAAO,IAAM,gBAAgB,mBAAmB,CAAC,UAAU;QAC7D;IACF,GAAG;QAAC;KAAS;IAEb,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;;0BACpC,8OAAC;gBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;;kCAClC,8OAAC;wBAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;oBAEnC,SAAS,MAAM,GAAG,mBACjB,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,8OAAC;gCACC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,gBAAgB,+KAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC5E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCACC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,iBAAiB,+KAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC7E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,8OAAC;gBACC,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;gBACnC,KAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;8BAChC,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;wBAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;wBAElC,qBACE,8OAAC;4BAAqB,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;sCACjD,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;kDAClE,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC;gDACC,KAAK;gDACL,KAAK,QAAQ,IAAI;gDACjB,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;4CAE/B,CAAC,2BACA,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;0DACtC,cAAA,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;0DAAG,QAAQ,IAAI;;;;;;0DAEhD,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;kEACnC,8OAAC;wDAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;4DAAE;4DAChC,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG;4DAAK;;;;;;;kEAG5C,8OAAC;wDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;kEAChC,0BACC,8OAAC;4DAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,OAAO;sEAAE;;;;;iFAIjC,8OAAC;4DAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;sEAAE;;;;;;;;;;;;;;;;;0DAO1C,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;0DACnC,cAAA,8OAAC;oDAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,KAAK;8DAAG,YAAY,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;2BArCvD,QAAQ,EAAE;;;;;oBA2CxB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/index.ts"], "sourcesContent": ["// Product Components\r\nexport { default as ProductCard } from './ProductCard/ProductCard';\r\nexport { default as ProductGrid } from './ProductGrid/ProductGrid';\r\nexport { default as ProductImageGallery } from './ProductImageGallery/ProductImageGallery';\r\nexport { default as ProductSpecifications } from './ProductSpecifications/ProductSpecifications';\r\n// export { default as PatinaSelector } from './PatinaSelector/PatinaSelector';\r\nexport { default as RelatedProducts } from './RelatedProducts/RelatedProducts';\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;AACrB;AACA;AACA;AACA;AACA,+EAA+E;AAC/E", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/collections/[id]/collectionPage.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"collectionPage-module__Lnisnq__active\",\n  \"advancedFilters\": \"collectionPage-module__Lnisnq__advancedFilters\",\n  \"checkbox\": \"collectionPage-module__Lnisnq__checkbox\",\n  \"checkboxLabel\": \"collectionPage-module__Lnisnq__checkboxLabel\",\n  \"clearFilters\": \"collectionPage-module__Lnisnq__clearFilters\",\n  \"collectionDescription\": \"collectionPage-module__Lnisnq__collectionDescription\",\n  \"collectionHeader\": \"collectionPage-module__Lnisnq__collectionHeader\",\n  \"collectionLevel\": \"collectionPage-module__Lnisnq__collectionLevel\",\n  \"collectionMeta\": \"collectionPage-module__Lnisnq__collectionMeta\",\n  \"collectionPage\": \"collectionPage-module__Lnisnq__collectionPage\",\n  \"collectionTitle\": \"collectionPage-module__Lnisnq__collectionTitle\",\n  \"container\": \"collectionPage-module__Lnisnq__container\",\n  \"desc\": \"collectionPage-module__Lnisnq__desc\",\n  \"errorContainer\": \"collectionPage-module__Lnisnq__errorContainer\",\n  \"filterGrid\": \"collectionPage-module__Lnisnq__filterGrid\",\n  \"filterGroup\": \"collectionPage-module__Lnisnq__filterGroup\",\n  \"filterLabel\": \"collectionPage-module__Lnisnq__filterLabel\",\n  \"filterSection\": \"collectionPage-module__Lnisnq__filterSection\",\n  \"filterToggle\": \"collectionPage-module__Lnisnq__filterToggle\",\n  \"headerContent\": \"collectionPage-module__Lnisnq__headerContent\",\n  \"loadingContainer\": \"collectionPage-module__Lnisnq__loadingContainer\",\n  \"loadingSpinner\": \"collectionPage-module__Lnisnq__loadingSpinner\",\n  \"priceInput\": \"collectionPage-module__Lnisnq__priceInput\",\n  \"priceRange\": \"collectionPage-module__Lnisnq__priceRange\",\n  \"productCount\": \"collectionPage-module__Lnisnq__productCount\",\n  \"productsSection\": \"collectionPage-module__Lnisnq__productsSection\",\n  \"searchBar\": \"collectionPage-module__Lnisnq__searchBar\",\n  \"searchField\": \"collectionPage-module__Lnisnq__searchField\",\n  \"searchIcon\": \"collectionPage-module__Lnisnq__searchIcon\",\n  \"searchInput\": \"collectionPage-module__Lnisnq__searchInput\",\n  \"sortControls\": \"collectionPage-module__Lnisnq__sortControls\",\n  \"sortDirection\": \"collectionPage-module__Lnisnq__sortDirection\",\n  \"sortSelect\": \"collectionPage-module__Lnisnq__sortSelect\",\n  \"spin\": \"collectionPage-module__Lnisnq__spin\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/collections/%5Bid%5D/page.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'next/navigation';\r\nimport { Product, Collection } from '@/services/types/entities';\r\nimport { productService, collectionService } from '@/services';\r\nimport { ProductGrid } from '@/components/products';\r\nimport styles from './collectionPage.module.css';\r\n\r\ninterface FilterState {\r\n  search: string;\r\n  priceRange: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  inStockOnly: boolean;\r\n  sortBy: 'name' | 'price' | 'newest';\r\n  sortDirection: 'asc' | 'desc';\r\n}\r\n\r\nexport default function CollectionPage() {\r\n  const params = useParams();\r\n  const collectionId = parseInt(params.id as string);\r\n\r\n  const [collection, setCollection] = useState<Collection | null>(null);\r\n  const [products, setProducts] = useState<Product[]>([]);\r\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  \r\n  const [filters, setFilters] = useState<FilterState>({\r\n    search: '',\r\n    priceRange: { min: 0, max: 10000 },\r\n    inStockOnly: false,\r\n    sortBy: 'name',\r\n    sortDirection: 'asc'\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (collectionId) {\r\n      fetchData();\r\n    }\r\n  }, [collectionId]);\r\n\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [products, filters]);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      const [collectionData, productsData] = await Promise.all([\r\n        collectionService.get.getById(collectionId),\r\n        productService.get.getByCollection(collectionId)\r\n      ]);\r\n\r\n      setCollection(collectionData);\r\n      setProducts(productsData);\r\n      \r\n      // Set initial price range based on actual products\r\n      if (productsData.length > 0) {\r\n        const prices = productsData.map(p => p.price);\r\n        const minPrice = Math.min(...prices);\r\n        const maxPrice = Math.max(...prices);\r\n        setFilters(prev => ({\r\n          ...prev,\r\n          priceRange: { min: minPrice, max: maxPrice }\r\n        }));\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching collection data:', err);\r\n      setError('Failed to load collection');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...products];\r\n\r\n    // Search filter\r\n    if (filters.search) {\r\n      filtered = filtered.filter(product =>\r\n        product.name.toLowerCase().includes(filters.search.toLowerCase()) ||\r\n        (product.description?.toLowerCase().includes(filters.search.toLowerCase()) ?? false)\r\n      );\r\n    }\r\n\r\n    // Price range filter\r\n    filtered = filtered.filter(product =>\r\n      product.price >= filters.priceRange.min && product.price <= filters.priceRange.max\r\n    );\r\n\r\n    // Stock filter\r\n    if (filters.inStockOnly) {\r\n      filtered = filtered.filter(product => product.stock > 0);\r\n    }\r\n\r\n    // Sorting\r\n    filtered.sort((a, b) => {\r\n      let comparison = 0;\r\n      \r\n      switch (filters.sortBy) {\r\n        case 'name':\r\n          comparison = a.name.localeCompare(b.name);\r\n          break;\r\n        case 'price':\r\n          comparison = a.price - b.price;\r\n          break;\r\n        case 'newest':\r\n          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\r\n          break;\r\n      }\r\n      \r\n      return filters.sortDirection === 'desc' ? -comparison : comparison;\r\n    });\r\n\r\n    setFilteredProducts(filtered);\r\n  };\r\n\r\n  const handleFilterChange = (newFilters: Partial<FilterState>) => {\r\n    setFilters(prev => ({ ...prev, ...newFilters }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      search: '',\r\n      priceRange: { min: 0, max: 10000 },\r\n      inStockOnly: false,\r\n      sortBy: 'name',\r\n      sortDirection: 'asc'\r\n    });\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={styles.loadingContainer}>\r\n        <div className={styles.loadingSpinner}></div>\r\n        <p>Loading collection...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !collection) {\r\n    return (\r\n      <div className={styles.errorContainer}>\r\n        <h1>Collection Not Found</h1>\r\n        <p>{error || 'The requested collection could not be found.'}</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={styles.collectionPage}>\r\n      <div className={styles.container}>\r\n        {/* Collection Header */}\r\n        <div className={styles.collectionHeader}>\r\n          <div className={styles.headerContent}>\r\n            <h1 className={styles.collectionTitle}>{collection.name}</h1>\r\n            <div className={styles.collectionMeta}>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter Bar */}\r\n        <div className={styles.filterSection}>\r\n          <div className={styles.searchBar}>\r\n            <div className={styles.searchInput}>\r\n              <svg className={styles.searchIcon} width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\r\n                <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n              </svg>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search products...\"\r\n                value={filters.search}\r\n                onChange={(e) => handleFilterChange({ search: e.target.value })}\r\n                className={styles.searchField}\r\n              />\r\n            </div>\r\n            \r\n            <button\r\n              className={`${styles.filterToggle} ${showFilters ? styles.active : ''}`}\r\n              onClick={() => setShowFilters(!showFilters)}\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <polygon points=\"22,3 2,3 10,12.46 10,19 14,21 14,12.46\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n              </svg>\r\n              Filters\r\n            </button>\r\n          </div>\r\n\r\n          {/* Advanced Filters */}\r\n          {showFilters && (\r\n            <div className={styles.advancedFilters}>\r\n              <div className={styles.filterGrid}>\r\n                {/* Price Range */}\r\n                <div className={styles.filterGroup}>\r\n                  <label className={styles.filterLabel}>Price Range</label>\r\n                  <div className={styles.priceRange}>\r\n                    <input\r\n                      type=\"number\"\r\n                      placeholder=\"Min\"\r\n                      value={filters.priceRange.min}\r\n                      onChange={(e) => handleFilterChange({\r\n                        priceRange: { ...filters.priceRange, min: Number(e.target.value) || 0 }\r\n                      })}\r\n                      className={styles.priceInput}\r\n                    />\r\n                    <span>to</span>\r\n                    <input\r\n                      type=\"number\"\r\n                      placeholder=\"Max\"\r\n                      value={filters.priceRange.max}\r\n                      onChange={(e) => handleFilterChange({\r\n                        priceRange: { ...filters.priceRange, max: Number(e.target.value) || 10000 }\r\n                      })}\r\n                      className={styles.priceInput}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Stock Filter */}\r\n                <div className={styles.filterGroup}>\r\n                  <label className={styles.checkboxLabel}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={filters.inStockOnly}\r\n                      onChange={(e) => handleFilterChange({ inStockOnly: e.target.checked })}\r\n                      className={styles.checkbox}\r\n                    />\r\n                    In Stock Only\r\n                  </label>\r\n                </div>\r\n\r\n                {/* Sort Options */}\r\n                <div className={styles.filterGroup}>\r\n                  <label className={styles.filterLabel}>Sort By</label>\r\n                  <div className={styles.sortControls}>\r\n                    <select\r\n                      value={filters.sortBy}\r\n                      onChange={(e) => handleFilterChange({ sortBy: e.target.value as any })}\r\n                      className={styles.sortSelect}\r\n                    >\r\n                      <option value=\"name\">Name</option>\r\n                      <option value=\"price\">Price</option>\r\n                      <option value=\"newest\">Newest</option>\r\n                    </select>\r\n                    \r\n                    <button\r\n                      className={`${styles.sortDirection} ${filters.sortDirection === 'desc' ? styles.desc : ''}`}\r\n                      onClick={() => handleFilterChange({\r\n                        sortDirection: filters.sortDirection === 'asc' ? 'desc' : 'asc'\r\n                      })}\r\n                      title={`Sort ${filters.sortDirection === 'asc' ? 'Descending' : 'Ascending'}`}\r\n                    >\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                        <path d=\"M7 10L12 15L17 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Clear Filters */}\r\n                <div className={styles.filterGroup}>\r\n                  <button onClick={clearFilters} className={styles.clearFilters}>\r\n                    Clear All Filters\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Products Grid */}\r\n        <div className={styles.productsSection}>\r\n          <ProductGrid\r\n            products={filteredProducts}\r\n            isLoading={isLoading}\r\n            showAddToCart={true}\r\n            showViewDetails={true}\r\n            emptyMessage={\r\n              filters.search || filters.inStockOnly || \r\n              filters.priceRange.min > 0 || filters.priceRange.max < 10000\r\n                ? \"No products match your current filters. Try adjusting your search criteria.\"\r\n                : \"This collection doesn't have any products yet.\"\r\n            }\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD,8CAA8C;;;;AAG9C;AACA;AAEA;AAAA;AAAA;AACA;AAAA;AACA;AAPA;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,SAAS,OAAO,EAAE;IAEvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ;QACR,YAAY;YAAE,KAAK;YAAG,KAAK;QAAM;QACjC,aAAa;QACb,QAAQ;QACR,eAAe;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YAChB;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,CAAC,gBAAgB,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACvD,8JAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC;gBAC9B,2JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,eAAe,CAAC;aACpC;YAED,cAAc;YACd,YAAY;YAEZ,mDAAmD;YACnD,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,MAAM,SAAS,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBAC5C,MAAM,WAAW,KAAK,GAAG,IAAI;gBAC7B,MAAM,WAAW,KAAK,GAAG,IAAI;gBAC7B,WAAW,CAAA,OAAQ,CAAC;wBAClB,GAAG,IAAI;wBACP,YAAY;4BAAE,KAAK;4BAAU,KAAK;wBAAS;oBAC7C,CAAC;YACH;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAS;QAE5B,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW,OAC9D,CAAC,QAAQ,WAAW,EAAE,cAAc,SAAS,QAAQ,MAAM,CAAC,WAAW,OAAO,KAAK;QAEvF;QAEA,qBAAqB;QACrB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,IAAI,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG;QAGpF,eAAe;QACf,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,GAAG;QACxD;QAEA,UAAU;QACV,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,aAAa;YAEjB,OAAQ,QAAQ,MAAM;gBACpB,KAAK;oBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACxC;gBACF,KAAK;oBACH,aAAa,EAAE,KAAK,GAAG,EAAE,KAAK;oBAC9B;gBACF,KAAK;oBACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;oBAC5E;YACJ;YAEA,OAAO,QAAQ,aAAa,KAAK,SAAS,CAAC,aAAa;QAC1D;QAEA,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,UAAU;YAAC,CAAC;IAChD;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,QAAQ;YACR,YAAY;gBAAE,KAAK;gBAAG,KAAK;YAAM;YACjC,aAAa;YACb,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,gBAAgB;;8BACrC,8OAAC;oBAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,cAAc;;;;;;8BACrC,8OAAC;8BAAE;;;;;;;;;;;;IAGT;IAEA,IAAI,SAAS,CAAC,YAAY;QACxB,qBACE,8OAAC;YAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;8BAAG,SAAS;;;;;;;;;;;;IAGnB;IAEA,qBACE,8OAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,cAAc;kBACnC,cAAA,8OAAC;YAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;8BAE9B,8OAAC;oBAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,gBAAgB;8BACrC,cAAA,8OAAC;wBAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAG,WAAW,iKAAA,CAAA,UAAM,CAAC,eAAe;0CAAG,WAAW,IAAI;;;;;;0CACvD,8OAAC;gCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;8BAMzC,8OAAC;oBAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,aAAa;;sCAClC,8OAAC;4BAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;4CAAE,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;;8DACjF,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,QAAO;oDAAe,aAAY;;;;;;8DAChE,8OAAC;oDAAK,GAAE;oDAAqB,QAAO;oDAAe,aAAY;oDAAI,eAAc;;;;;;;;;;;;sDAEnF,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,MAAM;4CACrB,UAAU,CAAC,IAAM,mBAAmB;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7D,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;8CAIjC,8OAAC;oCACC,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,cAAc,iKAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;oCACvE,SAAS,IAAM,eAAe,CAAC;;sDAE/B,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;sDACnD,cAAA,8OAAC;gDAAQ,QAAO;gDAAyC,QAAO;gDAAe,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;;;;;;;;;;wCAChI;;;;;;;;;;;;;wBAMT,6BACC,8OAAC;4BAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,eAAe;sCACpC,cAAA,8OAAC;gCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;;kDAE/B,8OAAC;wCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAM,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACtC,8OAAC;gDAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;;kEAC/B,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;wDAC7B,UAAU,CAAC,IAAM,mBAAmB;gEAClC,YAAY;oEAAE,GAAG,QAAQ,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAE;4DACxE;wDACA,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;;;;;;kEAE9B,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;wDAC7B,UAAU,CAAC,IAAM,mBAAmB;gEAClC,YAAY;oEAAE,GAAG,QAAQ,UAAU;oEAAE,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gEAAM;4DAC5E;wDACA,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;;;;;;;;;;;;;;;;;;kDAMlC,8OAAC;wCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;kDAChC,cAAA,8OAAC;4CAAM,WAAW,iKAAA,CAAA,UAAM,CAAC,aAAa;;8DACpC,8OAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,WAAW;oDAC5B,UAAU,CAAC,IAAM,mBAAmB;4DAAE,aAAa,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACpE,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;;;;;;gDAC1B;;;;;;;;;;;;kDAMN,8OAAC;wCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAM,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;0DAAE;;;;;;0DACtC,8OAAC;gDAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;;kEACjC,8OAAC;wDACC,OAAO,QAAQ,MAAM;wDACrB,UAAU,CAAC,IAAM,mBAAmB;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAAQ;wDACpE,WAAW,iKAAA,CAAA,UAAM,CAAC,UAAU;;0EAE5B,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;0EACtB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;;;;;;;kEAGzB,8OAAC;wDACC,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAAE,QAAQ,aAAa,KAAK,SAAS,iKAAA,CAAA,UAAM,CAAC,IAAI,GAAG,IAAI;wDAC3F,SAAS,IAAM,mBAAmB;gEAChC,eAAe,QAAQ,aAAa,KAAK,QAAQ,SAAS;4DAC5D;wDACA,OAAO,CAAC,KAAK,EAAE,QAAQ,aAAa,KAAK,QAAQ,eAAe,aAAa;kEAE7E,cAAA,8OAAC;4DAAI,OAAM;4DAAK,QAAO;4DAAK,SAAQ;4DAAY,MAAK;sEACnD,cAAA,8OAAC;gEAAK,GAAE;gEAAoB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO/G,8OAAC;wCAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,WAAW;kDAChC,cAAA,8OAAC;4CAAO,SAAS;4CAAc,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUzE,8OAAC;oBAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,eAAe;8BACpC,cAAA,8OAAC,sMAAA,CAAA,cAAW;wBACV,UAAU;wBACV,WAAW;wBACX,eAAe;wBACf,iBAAiB;wBACjB,cACE,QAAQ,MAAM,IAAI,QAAQ,WAAW,IACrC,QAAQ,UAAU,CAAC,GAAG,GAAG,KAAK,QAAQ,UAAU,CAAC,GAAG,GAAG,QACnD,gFACA;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}