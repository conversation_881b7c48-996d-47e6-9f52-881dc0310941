/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-y-reverse: 0;
      --tw-border-style: solid;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

@layer theme {
  :root, :host {
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-amber-50: oklch(98.7% .022 95.277);
    --color-amber-100: oklch(96.2% .059 95.617);
    --color-amber-200: oklch(92.4% .12 95.746);
    --color-amber-300: oklch(87.9% .169 91.605);
    --color-amber-400: oklch(82.8% .189 84.429);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-700: oklch(55.5% .163 48.998);
    --color-amber-800: oklch(47.3% .137 46.201);
    --color-amber-900: oklch(41.4% .112 45.904);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-wider: .05em;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .top-20 {
    top: calc(var(--spacing) * 20);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-full {
    height: 100%;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-amber-200 > :not(:last-child)) {
    border-color: var(--color-amber-200);
  }

  :where(.divide-black > :not(:last-child)) {
    border-color: var(--color-black);
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }

  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }

  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }

  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-2 {
    border-right-style: var(--tw-border-style);
    border-right-width: 2px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-b-4 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-amber-200 {
    border-color: var(--color-amber-200);
  }

  .border-amber-300 {
    border-color: var(--color-amber-300);
  }

  .border-amber-900 {
    border-color: var(--color-amber-900);
  }

  .border-black {
    border-color: var(--color-black);
  }

  .border-blue-300 {
    border-color: var(--color-blue-300);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-300 {
    border-color: var(--color-red-300);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }

  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }

  .bg-amber-900 {
    background-color: var(--color-amber-900);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .object-cover {
    object-fit: cover;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-line {
    white-space: pre-line;
  }

  .text-amber-600 {
    color: var(--color-amber-600);
  }

  .text-amber-700 {
    color: var(--color-amber-700);
  }

  .text-amber-800 {
    color: var(--color-amber-800);
  }

  .text-amber-900 {
    color: var(--color-amber-900);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-amber-400::placeholder {
    color: var(--color-amber-400);
  }

  .placeholder-black::placeholder {
    color: var(--color-black);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-amber-400:hover {
      border-color: var(--color-amber-400);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-50:hover {
      background-color: var(--color-amber-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-200:hover {
      background-color: var(--color-amber-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-amber-800:hover {
      background-color: var(--color-amber-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-black:hover {
      background-color: var(--color-black);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-50:hover {
      background-color: var(--color-red-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-700:hover {
      background-color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-800:hover {
      color: var(--color-amber-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-amber-900:hover {
      color: var(--color-amber-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-black:hover {
      color: var(--color-black);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-900:hover {
      color: var(--color-blue-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-800:hover {
      color: var(--color-green-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-900:hover {
      color: var(--color-green-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-800:hover {
      color: var(--color-red-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-900:hover {
      color: var(--color-red-900);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:border-amber-500:focus {
    border-color: var(--color-amber-500);
  }

  .focus\:border-black:focus {
    border-color: var(--color-black);
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-amber-500:focus {
    --tw-ring-color: var(--color-amber-500);
  }

  .focus\:ring-black:focus {
    --tw-ring-color: var(--color-black);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:static {
      position: static;
    }
  }

  @media (width >= 64rem) {
    .lg\:inset-0 {
      inset: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:translate-x-0 {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
}

:root {
  --background: #fff;
  --foreground: #fff;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #fff;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}


/* [project]/src/components/shared/Header/header.module.css [app-client] (css) */
.header-module__zhIT0W__Headerroot {
  --cast-stone-brown: #4a3728;
  --cast-stone-light-brown: #6b4e3d;
  --cast-stone-cream: #faf9f7;
  --cast-stone-white: #fff;
  --cast-stone-gray: #8b7355;
  --cast-stone-shadow: #4a37281a;
  --cast-stone-shadow-hover: #4a372826;
  --transition-smooth: all .3s cubic-bezier(.4, 0, .2, 1);
  --transition-fast: all .2s cubic-bezier(.4, 0, .2, 1);
}

.header-module__zhIT0W__header {
  backdrop-filter: none;
  z-index: 1000;
  background: none;
  border: none;
  padding: 0;
  transition: all .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.header-module__zhIT0W__header.header-module__zhIT0W__scrolled {
  backdrop-filter: none;
  background: none;
}

.header-module__zhIT0W__container {
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  height: 70px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
}

.header-module__zhIT0W__logo {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.header-module__zhIT0W__logoLink {
  color: #fffffff2;
  text-shadow: 2px 2px 4px #000000b3;
  flex-direction: column;
  align-items: flex-start;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__logoLink {
  color: #fffffff2;
  text-shadow: 2px 2px 4px #000000b3;
}

.header-module__zhIT0W__logoLink:hover {
  transform: translateY(-1px);
}

.header-module__zhIT0W__logoText {
  letter-spacing: .1em;
  text-transform: uppercase;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1;
}

.header-module__zhIT0W__logoSubtext {
  letter-spacing: .1em;
  text-transform: uppercase;
  color: var(--cast-stone-gray);
  margin-top: 2px;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 400;
}

.header-module__zhIT0W__nav {
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__navList {
  align-items: center;
  gap: 0;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.header-module__zhIT0W__navItem {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
  color: #ffffffe6;
  cursor: pointer;
  letter-spacing: .05em;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px #000000b3;
  background: none;
  border: none;
  align-items: center;
  gap: .5rem;
  padding: 1rem 1.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton {
  color: #ffffffe6;
  text-shadow: 2px 2px 4px #000000b3;
}

.header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__navButton:hover {
  color: #fff;
  text-shadow: 2px 2px 6px #000c;
  transform: translateY(-1px);
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton:hover {
  color: #fff;
  text-shadow: 2px 2px 6px #000c;
}

.header-module__zhIT0W__navLink:after, .header-module__zhIT0W__navButton:after {
  content: "";
  background: #ffffffe6;
  width: 0;
  height: 2px;
  transition: all .3s;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 4px #00000080;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink:after, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton:after {
  background: #ffffffe6;
  box-shadow: 0 0 4px #00000080;
}

.header-module__zhIT0W__navLink:hover:after, .header-module__zhIT0W__navButton:hover:after, .header-module__zhIT0W__navButton.header-module__zhIT0W__active:after {
  width: 80%;
}

.header-module__zhIT0W__dropdownContainer {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__dropdownIcon {
  transition: var(--transition-smooth);
  color: #ffffffb3;
  text-shadow: 1px 1px 2px #00000080;
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdownIcon.header-module__zhIT0W__rotated {
  color: #ffffffe6;
  transform: rotate(180deg);
}

.header-module__zhIT0W__loadingIcon {
  color: #ffffffb3;
  text-shadow: 1px 1px 2px #00000080;
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdown {
  background: var(--cast-stone-white);
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  min-width: 280px;
  transition: var(--transition-smooth);
  backdrop-filter: blur(10px);
  border: 1px solid #4a37281a;
  border-radius: 8px;
  animation: .3s cubic-bezier(.4, 0, .2, 1) forwards header-module__zhIT0W__dropdownSlideIn;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%)translateY(-10px);
}

@keyframes header-module__zhIT0W__dropdownSlideIn {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%)translateY(-10px);
  }

  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%)translateY(0);
  }
}

.header-module__zhIT0W__dropdownList {
  margin: 0;
  padding: .5rem 0;
  list-style: none;
}

.header-module__zhIT0W__dropdownItem {
  position: relative;
}

.header-module__zhIT0W__dropdownLink {
  color: var(--cast-stone-brown);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .75rem 1.25rem;
  font-size: .9rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
}

.header-module__zhIT0W__dropdownLink:hover {
  color: var(--cast-stone-light-brown);
  border-left-color: var(--cast-stone-brown);
  background: #4a37280a;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownList {
  background: #4a372805;
  border-top: 1px solid #4a372814;
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-module__zhIT0W__subDropdownItem {
  position: relative;
}

.header-module__zhIT0W__subDropdownLink {
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .6rem 1.25rem .6rem 2rem;
  font-size: .85rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
  position: relative;
}

.header-module__zhIT0W__subDropdownLink:before {
  content: "→  ";
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  font-size: .7rem;
  position: absolute;
  left: .75rem;
}

.header-module__zhIT0W__subDropdownLink:hover {
  color: var(--cast-stone-brown);
  border-left-color: var(--cast-stone-light-brown);
  background: #4a37280f;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownLink:hover:before {
  color: var(--cast-stone-brown);
  transform: translateX(2px);
}

.header-module__zhIT0W__subSubDropdownList {
  background: #4a37280a;
  border-top: 1px solid #4a37281f;
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-module__zhIT0W__subSubDropdownItem {
  position: relative;
}

.header-module__zhIT0W__subSubDropdownLink {
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  border-left: 6px solid #0000;
  padding: .5rem 1.25rem .5rem 2.5rem;
  font-size: .8rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
  position: relative;
}

.header-module__zhIT0W__subSubDropdownLink:before {
  content: "⤷  ";
  color: var(--cast-stone-gray);
  transition: var(--transition-fast);
  font-size: .65rem;
  position: absolute;
  left: 1.5rem;
}

.header-module__zhIT0W__subSubDropdownLink:hover {
  color: var(--cast-stone-brown);
  border-left-color: var(--cast-stone-light-brown);
  background: #4a372814;
  transform: translateX(2px);
}

.header-module__zhIT0W__subSubDropdownLink:hover:before {
  color: var(--cast-stone-brown);
  transform: translateX(2px);
}

.header-module__zhIT0W__cartContainer {
  align-items: center;
  margin-left: 1rem;
  display: flex;
}

.header-module__zhIT0W__cartLink {
  width: 44px;
  height: 44px;
  color: var(--cast-stone-brown);
  transition: var(--transition-smooth);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__cartLink:hover {
  color: var(--cast-stone-light-brown);
  background: #4a37280f;
  transform: translateY(-1px);
}

.header-module__zhIT0W__cartIconWrapper {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__cartBadge {
  color: #fff;
  background: #dc2626;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  height: 20px;
  padding: 0 4px;
  font-size: .75rem;
  font-weight: 600;
  animation: .3s ease-out header-module__zhIT0W__cartBadgeAppear;
  display: flex;
  position: absolute;
  top: -8px;
  right: -8px;
  box-shadow: 0 2px 4px #0003;
}

@keyframes header-module__zhIT0W__cartBadgeAppear {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media (width <= 1024px) {
  .header-module__zhIT0W__container {
    padding: 0 1.5rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.5rem 1rem;
    font-size: .9rem;
  }
}

@media (width <= 768px) {
  .header-module__zhIT0W__container {
    height: 70px;
    padding: 0 1rem;
  }

  .header-module__zhIT0W__logoText {
    font-size: 1.75rem;
  }

  .header-module__zhIT0W__logoSubtext {
    font-size: .7rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.25rem .75rem;
    font-size: .85rem;
  }

  .header-module__zhIT0W__dropdown {
    min-width: 200px;
  }
}

@media (width <= 640px) {
  .header-module__zhIT0W__nav {
    display: none;
  }

  .header-module__zhIT0W__container {
    justify-content: space-between;
  }
}


/* [project]/src/components/shared/Footer/footer.module.css [app-client] (css) */
.footer-module__94Lf8a__footer {
  color: #fff;
  background: #1e40af;
  padding: 4rem 0 2rem;
  position: relative;
}

.footer-module__94Lf8a__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-module__94Lf8a__content {
  border-bottom: 1px solid #ffffff1a;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  display: grid;
}

.footer-module__94Lf8a__brand {
  max-width: 400px;
}

.footer-module__94Lf8a__brandName {
  color: #white;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
}

.footer-module__94Lf8a__brandDescription {
  color: #fffc;
  margin-bottom: 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.footer-module__94Lf8a__socialLinks {
  gap: 1rem;
  display: flex;
}

.footer-module__94Lf8a__socialLink {
  width: 40px;
  height: 40px;
  color: #white;
  background: #ffffff1a;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
}

.footer-module__94Lf8a__socialLink:hover {
  background: #white;
  color: #4a3728;
  transform: translateY(-2px);
}

.footer-module__94Lf8a__linkGroup {
  flex-direction: column;
  display: flex;
}

.footer-module__94Lf8a__linkGroupTitle {
  color: #white;
  letter-spacing: -.01em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
}

.footer-module__94Lf8a__linkList {
  flex-direction: column;
  gap: .75rem;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.footer-module__94Lf8a__link {
  color: #fffc;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 400;
  line-height: 1.4;
  text-decoration: none;
  transition: all .3s;
}

.footer-module__94Lf8a__link:hover {
  color: #white;
  transform: translateX(4px);
}

.footer-module__94Lf8a__contactInfo {
  border-top: 1px solid #ffffff1a;
  grid-column: 1 / -1;
  margin-top: 2rem;
  padding-top: 2rem;
}

.footer-module__94Lf8a__contactTitle {
  color: #white;
  letter-spacing: -.01em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
}

.footer-module__94Lf8a__contactDetails {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  display: grid;
}

.footer-module__94Lf8a__contactItem {
  flex-direction: column;
  gap: .25rem;
  display: flex;
}

.footer-module__94Lf8a__contactLabel {
  color: #white;
  text-transform: uppercase;
  letter-spacing: .05em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 600;
}

.footer-module__94Lf8a__contactValue {
  color: #ffffffe6;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 400;
  line-height: 1.4;
}

.footer-module__94Lf8a__bottom {
  border-top: 1px solid #ffffff1a;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  display: flex;
}

.footer-module__94Lf8a__copyright {
  color: #ffffffb3;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
}

.footer-module__94Lf8a__legalLinks {
  gap: 2rem;
  display: flex;
}

.footer-module__94Lf8a__legalLink {
  color: #ffffffb3;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
  text-decoration: none;
  transition: color .3s;
}

.footer-module__94Lf8a__legalLink:hover {
  color: #white;
}

@media (width <= 1024px) {
  .footer-module__94Lf8a__footer {
    padding: 3rem 0 1.5rem;
  }

  .footer-module__94Lf8a__container {
    padding: 0 1.5rem;
  }

  .footer-module__94Lf8a__content {
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
  }

  .footer-module__94Lf8a__brand {
    text-align: center;
    grid-column: 1 / -1;
    max-width: none;
    margin-bottom: 1rem;
  }

  .footer-module__94Lf8a__socialLinks {
    justify-content: center;
  }
}

@media (width <= 768px) {
  .footer-module__94Lf8a__footer {
    padding: 2.5rem 0 1rem;
  }

  .footer-module__94Lf8a__container {
    padding: 0 1rem;
  }

  .footer-module__94Lf8a__content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-module__94Lf8a__brand {
    margin-bottom: 1.5rem;
  }

  .footer-module__94Lf8a__brandName {
    font-size: 1.75rem;
  }

  .footer-module__94Lf8a__brandDescription {
    font-size: .9rem;
  }

  .footer-module__94Lf8a__linkGroup {
    align-items: center;
  }

  .footer-module__94Lf8a__linkGroupTitle {
    margin-bottom: 1rem;
    font-size: 1.125rem;
  }

  .footer-module__94Lf8a__contactInfo {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .footer-module__94Lf8a__contactDetails {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .footer-module__94Lf8a__bottom {
    text-align: center;
    flex-direction: column;
    gap: 1rem;
  }

  .footer-module__94Lf8a__legalLinks {
    gap: 1rem;
  }
}

@media (width <= 480px) {
  .footer-module__94Lf8a__footer {
    padding: 2rem 0 1rem;
  }

  .footer-module__94Lf8a__brandName {
    font-size: 1.5rem;
  }

  .footer-module__94Lf8a__brandDescription {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__socialLinks {
    gap: .75rem;
  }

  .footer-module__94Lf8a__socialLink {
    width: 36px;
    height: 36px;
  }

  .footer-module__94Lf8a__linkGroupTitle {
    font-size: 1rem;
  }

  .footer-module__94Lf8a__link {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__contactTitle {
    font-size: 1rem;
  }

  .footer-module__94Lf8a__contactLabel {
    font-size: .8rem;
  }

  .footer-module__94Lf8a__contactValue {
    font-size: .85rem;
  }

  .footer-module__94Lf8a__legalLinks {
    flex-direction: column;
    gap: .5rem;
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__87a6e4c6._.css.map*/