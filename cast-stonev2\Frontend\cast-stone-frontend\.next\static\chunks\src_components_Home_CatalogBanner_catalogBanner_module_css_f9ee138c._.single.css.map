{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CatalogBanner/catalogBanner.module.css"], "sourcesContent": ["/* Catalog Banner Styles */\r\n.catalogBanner {\r\n  position: relative;\r\n  padding: 8rem 0;\r\n  background: #1e3a8a;\r\n  overflow: hidden;\r\n  margin: 4rem 0;\r\n}\r\n\r\n/* Background Styles */\r\n.backgroundContainer {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.backgroundImage {\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n.backgroundOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 2;\r\n}\r\n\r\n/* Content Styles */\r\n.container {\r\n  position: relative;\r\n  z-index: 3;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.textContent {\r\n  color: #ffffff;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  display: block;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3.5rem;\r\n  font-weight: 700;\r\n  line-height: 1.1;\r\n  color: #ffffff;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.description {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 2.5rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Features */\r\n.features {\r\n  display: flex;\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.feature {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.featureIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(212, 175, 140, 0.2);\r\n  border-radius: 50%;\r\n  color: #white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* CTA Section */\r\n.ctaContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 2rem;\r\n}\r\n\r\n.ctaButton {\r\n  position: relative;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.25rem 2.5rem;\r\n  background: linear-gradient(135deg, #white,rgb(245, 244, 243));\r\n  color: #white;\r\n  text-decoration: none;\r\n  border-radius: 50px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 32px rgba(142, 139, 137, 0.3);\r\n}\r\n\r\n.ctaButton:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 40px rgba(212, 175, 140, 0.4);\r\n  background: linear-gradient(135deg, #white, #white);\r\n}\r\n\r\n.ctaText {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.ctaIcon {\r\n  position: relative;\r\n  z-index: 2;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.ctaButton:hover .ctaIcon {\r\n  transform: translateX(4px);\r\n}\r\n\r\n.buttonRipple {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\r\n  transform: scale(0);\r\n  transition: transform 0.6s ease-out;\r\n  z-index: 1;\r\n}\r\n\r\n.ctaButton:active .buttonRipple {\r\n  transform: scale(1);\r\n}\r\n\r\n/* Catalog Stats */\r\n.catalogStats {\r\n  display: flex;\r\n  gap: 3rem;\r\n}\r\n\r\n.stat {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.statNumber {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  line-height: 1;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.statLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 500;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Decorative Elements */\r\n.decorativeElements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n  pointer-events: none;\r\n}\r\n\r\n.decorativeCircle {\r\n  position: absolute;\r\n  top: 20%;\r\n  right: 10%;\r\n  width: 200px;\r\n  height: 200px;\r\n  border: 2px solid rgba(212, 175, 140, 0.2);\r\n  border-radius: 50%;\r\n  animation: float 6s ease-in-out infinite;\r\n}\r\n\r\n.decorativeLine {\r\n  position: absolute;\r\n  bottom: 20%;\r\n  left: 5%;\r\n  width: 150px;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);\r\n  animation: slide 8s ease-in-out infinite;\r\n}\r\n\r\n/* Animations */\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n@keyframes slide {\r\n  0%, 100% {\r\n    transform: translateX(0px);\r\n  }\r\n  50% {\r\n    transform: translateX(50px);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .catalogBanner {\r\n    padding: 6rem 0;\r\n  }\r\n  \r\n  .content {\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 3rem;\r\n  }\r\n  \r\n  .features {\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .catalogBanner {\r\n    padding: 4rem 0;\r\n    margin: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .content {\r\n    grid-template-columns: 1fr;\r\n    gap: 2.5rem;\r\n    text-align: center;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .description {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .features {\r\n    justify-content: center;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .ctaContainer {\r\n    align-items: center;\r\n  }\r\n  \r\n  .catalogStats {\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .catalogBanner {\r\n    padding: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .features {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .feature {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .ctaButton {\r\n    padding: 1rem 2rem;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .catalogStats {\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .statNumber {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;AASA;;;;;;AASA;;;;;AAKA;;;;;;;AAgBA;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA"}}]}