{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/cart/cart.module.css"], "sourcesContent": ["/* Cart Page Styles - Magazine/Editorial Theme */\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 2rem 1rem;\r\n  min-height: 80vh;\r\n}\r\n\r\n/* Header */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 2rem;\r\n  border-bottom: 2px solid #f3f4f6;\r\n}\r\n\r\n.title {\r\n  color: #1f2937;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.subtitle {\r\n  color: #4b5563;\r\n  font-size: 1.1rem;\r\n  margin: 0;\r\n}\r\n\r\n/* Loading State */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  color: #4b5563;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f4f6;\r\n  border-top: 3px solid #2563eb;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Empty Cart */\r\n.emptyCart {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 400px;\r\n  padding: 2rem;\r\n}\r\n\r\n.emptyIcon {\r\n  width: 120px;\r\n  height: 120px;\r\n  color: #d1d5db;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.emptyIcon svg {\r\n  width: 100%;\r\n  height: 100%;\r\n  stroke-width: 1.5;\r\n}\r\n\r\n.emptyTitle {\r\n  color: #1f2937;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.emptyMessage {\r\n  color: #4b5563;\r\n  font-size: 1.1rem;\r\n  line-height: 1.6;\r\n  margin: 0 0 2rem 0;\r\n  max-width: 500px;\r\n}\r\n\r\n.shopNowBtn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 2rem;\r\n  background: #2563eb;\r\n  color: white;\r\n  border-radius: 6px;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.shopNowBtn:hover {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);\r\n}\r\n\r\n.shopIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Error Message */\r\n.errorMessage {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  background: #fef2f2;\r\n  border: 1px solid #fecaca;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 2rem;\r\n  color: #dc2626;\r\n}\r\n\r\n.errorIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Cart Content */\r\n.cartContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 350px;\r\n  gap: 3rem;\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n/* Cart Items */\r\n.cartItems {\r\n  min-width: 0; /* Allow content to shrink */\r\n}\r\n\r\n.itemsHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1.5rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.itemsHeader h2 {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0;\r\n}\r\n\r\n.continueShoppingLink {\r\n  color: #2563eb;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.continueShoppingLink:hover {\r\n  color: #1d4ed8;\r\n  text-decoration: underline;\r\n}\r\n\r\n.itemsList {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n/* Cart Summary Container */\r\n.cartSummaryContainer {\r\n  /* Styles handled by CartSummary component */\r\n}\r\n\r\n/* Additional Actions */\r\n.additionalActions {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 2rem;\r\n  padding-top: 2rem;\r\n  border-top: 2px solid #f3f4f6;\r\n}\r\n\r\n.helpSection,\r\n.shippingInfo {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n}\r\n\r\n.helpSection h3,\r\n.shippingInfo h3 {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.helpSection p {\r\n  color: #4b5563;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.contactLink {\r\n  color: #2563eb;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n}\r\n\r\n.contactLink:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.shippingInfo ul {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.shippingInfo li {\r\n  color: #4b5563;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.shippingInfo li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.shippingInfo li::before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #059669;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .cartContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header {\r\n    margin-bottom: 2rem;\r\n    padding-bottom: 1.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .cartContent {\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .additionalActions {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .helpSection,\r\n  .shippingInfo {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .emptyIcon {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .emptyTitle {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .emptyMessage {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .itemsHeader {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .shopNowBtn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAYA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;EACE;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA"}}]}