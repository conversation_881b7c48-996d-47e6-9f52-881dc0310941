{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/heroSection.module.css"], "sourcesContent": ["/* Hero Section Styles */\r\n.hero {\r\n  position: relative;\r\n  height: 100vh;\r\n  min-height: 700px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  background-color: #1a1a1a; /* Fallback color */\r\n}\r\n\r\n/* Image Background Carousel */\r\n.imageContainer {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.imageSlide {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  opacity: 0;\r\n  transition: opacity 1.5s ease-in-out;\r\n  transform: scale(1);\r\n  will-change: transform, opacity;\r\n}\r\n\r\n.imageSlide.active {\r\n  opacity: 1;\r\n  animation: smoothZoom 5s ease-out forwards;\r\n}\r\n\r\n.imageSlide:not(.active) {\r\n  animation: none;\r\n  transform: scale(1.06); /* Keep zoomed state when inactive */\r\n}\r\n\r\n/* Smooth zoom animation without reset */\r\n@keyframes smoothZoom {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  100% {\r\n    transform: scale(1.06);\r\n  }\r\n}\r\n\r\n.backgroundImage {\r\n  object-fit: cover;\r\n  object-position: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(\r\n    135deg,\r\n    rgba(0, 0, 0, 0.3) 0%,\r\n    rgba(0, 0, 0, 0.2) 50%,\r\n    rgba(0, 0, 0, 0.4) 100%\r\n  );\r\n  z-index: 2;\r\n}\r\n\r\n/* Content Styles */\r\n.container {\r\n  position: relative;\r\n  z-index: 3;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.content {\r\n  max-width: 900px;\r\n  margin: 0 auto;\r\n  padding-top: 2rem;\r\n}\r\n\r\n.title {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 4.5rem;\r\n  font-weight: 300;\r\n  line-height: 1.1;\r\n  margin-bottom: 2rem;\r\n  color: #ffffff;\r\n  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);\r\n  letter-spacing: 0.02em;\r\n}\r\n\r\n.titleLine1,\r\n.titleLine2 {\r\n  display: block;\r\n  animation: fadeInUp 1s ease-out forwards;\r\n}\r\n\r\n.titleLine1 {\r\n  animation-delay: 0.3s;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.titleLine2 {\r\n  animation-delay: 0.6s;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 300;\r\n  line-height: 1.7;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 3rem;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);\r\n  animation: fadeInUp 1s ease-out 0.9s forwards;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n/* Button Styles */\r\n.actions {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  animation: fadeInUp 1s ease-out 1.2s forwards;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.primaryButton,\r\n.secondaryButton {\r\n  position: relative;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 1.2rem 3rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  letter-spacing: 0.15em;\r\n  text-transform: uppercase;\r\n  text-decoration: none;\r\n  border-radius: 0;\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  border: 2px solid transparent;\r\n  min-width: 220px;\r\n}\r\n\r\n.primaryButton {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: rgba(0, 0, 0, 0.8);\r\n  border-color: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.primaryButton:hover {\r\n  background: rgba(255, 255, 255, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.secondaryButton {\r\n  background: transparent;\r\n  color: #ffffff;\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.secondaryButton:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 1);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.buttonText {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.buttonRipple {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\r\n  transform: scale(0);\r\n  transition: transform 0.6s ease-out;\r\n  z-index: 1;\r\n}\r\n\r\n.primaryButton:active .buttonRipple,\r\n.secondaryButton:active .buttonRipple {\r\n  transform: scale(1);\r\n}\r\n\r\n/* Scroll Indicator */\r\n.scrollIndicator {\r\n  position: absolute;\r\n  bottom: 2rem;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 3;\r\n  animation: fadeInUp 1s ease-out 1.5s forwards;\r\n  opacity: 0;\r\n}\r\n\r\n.scrollArrow {\r\n  color: #ffffff;\r\n  animation: bounce 2s infinite;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.scrollArrow:hover {\r\n  color: #d4af8c;\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-10px);\r\n  }\r\n  60% {\r\n    transform: translateY(-5px);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .title {\r\n    font-size: 3.5rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1rem;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .primaryButton,\r\n  .secondaryButton {\r\n    width: 100%;\r\n    max-width: 280px;\r\n    padding: 0.875rem 2rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero {\r\n    min-height: 500px;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 0.9rem;\r\n    margin-bottom: 1.5rem;\r\n  }\r\n\r\n  .primaryButton,\r\n  .secondaryButton {\r\n    font-size: 0.8rem;\r\n    padding: 0.75rem 1.5rem;\r\n  }\r\n}\r\n\r\n/* Navigation Arrows */\r\n.navArrow {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  /* background: rgba(255, 255, 255, 0.1); */\r\n  /* backdrop-filter: blur(10px);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 0%; */\r\n  width: 60px;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  z-index: 4;\r\n  opacity: 0.7;\r\n}\r\n\r\n.navArrow:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  color: rgba(255, 255, 255, 1);\r\n  opacity: 1;\r\n  transform: translateY(-50%) scale(1.1);\r\n}\r\n\r\n.navArrowLeft {\r\n  left: 2rem;\r\n}\r\n\r\n.navArrowRight {\r\n  right: 2rem;\r\n}\r\n\r\n/* Indicator Dots */\r\n.indicators {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n  margin-top: 3rem;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.indicator {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n  background: transparent;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.indicator:hover {\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  transform: scale(1.2);\r\n}\r\n\r\n.indicatorActive {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-color: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* Mobile responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .hero {\r\n    min-height: 500px;\r\n  }\r\n\r\n  .navArrow {\r\n    width: 50px;\r\n    height: 50px;\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .navArrowLeft {\r\n    left: 1rem;\r\n  }\r\n\r\n  .navArrowRight {\r\n    right: 1rem;\r\n  }\r\n\r\n  .indicators {\r\n    margin-top: 2rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .indicator {\r\n    width: 10px;\r\n    height: 10px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAMA;;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAQF;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAQA;;;;AAIA;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAOA;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CategoriesSection/categoriesSection.module.css"], "sourcesContent": ["/* Categories Section Styles */\r\n.categoriesSection {\r\n  padding: 6rem 0;\r\n  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);\r\n  position: relative;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.sectionTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.sectionSubtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  color:rgb(54, 76, 137);\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Grid Layout */\r\n.grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* Loading States */\r\n.loadingGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.loadingCard {\r\n  background: #ffffff;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n.loadingImage {\r\n  width: 100%;\r\n  height: 300px;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n}\r\n\r\n.loadingContent {\r\n  padding: 2rem;\r\n}\r\n\r\n.loadingText {\r\n  height: 1rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.loadingText:nth-child(1) {\r\n  width: 60%;\r\n}\r\n\r\n.loadingText:nth-child(2) {\r\n  width: 80%;\r\n}\r\n\r\n.loadingText:nth-child(3) {\r\n  width: 40%;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Error State */\r\n.errorMessage {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #6b7280;\r\n  font-size: 1.125rem;\r\n}\r\n\r\n/* Category Card Styles */\r\n.categoryCard {\r\n  position: relative;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  box-shadow: 0 8px 32px rgba(74, 55, 40, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  height: 400px;\r\n}\r\n\r\n.categoryCard:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 60px rgba(110, 104, 99, 0.15);\r\n}\r\n\r\n.cardLink {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  text-decoration: none;\r\n  color: inherit;\r\n  position: relative;\r\n}\r\n\r\n/* Image Styles */\r\n.imageContainer {\r\n  position: relative;\r\n  height: 60%;\r\n  overflow: hidden;\r\n}\r\n\r\n.categoryImage {\r\n  object-fit: cover;\r\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.categoryCard:hover .categoryImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 1;\r\n}\r\n\r\n.noImagePlaceholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #1e0bac 0%, #4a3728 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.noImageText {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Card Content */\r\n.cardContent {\r\n  position: relative;\r\n  height: 40%;\r\n  padding: 1.5rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  z-index: 2;\r\n}\r\n\r\n.cardHeader {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.stats {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: 0.5rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.statsNumber {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  line-height: 1;\r\n}\r\n\r\n.statsLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  color: #1e3a8a;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n.categoryTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #1e3a8a;\r\n  margin: 0.25rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.categorySubtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 600;\r\n  color:rgb(48, 69, 126);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  margin: 0;\r\n}\r\n\r\n.categoryDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #1e3a8a;\r\n  line-height: 1.5;\r\n  margin-bottom: 1rem;\r\n  flex-grow: 1;\r\n}\r\n\r\n/* Card Actions */\r\n.cardActions {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  align-items: center;\r\n}\r\n\r\n.actionButton,\r\n.secondaryButton {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.actionButton {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  padding: 0.5rem 1rem;\r\n}\r\n\r\n.actionButton:hover {\r\n  background: #1e3a8a;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.secondaryButton {\r\n  background: transparent;\r\n  color:rgb(51, 73, 134);\r\n  padding: 0.5rem 0.75rem;\r\n  border: 1px solid rgba(105, 120, 161, 0.3);\r\n}\r\n\r\n.secondaryButton:hover {\r\n  background: rgba(69, 66, 153, 0.1);\r\n  color: #1e3a8a;\r\n}\r\n\r\n/* Hover Effect */\r\n.hoverEffect {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\n    rgba(74, 55, 40, 0.05) 0%,\r\n    transparent 50%,\r\n    rgba(139, 115, 85, 0.05) 100%\r\n  );\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  z-index: 1;\r\n}\r\n\r\n.categoryCard:hover .hoverEffect {\r\n  opacity: 1;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .categoriesSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .grid {\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 350px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 300px;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .sectionSubtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.25rem;\r\n  }\r\n  \r\n  .statsNumber {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .categoryTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .categoriesSection {\r\n    padding: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 280px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .cardActions {\r\n    flex-direction: column;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .actionButton,\r\n  .secondaryButton {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AAiBA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;AAeA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CatalogBanner/catalogBanner.module.css"], "sourcesContent": ["/* Catalog Banner Styles */\r\n.catalogBanner {\r\n  position: relative;\r\n  padding: 8rem 0;\r\n  background: #1e3a8a;\r\n  overflow: hidden;\r\n  margin: 4rem 0;\r\n}\r\n\r\n/* Background Styles */\r\n.backgroundContainer {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1;\r\n}\r\n\r\n.backgroundImage {\r\n  object-fit: cover;\r\n  object-position: center;\r\n}\r\n\r\n.backgroundOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 2;\r\n}\r\n\r\n/* Content Styles */\r\n.container {\r\n  position: relative;\r\n  z-index: 3;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.content {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.textContent {\r\n  color: #ffffff;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  display: block;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3.5rem;\r\n  font-weight: 700;\r\n  line-height: 1.1;\r\n  color: #ffffff;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.description {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 2.5rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Features */\r\n.features {\r\n  display: flex;\r\n  gap: 2rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.feature {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.featureIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(212, 175, 140, 0.2);\r\n  border-radius: 50%;\r\n  color: #white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* CTA Section */\r\n.ctaContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 2rem;\r\n}\r\n\r\n.ctaButton {\r\n  position: relative;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.25rem 2.5rem;\r\n  background: linear-gradient(135deg, #white,rgb(245, 244, 243));\r\n  color: #white;\r\n  text-decoration: none;\r\n  border-radius: 50px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 32px rgba(142, 139, 137, 0.3);\r\n}\r\n\r\n.ctaButton:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 12px 40px rgba(212, 175, 140, 0.4);\r\n  background: linear-gradient(135deg, #white, #white);\r\n}\r\n\r\n.ctaText {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.ctaIcon {\r\n  position: relative;\r\n  z-index: 2;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.ctaButton:hover .ctaIcon {\r\n  transform: translateX(4px);\r\n}\r\n\r\n.buttonRipple {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\r\n  transform: scale(0);\r\n  transition: transform 0.6s ease-out;\r\n  z-index: 1;\r\n}\r\n\r\n.ctaButton:active .buttonRipple {\r\n  transform: scale(1);\r\n}\r\n\r\n/* Catalog Stats */\r\n.catalogStats {\r\n  display: flex;\r\n  gap: 3rem;\r\n}\r\n\r\n.stat {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.statNumber {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  line-height: 1;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.statLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 500;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Decorative Elements */\r\n.decorativeElements {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 2;\r\n  pointer-events: none;\r\n}\r\n\r\n.decorativeCircle {\r\n  position: absolute;\r\n  top: 20%;\r\n  right: 10%;\r\n  width: 200px;\r\n  height: 200px;\r\n  border: 2px solid rgba(212, 175, 140, 0.2);\r\n  border-radius: 50%;\r\n  animation: float 6s ease-in-out infinite;\r\n}\r\n\r\n.decorativeLine {\r\n  position: absolute;\r\n  bottom: 20%;\r\n  left: 5%;\r\n  width: 150px;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, transparent, rgba(212, 175, 140, 0.3), transparent);\r\n  animation: slide 8s ease-in-out infinite;\r\n}\r\n\r\n/* Animations */\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n@keyframes slide {\r\n  0%, 100% {\r\n    transform: translateX(0px);\r\n  }\r\n  50% {\r\n    transform: translateX(50px);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .catalogBanner {\r\n    padding: 6rem 0;\r\n  }\r\n  \r\n  .content {\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 3rem;\r\n  }\r\n  \r\n  .features {\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .catalogBanner {\r\n    padding: 4rem 0;\r\n    margin: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .content {\r\n    grid-template-columns: 1fr;\r\n    gap: 2.5rem;\r\n    text-align: center;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .description {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .features {\r\n    justify-content: center;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .ctaContainer {\r\n    align-items: center;\r\n  }\r\n  \r\n  .catalogStats {\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .catalogBanner {\r\n    padding: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .features {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .feature {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .ctaButton {\r\n    padding: 1rem 2rem;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .catalogStats {\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .statNumber {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;AASA;;;;;;AASA;;;;;AAKA;;;;;;;AAgBA;;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;AAaA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css"], "sourcesContent": ["/* Collections Carousel Styles */\r\n.collectionsSection {\r\n  padding: 6rem 0;\r\n  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  margin-bottom: 3rem;\r\n  gap: 2rem;\r\n}\r\n\r\n.headerContent {\r\n  flex: 1;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  color: #1e40af;\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n  max-width: 600px;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.navButton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 48px;\r\n  height: 48px;\r\n  background: #ffffff;\r\n  border: 2px solid rgba(74, 55, 40, 0.1);\r\n  border-radius: 50%;\r\n  color: #1e3a8a;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 4px 12px rgba(74, 55, 40, 0.1);\r\n}\r\n\r\n.navButton:hover {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  border-color: #1e3a8a;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 20px rgba(74, 55, 40, 0.2);\r\n}\r\n\r\n/* Carousel Container */\r\n.carouselContainer {\r\n  position: relative;\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n.carousel {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  overflow-x: auto;\r\n  scroll-behavior: smooth;\r\n  padding: 1rem 0;\r\n  scrollbar-width: none;\r\n  -ms-overflow-style: none;\r\n}\r\n\r\n.carousel::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n/* Collection Card */\r\n.collectionCard {\r\n  flex: 0 0 320px;\r\n  height: 400px;\r\n  position: relative;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  box-shadow: 0 8px 32px rgba(30, 11, 172, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.collectionCard:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 60px rgba(74, 55, 40, 0.15);\r\n}\r\n\r\n.cardLink {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  text-decoration: none;\r\n  color: inherit;\r\n}\r\n\r\n/* Image Styles */\r\n.imageContainer {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.collectionImage {\r\n  object-fit: cover;\r\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.collectionCard:hover .collectionImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 1;\r\n}\r\n\r\n.noImagePlaceholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #1e0bac 0%, #4a3728 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.noImageText {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Card Content */\r\n.cardContent {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 2rem;\r\n  color: #ffffff;\r\n  z-index: 2;\r\n  background: linear-gradient(\r\n    to top,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n    transparent 100%\r\n  );\r\n}\r\n\r\n.productCount {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n  color: #1e3a8a;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.collectionName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 0.75rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.collectionDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  line-height: 1.4;\r\n  margin-bottom: 1rem;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.cardAction {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  color: #1e3a8a;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.collectionCard:hover .cardAction {\r\n  color: #ffffff;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.actionText {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* View All Button */\r\n.viewAllContainer {\r\n  text-align: center;\r\n}\r\n\r\n.viewAllButton {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem 2rem;\r\n  background: transparent;\r\n  color: #1e3a8a;\r\n  text-decoration: none;\r\n  border: 2px solid #1e3a8a;\r\n  border-radius: 50px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.viewAllButton:hover {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(74, 55, 40, 0.3);\r\n}\r\n\r\n/* Loading and Error States */\r\n.loadingContainer,\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #8b7355;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid rgba(74, 55, 40, 0.1);\r\n  border-top: 3px solid #1e3a8a;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .collectionsSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 280px;\r\n    height: 350px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .navigation {\r\n    align-self: flex-end;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .subtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 260px;\r\n    height: 320px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .navigation {\r\n    display: none;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 240px;\r\n    height: 300px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.25rem;\r\n  }\r\n  \r\n  .collectionName {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAeA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/TestimonialsSection/testimonialsSection.module.css"], "sourcesContent": ["/* Testimonials Section Styles */\r\n.testimonialsSection {\r\n  padding: 8rem 0;\r\n  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);\r\n  color: #ffffff;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.testimonialsSection::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('/images/testimonials-pattern.svg') repeat;\r\n  opacity: 0.05;\r\n  z-index: 1;\r\n}\r\n\r\n.container {\r\n  position: relative;\r\n  z-index: 2;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  display: block;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3.5rem;\r\n  font-weight: 700;\r\n  color: #ffffff;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.02em;\r\n  line-height: 1.1;\r\n}\r\n\r\n.description {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Testimonials Container */\r\n.testimonialsContainer {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  margin-bottom: 4rem;\r\n}\r\n\r\n/* Testimonial Content */\r\n.testimonialContent {\r\n  position: relative;\r\n}\r\n\r\n.quoteIcon {\r\n  color: #white;\r\n  margin-bottom: 2rem;\r\n  opacity: 0.7;\r\n}\r\n\r\n.testimonialText {\r\n  position: relative;\r\n}\r\n\r\n.testimonialContent {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  line-height: 1.6;\r\n  color: #ffffff;\r\n  margin-bottom: 2rem;\r\n  font-style: italic;\r\n  font-weight: 400;\r\n}\r\n\r\n.rating {\r\n  display: flex;\r\n  gap: 0.25rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.projectInfo {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.projectLabel {\r\n  color: #white;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.projectName {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Testimonial Meta */\r\n.testimonialMeta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.authorInfo {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.authorImage {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 3px solid #white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.authorPhoto {\r\n  object-fit: cover;\r\n}\r\n\r\n.authorDetails {\r\n  flex: 1;\r\n}\r\n\r\n.authorName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 0.25rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.authorTitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #white;\r\n  font-weight: 500;\r\n  margin-bottom: 0.125rem;\r\n}\r\n\r\n.authorCompany {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  justify-content: center;\r\n}\r\n\r\n.navDot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.navDot:hover {\r\n  background: rgba(212, 175, 140, 0.7);\r\n  transform: scale(1.2);\r\n}\r\n\r\n.navDot.active {\r\n  background: #white;\r\n  transform: scale(1.3);\r\n}\r\n\r\n/* Stats */\r\n.stats {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 2rem;\r\n  padding-top: 3rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.stat {\r\n  text-align: center;\r\n}\r\n\r\n.statNumber {\r\n  display: block;\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  line-height: 1;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.statLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .testimonialsSection {\r\n    padding: 6rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 3rem;\r\n  }\r\n  \r\n  .testimonialsContainer {\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .testimonialsSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 3rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .description {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .testimonialsContainer {\r\n    grid-template-columns: 1fr;\r\n    gap: 2.5rem;\r\n    text-align: center;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1.125rem;\r\n  }\r\n  \r\n  .authorInfo {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .stats {\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .authorInfo {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .authorImage {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .stats {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .statNumber {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;AAYA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/homeComponent.module.css"], "sourcesContent": ["/* Home Component Styles */\r\n.homeComponent {\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* Smooth scrolling for the entire page */\r\n.homeComponent {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n/* Ensure proper spacing between sections */\r\n.homeComponent > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* Add subtle transitions for section reveals */\r\n.homeComponent section {\r\n  opacity: 1;\r\n  transform: translateY(0);\r\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .homeComponent {\r\n    overflow-x: hidden;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .homeComponent {\r\n    background: white;\r\n    color: black;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAYA;;;;;AAMA;;;;;;AAOA;EACE;;;;;AAMF;EACE", "debugId": null}}]}