{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwYGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwSGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwcGFWNOITd.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 95.94%;\ndescent-override: 28.16%;\nline-gap-override: 0.00%;\nsize-adjust: 104.76%;\n\n}\n.className {\n    font-family: 'Geist', 'Geist Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-sans: 'Geist', 'Geist Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrMdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrkdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrcdmhHkjko.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 74.67%;\ndescent-override: 21.92%;\nline-gap-override: 0.00%;\nsize-adjust: 134.59%;\n\n}\n.className {\n    font-family: 'Geist Mono', 'Geist Mono Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-300: oklch(80.8% 0.114 19.571);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-700: oklch(50.5% 0.213 27.518);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-amber-50: oklch(98.7% 0.022 95.277);\n    --color-amber-100: oklch(96.2% 0.059 95.617);\n    --color-amber-200: oklch(92.4% 0.12 95.746);\n    --color-amber-300: oklch(87.9% 0.169 91.605);\n    --color-amber-400: oklch(82.8% 0.189 84.429);\n    --color-amber-500: oklch(76.9% 0.188 70.08);\n    --color-amber-600: oklch(66.6% 0.179 58.318);\n    --color-amber-700: oklch(55.5% 0.163 48.998);\n    --color-amber-800: oklch(47.3% 0.137 46.201);\n    --color-amber-900: oklch(41.4% 0.112 45.904);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-300: oklch(80.9% 0.105 251.813);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-gray-50: oklch(98.5% 0.002 247.839);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-md: 28rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-wider: 0.05em;\n    --radius-md: 0.375rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .top-20 {\n    top: calc(var(--spacing) * 20);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-4 {\n    margin-right: calc(var(--spacing) * 4);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-3 {\n    margin-left: calc(var(--spacing) * 3);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .max-h-40 {\n    max-height: calc(var(--spacing) * 40);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .-translate-x-full {\n    --tw-translate-x: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-0 {\n    --tw-translate-x: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .divide-y {\n    :where(& > :not(:last-child)) {\n      --tw-divide-y-reverse: 0;\n      border-bottom-style: var(--tw-border-style);\n      border-top-style: var(--tw-border-style);\n      border-top-width: calc(1px * var(--tw-divide-y-reverse));\n      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n    }\n  }\n  .divide-amber-200 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--color-amber-200);\n    }\n  }\n  .divide-black {\n    :where(& > :not(:last-child)) {\n      border-color: var(--color-black);\n    }\n  }\n  .divide-gray-200 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--color-gray-200);\n    }\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-md {\n    border-radius: var(--radius-md);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .rounded-l-lg {\n    border-top-left-radius: var(--radius-lg);\n    border-bottom-left-radius: var(--radius-lg);\n  }\n  .rounded-l-md {\n    border-top-left-radius: var(--radius-md);\n    border-bottom-left-radius: var(--radius-md);\n  }\n  .rounded-r-lg {\n    border-top-right-radius: var(--radius-lg);\n    border-bottom-right-radius: var(--radius-lg);\n  }\n  .rounded-r-md {\n    border-top-right-radius: var(--radius-md);\n    border-bottom-right-radius: var(--radius-md);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-r-2 {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 2px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-b-4 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 4px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-amber-200 {\n    border-color: var(--color-amber-200);\n  }\n  .border-amber-300 {\n    border-color: var(--color-amber-300);\n  }\n  .border-amber-900 {\n    border-color: var(--color-amber-900);\n  }\n  .border-black {\n    border-color: var(--color-black);\n  }\n  .border-blue-300 {\n    border-color: var(--color-blue-300);\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-red-300 {\n    border-color: var(--color-red-300);\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-white {\n    border-color: var(--color-white);\n  }\n  .border-yellow-200 {\n    border-color: var(--color-yellow-200);\n  }\n  .bg-amber-50 {\n    background-color: var(--color-amber-50);\n  }\n  .bg-amber-100 {\n    background-color: var(--color-amber-100);\n  }\n  .bg-amber-900 {\n    background-color: var(--color-amber-900);\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-blue-50 {\n    background-color: var(--color-blue-50);\n  }\n  .bg-blue-100 {\n    background-color: var(--color-blue-100);\n  }\n  .bg-blue-600 {\n    background-color: var(--color-blue-600);\n  }\n  .bg-gray-50 {\n    background-color: var(--color-gray-50);\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-600 {\n    background-color: var(--color-gray-600);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-orange-50 {\n    background-color: var(--color-orange-50);\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-600 {\n    background-color: var(--color-red-600);\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-12 {\n    padding: calc(var(--spacing) * 12);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-wider {\n    --tw-tracking: var(--tracking-wider);\n    letter-spacing: var(--tracking-wider);\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .whitespace-pre-line {\n    white-space: pre-line;\n  }\n  .text-amber-600 {\n    color: var(--color-amber-600);\n  }\n  .text-amber-700 {\n    color: var(--color-amber-700);\n  }\n  .text-amber-800 {\n    color: var(--color-amber-800);\n  }\n  .text-amber-900 {\n    color: var(--color-amber-900);\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-800 {\n    color: var(--color-blue-800);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-600 {\n    color: var(--color-gray-600);\n  }\n  .text-gray-700 {\n    color: var(--color-gray-700);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-purple-800 {\n    color: var(--color-purple-800);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-700 {\n    color: var(--color-red-700);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .lowercase {\n    text-transform: lowercase;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .placeholder-amber-400 {\n    &::placeholder {\n      color: var(--color-amber-400);\n    }\n  }\n  .placeholder-black {\n    &::placeholder {\n      color: var(--color-black);\n    }\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:border-amber-400 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-amber-400);\n      }\n    }\n  }\n  .hover\\:bg-amber-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-amber-50);\n      }\n    }\n  }\n  .hover\\:bg-amber-200 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-amber-200);\n      }\n    }\n  }\n  .hover\\:bg-amber-800 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-amber-800);\n      }\n    }\n  }\n  .hover\\:bg-black {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-black);\n      }\n    }\n  }\n  .hover\\:bg-blue-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-blue-700);\n      }\n    }\n  }\n  .hover\\:bg-gray-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-50);\n      }\n    }\n  }\n  .hover\\:bg-red-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-50);\n      }\n    }\n  }\n  .hover\\:bg-red-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-700);\n      }\n    }\n  }\n  .hover\\:text-amber-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-amber-800);\n      }\n    }\n  }\n  .hover\\:text-amber-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-amber-900);\n      }\n    }\n  }\n  .hover\\:text-black {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-black);\n      }\n    }\n  }\n  .hover\\:text-blue-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-900);\n      }\n    }\n  }\n  .hover\\:text-gray-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-gray-600);\n      }\n    }\n  }\n  .hover\\:text-green-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-green-800);\n      }\n    }\n  }\n  .hover\\:text-green-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-green-900);\n      }\n    }\n  }\n  .hover\\:text-red-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-800);\n      }\n    }\n  }\n  .hover\\:text-red-900 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-900);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-md {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .focus\\:border-amber-500 {\n    &:focus {\n      border-color: var(--color-amber-500);\n    }\n  }\n  .focus\\:border-black {\n    &:focus {\n      border-color: var(--color-black);\n    }\n  }\n  .focus\\:border-transparent {\n    &:focus {\n      border-color: transparent;\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-amber-500 {\n    &:focus {\n      --tw-ring-color: var(--color-amber-500);\n    }\n  }\n  .focus\\:ring-black {\n    &:focus {\n      --tw-ring-color: var(--color-black);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:static {\n    @media (width >= 64rem) {\n      position: static;\n    }\n  }\n  .lg\\:inset-0 {\n    @media (width >= 64rem) {\n      inset: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:translate-x-0 {\n    @media (width >= 64rem) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n}\n:root {\n  --background: #ffffff;\n  --foreground: rgb(255, 255, 255);\n}\n@media (prefers-color-scheme: dark) {\n  :root {\n    --background: rgb(255, 255, 255);\n    --foreground: #ededed;\n  }\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: Arial, Helvetica, sans-serif;\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-divide-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-divide-y-reverse: 0;\n      --tw-border-style: solid;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n    }\n  }\n}\r\n"], "names": [], "mappings": "AACA;EA4/CE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5/CJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA+FE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AA3OF;;AAAA;EAgPE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;;;EASA;;;;EAKA;;;;EAKA;;;;EAIF;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAKE;;;;EAKA;;;;EAIF;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAMI;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAOzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;AAK7B;;;;;AAIA;EACE;;;;;;AAKF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA", "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/shared/Header/header.module.css"], "sourcesContent": ["/* Blue and White Theme Variables */\r\n.Headerroot {\r\n  --cast-stone-blue: #2563eb;\r\n  --cast-stone-light-blue: #3b82f6;\r\n  --cast-stone-blue-50: #eff6ff;\r\n  --cast-stone-white: #ffffff;\r\n  --cast-stone-dark-text: #1f2937;\r\n  --cast-stone-gray-text: #4b5563;\r\n  --cast-stone-shadow: rgba(37, 99, 235, 0.1);\r\n  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);\r\n  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n/* Header Styles - Completely transparent design */\r\n.header {\r\n  background: transparent;\r\n  backdrop-filter: none;\r\n  border: none;\r\n  padding: 0;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 1000;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Header when scrolled - stays transparent */\r\n.header.scrolled {\r\n  background: transparent;\r\n  backdrop-filter: none;\r\n}\r\n\r\n/* Header for non-home pages - blue background with white text */\r\n.header.nonHomePage {\r\n  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  backdrop-filter: none;\r\n  position: relative;\r\n  box-shadow: 0 2px 10px rgba(37, 99, 235, 0.1);\r\n}\r\n\r\n.header.nonHomePage.scrolled {\r\n  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 1rem 2rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n}\r\n\r\n/* Logo Styles */\r\n.logo {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.logoLink {\r\n  text-decoration: none;\r\n  color: rgba(255, 255, 255, 0.95);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.scrolled .logoLink {\r\n  color: rgba(255, 255, 255, 0.95);\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.nonHomePage .logoLink {\r\n  color: var(--cast-stone-white);\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.logoLink:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.logoText {\r\n  font-size: 1.8rem;\r\n  font-weight: 600;\r\n  letter-spacing: 0.1em;\r\n  line-height: 1;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.logoSubtext {\r\n  font-size: 0.75rem;\r\n  font-weight: 400;\r\n  letter-spacing: 0.1em;\r\n  text-transform: uppercase;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-top: 2px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n}\r\n\r\n.nonHomePage .logoSubtext {\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n/* Navigation Styles */\r\n.nav {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navList {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  gap: 0;\r\n  align-items: center;\r\n}\r\n\r\n.navItem {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navLink,\r\n.navButton {\r\n  text-decoration: none;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 500;\r\n  font-size: 0.9rem;\r\n  padding: 1rem 1.5rem;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  letter-spacing: 0.05em;\r\n  text-transform: uppercase;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.scrolled .navLink,\r\n.scrolled .navButton {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.nonHomePage .navLink,\r\n.nonHomePage .navButton {\r\n  color: var(--cast-stone-white);\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.navLink:hover,\r\n.navButton:hover {\r\n  color: rgba(255, 255, 255, 1);\r\n  transform: translateY(-1px);\r\n  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n.scrolled .navLink:hover,\r\n.scrolled .navButton:hover {\r\n  color: rgba(255, 255, 255, 1);\r\n  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n.nonHomePage .navLink:hover,\r\n.nonHomePage .navButton:hover {\r\n  color: rgba(255, 255, 255, 1);\r\n  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n.navLink::after,\r\n.navButton::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  width: 0;\r\n  height: 2px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  transition: all 0.3s ease;\r\n  transform: translateX(-50%);\r\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.scrolled .navLink::after,\r\n.scrolled .navButton::after {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.nonHomePage .navLink::after,\r\n.nonHomePage .navButton::after {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.navLink:hover::after,\r\n.navButton:hover::after,\r\n.navButton.active::after {\r\n  width: 80%;\r\n}\r\n\r\n/* Dropdown Styles */\r\n.dropdownContainer {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.dropdownIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: var(--transition-smooth);\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.dropdownIcon.rotated {\r\n  transform: rotate(180deg);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.nonHomePage .dropdownIcon {\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.nonHomePage .dropdownIcon.rotated {\r\n  color: var(--cast-stone-white);\r\n}\r\n\r\n.loadingIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.dropdown {\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: var(--cast-stone-white);\r\n  border: 1px solid rgba(74, 55, 40, 0.1);\r\n  border-radius: 8px;\r\n  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);\r\n  min-width: 280px;\r\n  z-index: 1001;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transform: translateX(-50%) translateY(-10px);\r\n  transition: var(--transition-smooth);\r\n  animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n@keyframes dropdownSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    transform: translateX(-50%) translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    visibility: visible;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n.dropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.dropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.dropdownLink {\r\n  display: block;\r\n  padding: 0.75rem 1.25rem;\r\n  color: var(--cast-stone-dark-text);\r\n  text-decoration: none;\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 3px solid transparent;\r\n}\r\n\r\n.dropdownLink:hover {\r\n  background: rgba(37, 99, 235, 0.04);\r\n  color: var(--cast-stone-blue);\r\n  border-left-color: var(--cast-stone-blue);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Sub-dropdown Styles */\r\n.subDropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  background: rgba(74, 55, 40, 0.02);\r\n  border-top: 1px solid rgba(74, 55, 40, 0.08);\r\n}\r\n\r\n.subDropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.subDropdownLink {\r\n  display: block;\r\n  padding: 0.6rem 1.25rem 0.6rem 2rem;\r\n  color: var(--cast-stone-gray);\r\n  text-decoration: none;\r\n  font-size: 0.85rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 3px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.subDropdownLink::before {\r\n  content: '→  ';\r\n  position: absolute;\r\n  left: 0.75rem;\r\n  color: var(--cast-stone-gray);\r\n  font-size: 0.7rem;\r\n  transition: var(--transition-fast);\r\n}\r\n\r\n.subDropdownLink:hover {\r\n  background: rgba(74, 55, 40, 0.06);\r\n  color: var(--cast-stone-brown);\r\n  border-left-color: var(--cast-stone-light-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n.subDropdownLink:hover::before {\r\n  color: var(--cast-stone-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Sub-sub-dropdown Styles (Level 3) */\r\n.subSubDropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  background: rgba(74, 55, 40, 0.04);\r\n  border-top: 1px solid rgba(74, 55, 40, 0.12);\r\n}\r\n\r\n.subSubDropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.subSubDropdownLink {\r\n  display: block;\r\n  padding: 0.5rem 1.25rem 0.5rem 2.5rem;\r\n  color: var(--cast-stone-gray);\r\n  text-decoration: none;\r\n  font-size: 0.8rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 6px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.subSubDropdownLink::before {\r\n  content: '⤷  ';\r\n  position: absolute;\r\n  left: 1.5rem;\r\n  color: var(--cast-stone-gray);\r\n  font-size: 0.65rem;\r\n  transition: var(--transition-fast);\r\n}\r\n\r\n.subSubDropdownLink:hover {\r\n  background: rgba(74, 55, 40, 0.08);\r\n  color: var(--cast-stone-brown);\r\n  border-left-color: var(--cast-stone-light-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n.subSubDropdownLink:hover::before {\r\n  color: var(--cast-stone-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Cart Styles */\r\n.cartContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 1rem;\r\n}\r\n\r\n.cartLink {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 44px;\r\n  height: 44px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  text-decoration: none;\r\n  border-radius: 8px;\r\n  transition: var(--transition-smooth);\r\n  position: relative;\r\n}\r\n\r\n.nonHomePage .cartLink {\r\n  color: var(--cast-stone-white);\r\n}\r\n\r\n.cartLink:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 1);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.nonHomePage .cartLink:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  color: var(--cast-stone-white);\r\n}\r\n\r\n.cartIconWrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.cartBadge {\r\n  position: absolute;\r\n  top: -8px;\r\n  right: -8px;\r\n  background: #dc2626;\r\n  color: white;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  min-width: 20px;\r\n  height: 20px;\r\n  border-radius: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 4px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  animation: cartBadgeAppear 0.3s ease-out;\r\n}\r\n\r\n@keyframes cartBadgeAppear {\r\n  0% {\r\n    transform: scale(0);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n\r\n  .navList {\r\n    gap: 0;\r\n  }\r\n\r\n  .navLink,\r\n  .navButton {\r\n    padding: 1.5rem 1rem;\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n    height: 70px;\r\n  }\r\n\r\n  .logoText {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .logoSubtext {\r\n    font-size: 0.7rem;\r\n  }\r\n\r\n  .navList {\r\n    gap: 0;\r\n  }\r\n\r\n  .navLink,\r\n  .navButton {\r\n    padding: 1.25rem 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .dropdown {\r\n    min-width: 200px;\r\n  }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .nav {\r\n    display: none; /* Will implement mobile menu in future */\r\n  }\r\n\r\n  .container {\r\n    justify-content: space-between;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAKA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;AAMA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;AAMA;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;;AAMA;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;AAQA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;AAeA;EACE;;;;EAIA;;;;EAIA;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA", "debugId": null}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/shared/Footer/footer.module.css"], "sourcesContent": ["/* Footer Styles */\r\n.footer {\r\n  background: #1e40af;\r\n  color: #ffffff;\r\n  padding: 4rem 0 2rem;\r\n  position: relative;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Content Grid */\r\n.content {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr 1fr 1fr;\r\n  gap: 3rem;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 3rem;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Brand Section */\r\n.brand {\r\n  max-width: 400px;\r\n}\r\n\r\n.brandName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.brandDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 2rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Social Links */\r\n.socialLinks {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.socialLink {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 50%;\r\n  color: #white;\r\n  text-decoration: none;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.socialLink:hover {\r\n  background: #white;\r\n  color: #4a3728;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Link Groups */\r\n.linkGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.linkGroupTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.01em;\r\n}\r\n\r\n.linkList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.link {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n}\r\n\r\n.link:hover {\r\n  color: #white;\r\n  transform: translateX(4px);\r\n}\r\n\r\n/* Contact Info */\r\n.contactInfo {\r\n  grid-column: 1 / -1;\r\n  margin-top: 2rem;\r\n  padding-top: 2rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.contactTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.01em;\r\n}\r\n\r\n.contactDetails {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1.5rem;\r\n}\r\n\r\n.contactItem {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.contactLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.contactValue {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  line-height: 1.4;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Bottom Section */\r\n.bottom {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: 2rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.copyright {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-weight: 400;\r\n}\r\n\r\n.legalLinks {\r\n  display: flex;\r\n  gap: 2rem;\r\n}\r\n\r\n.legalLink {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-decoration: none;\r\n  transition: color 0.3s ease;\r\n  font-weight: 400;\r\n}\r\n\r\n.legalLink:hover {\r\n  color: #white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .footer {\r\n    padding: 3rem 0 1.5rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n\r\n  .content {\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 2.5rem;\r\n  }\r\n\r\n  .brand {\r\n    grid-column: 1 / -1;\r\n    max-width: none;\r\n    text-align: center;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .socialLinks {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .footer {\r\n    padding: 2.5rem 0 1rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .content {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .brand {\r\n    margin-bottom: 1.5rem;\r\n  }\r\n\r\n  .brandName {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .brandDescription {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .linkGroup {\r\n    align-items: center;\r\n  }\r\n\r\n  .linkGroupTitle {\r\n    font-size: 1.125rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .contactInfo {\r\n    margin-top: 1.5rem;\r\n    padding-top: 1.5rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .contactDetails {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .bottom {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .legalLinks {\r\n    gap: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .footer {\r\n    padding: 2rem 0 1rem;\r\n  }\r\n\r\n  .brandName {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .brandDescription {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .socialLinks {\r\n    gap: 0.75rem;\r\n  }\r\n\r\n  .socialLink {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n\r\n  .linkGroupTitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .link {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .contactTitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .contactLabel {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .contactValue {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .legalLinks {\r\n    flex-direction: column;\r\n    gap: 0.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}