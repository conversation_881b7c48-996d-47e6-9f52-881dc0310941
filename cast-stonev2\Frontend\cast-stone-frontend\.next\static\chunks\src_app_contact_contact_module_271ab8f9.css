/* [project]/src/app/contact/contact.module.css [app-client] (css) */
.contact-module__swMWYG__contactPage {
  --navy-blue: #1e3b8a;
  --dark-navy: #1e40af;
  --light-navy: #3b82f6;
  --white: #fff;
  --light-gray: #f8fafc;
  --medium-gray: #64748b;
  --dark-gray: #334155;
  --border-gray: #e2e8f0;
  --shadow: #1e3a8a1a;
  --shadow-hover: #1e3a8a26;
  --transition: all .3s ease;
  background-color: var(--white);
  min-height: 100vh;
  font-family: Helvetica Neue, Arial, sans-serif;
}

.contact-module__swMWYG__mainContent {
  background-color: var(--white);
  padding: 2rem 0;
}

.contact-module__swMWYG__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-module__swMWYG__contentGrid {
  grid-template-columns: 1fr 1fr;
  align-items: start;
  gap: 3rem;
  min-height: 80vh;
  display: grid;
}

.contact-module__swMWYG__infoSection {
  padding: 2rem 0;
}

.contact-module__swMWYG__heroText {
  margin-bottom: 2rem;
}

.contact-module__swMWYG__heroTitle {
  color: var(--dark-gray);
  margin-bottom: 2rem;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 1.6;
}

.contact-module__swMWYG__contactCards {
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  display: flex;
}

.contact-module__swMWYG__contactCard {
  background: var(--light-gray);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  display: flex;
}

.contact-module__swMWYG__cardIcon {
  width: 24px;
  height: 24px;
  color: var(--navy-blue);
  flex-shrink: 0;
}

.contact-module__swMWYG__cardContent {
  flex: 1;
}

.contact-module__swMWYG__cardTitle {
  color: var(--dark-gray);
  margin: 0;
  font-size: .95rem;
  font-weight: 600;
}

.contact-module__swMWYG__cardSubtitle {
  color: var(--medium-gray);
  margin: 0;
  font-size: .9rem;
}

.contact-module__swMWYG__companyDescription {
  margin-top: 2rem;
}

.contact-module__swMWYG__descriptionText {
  color: var(--dark-gray);
  margin-bottom: 1rem;
  font-size: .95rem;
  line-height: 1.6;
}

.contact-module__swMWYG__descriptionText:last-child {
  margin-bottom: 0;
}

.contact-module__swMWYG__formSection {
  padding: 2rem 0;
}

.contact-module__swMWYG__formCard {
  background: var(--light-gray);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  padding: 2rem;
}

.contact-module__swMWYG__formTitle {
  color: var(--dark-gray);
  margin-bottom: .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.contact-module__swMWYG__requiredField {
  color: var(--medium-gray);
  margin-bottom: 1.5rem;
  font-size: .9rem;
}

.contact-module__swMWYG__contactForm {
  flex-direction: column;
  gap: 1.25rem;
  display: flex;
}

.contact-module__swMWYG__formGroup {
  flex-direction: column;
  display: flex;
}

.contact-module__swMWYG__label {
  color: var(--dark-gray);
  margin-bottom: .5rem;
  font-size: .9rem;
  font-weight: 500;
}

.contact-module__swMWYG__input, .contact-module__swMWYG__select, .contact-module__swMWYG__textarea {
  border: 1px solid var(--border-gray);
  background-color: var(--white);
  color: var(--dark-gray);
  transition: var(--transition);
  border-radius: 4px;
  outline: none;
  width: 100%;
  padding: .75rem 1rem;
  font-size: .95rem;
}

.contact-module__swMWYG__input:focus, .contact-module__swMWYG__select:focus, .contact-module__swMWYG__textarea:focus {
  border-color: var(--navy-blue);
  box-shadow: 0 0 0 2px #1e3a8a1a;
}

.contact-module__swMWYG__textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.contact-module__swMWYG__textarea::placeholder {
  color: var(--medium-gray);
  opacity: .8;
}

.contact-module__swMWYG__select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23334155\" stroke-width=\"2\"><polyline points=\"6,9 12,15 18,9\"/></svg>");
  background-position: right .75rem center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.contact-module__swMWYG__disclaimer {
  margin: 1rem 0;
}

.contact-module__swMWYG__disclaimerText {
  color: var(--medium-gray);
  margin-bottom: .5rem;
  font-size: .8rem;
  line-height: 1.4;
}

.contact-module__swMWYG__disclaimerText:last-child {
  margin-bottom: 0;
}

.contact-module__swMWYG__submitButton {
  background: var(--navy-blue);
  color: var(--white);
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: .05em;
  border: none;
  border-radius: 4px;
  align-self: flex-start;
  margin-top: .5rem;
  padding: .875rem 2rem;
  font-size: .9rem;
  font-weight: 600;
}

.contact-module__swMWYG__submitButton:hover:not(:disabled) {
  background: #c19660;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #d4a5744d;
}

.contact-module__swMWYG__submitButton:disabled {
  opacity: .7;
  cursor: not-allowed;
  transform: none;
}

.contact-module__swMWYG__submitting {
  background: var(--medium-gray) !important;
}

.contact-module__swMWYG__submitMessage {
  border: 1px solid;
  border-radius: 4px;
  margin-bottom: 1rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 500;
}

.contact-module__swMWYG__submitMessage.contact-module__swMWYG__success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.contact-module__swMWYG__submitMessage.contact-module__swMWYG__error {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

@media (width <= 1024px) {
  .contact-module__swMWYG__contentGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-module__swMWYG__infoSection {
    order: 2;
  }

  .contact-module__swMWYG__formSection {
    order: 1;
  }
}

@media (width <= 768px) {
  .contact-module__swMWYG__container {
    padding: 0 1rem;
  }

  .contact-module__swMWYG__mainContent {
    padding: 1rem 0;
  }

  .contact-module__swMWYG__formCard {
    padding: 1.5rem;
  }

  .contact-module__swMWYG__heroTitle {
    font-size: 1.25rem;
  }

  .contact-module__swMWYG__contactCards {
    gap: .75rem;
  }

  .contact-module__swMWYG__contactCard {
    padding: .75rem;
  }

  .contact-module__swMWYG__descriptionText {
    font-size: .9rem;
  }
}

@media (width <= 480px) {
  .contact-module__swMWYG__formCard {
    padding: 1rem;
  }

  .contact-module__swMWYG__heroTitle {
    font-size: 1.1rem;
  }

  .contact-module__swMWYG__formTitle {
    font-size: 1.25rem;
  }

  .contact-module__swMWYG__input, .contact-module__swMWYG__select, .contact-module__swMWYG__textarea {
    padding: .625rem .75rem;
    font-size: .9rem;
  }

  .contact-module__swMWYG__submitButton {
    padding: .75rem 1.5rem;
    font-size: .85rem;
  }
}


/*# sourceMappingURL=src_app_contact_contact_module_271ab8f9.css.map*/