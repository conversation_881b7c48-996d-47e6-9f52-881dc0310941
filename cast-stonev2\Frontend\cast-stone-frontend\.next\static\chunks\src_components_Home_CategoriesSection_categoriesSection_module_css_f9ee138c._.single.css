/* [project]/src/components/Home/CategoriesSection/categoriesSection.module.css [app-client] (css) */
.categoriesSection-module__fGUJra__categoriesSection {
  background: linear-gradient(135deg, #faf9f7 0%, #fff 100%);
  padding: 6rem 0;
  position: relative;
}

.categoriesSection-module__fGUJra__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.categoriesSection-module__fGUJra__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.categoriesSection-module__fGUJra__sectionTitle {
  color: #1e3a8a;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3rem;
  font-weight: 700;
}

.categoriesSection-module__fGUJra__sectionSubtitle {
  color: #364c89;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.categoriesSection-module__fGUJra__grid, .categoriesSection-module__fGUJra__loadingGrid {
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.categoriesSection-module__fGUJra__loadingCard {
  background: #fff;
  border-radius: 16px;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite categoriesSection-module__fGUJra__pulse;
  overflow: hidden;
  box-shadow: 0 4px 20px #1e3a8a14;
}

.categoriesSection-module__fGUJra__loadingImage {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  width: 100%;
  height: 300px;
  animation: 2s infinite categoriesSection-module__fGUJra__shimmer;
}

.categoriesSection-module__fGUJra__loadingContent {
  padding: 2rem;
}

.categoriesSection-module__fGUJra__loadingText {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 1rem;
  margin-bottom: .75rem;
  animation: 2s infinite categoriesSection-module__fGUJra__shimmer;
}

.categoriesSection-module__fGUJra__loadingText:first-child {
  width: 60%;
}

.categoriesSection-module__fGUJra__loadingText:nth-child(2) {
  width: 80%;
}

.categoriesSection-module__fGUJra__loadingText:nth-child(3) {
  width: 40%;
}

@keyframes categoriesSection-module__fGUJra__shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes categoriesSection-module__fGUJra__pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.categoriesSection-module__fGUJra__errorMessage {
  text-align: center;
  color: #6b7280;
  padding: 4rem 2rem;
  font-size: 1.125rem;
}

.categoriesSection-module__fGUJra__categoryCard {
  background: #fff;
  border-radius: 16px;
  height: 400px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #4a37281a;
}

.categoriesSection-module__fGUJra__categoryCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px #6e686326;
}

.categoriesSection-module__fGUJra__cardLink {
  width: 100%;
  height: 100%;
  color: inherit;
  text-decoration: none;
  display: block;
  position: relative;
}

.categoriesSection-module__fGUJra__imageContainer {
  height: 60%;
  position: relative;
  overflow: hidden;
}

.categoriesSection-module__fGUJra__categoryImage {
  object-fit: cover;
  transition: transform .6s cubic-bezier(.4, 0, .2, 1);
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__categoryImage {
  transform: scale(1.05);
}

.categoriesSection-module__fGUJra__imageOverlay {
  z-index: 1;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__noImagePlaceholder {
  background: linear-gradient(135deg, #1e0bac 0%, #4a3728 100%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.categoriesSection-module__fGUJra__noImageText {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: 1rem;
  font-weight: 500;
}

.categoriesSection-module__fGUJra__cardContent {
  z-index: 2;
  flex-direction: column;
  justify-content: space-between;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  position: relative;
}

.categoriesSection-module__fGUJra__cardHeader {
  margin-bottom: 1rem;
}

.categoriesSection-module__fGUJra__stats {
  align-items: baseline;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.categoriesSection-module__fGUJra__statsNumber {
  color: #1e3a8a;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.categoriesSection-module__fGUJra__statsLabel {
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 500;
}

.categoriesSection-module__fGUJra__categoryTitle {
  color: #1e3a8a;
  margin: .25rem 0;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.categoriesSection-module__fGUJra__categorySubtitle {
  color: #30457e;
  text-transform: uppercase;
  letter-spacing: .15em;
  margin: 0;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
}

.categoriesSection-module__fGUJra__categoryDescription {
  color: #1e3a8a;
  flex-grow: 1;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  line-height: 1.5;
}

.categoriesSection-module__fGUJra__cardActions {
  align-items: center;
  gap: .75rem;
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
  text-transform: uppercase;
  letter-spacing: .05em;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton {
  color: #fff;
  background: #1e3a8a;
  padding: .5rem 1rem;
}

.categoriesSection-module__fGUJra__actionButton:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.categoriesSection-module__fGUJra__secondaryButton {
  color: #334986;
  background: none;
  border: 1px solid #6978a14d;
  padding: .5rem .75rem;
}

.categoriesSection-module__fGUJra__secondaryButton:hover {
  color: #1e3a8a;
  background: #4542991a;
}

.categoriesSection-module__fGUJra__hoverEffect {
  opacity: 0;
  z-index: 1;
  background: linear-gradient(135deg, #4a37280d 0%, #0000 50%, #8b73550d 100%);
  transition: opacity .3s;
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__hoverEffect {
  opacity: 1;
}

@media (width <= 1024px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 4rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1.5rem;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2.5rem;
  }

  .categoriesSection-module__fGUJra__grid {
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 350px;
  }
}

@media (width <= 768px) {
  .categoriesSection-module__fGUJra__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 300px;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2rem;
  }

  .categoriesSection-module__fGUJra__sectionSubtitle {
    font-size: 1rem;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1.25rem;
  }

  .categoriesSection-module__fGUJra__statsNumber {
    font-size: 1.75rem;
  }

  .categoriesSection-module__fGUJra__categoryTitle {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 3rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1rem;
  }

  .categoriesSection-module__fGUJra__header {
    margin-bottom: 2.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 280px;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1rem;
  }

  .categoriesSection-module__fGUJra__cardActions {
    flex-direction: column;
    gap: .5rem;
  }

  .categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
    justify-content: center;
    width: 100%;
  }
}

/*# sourceMappingURL=src_components_Home_CategoriesSection_categoriesSection_module_css_f9ee138c._.single.css.map*/