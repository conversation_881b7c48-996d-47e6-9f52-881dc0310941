/* [project]/src/components/cart/CartItem/cartItem.module.css [app-client] (css) */
.cartItem-module__IXYF0W__cartItem {
  background: #fff;
  border-radius: 8px;
  grid-template-columns: 120px 1fr auto auto auto;
  align-items: start;
  gap: 1.5rem;
  padding: 1.5rem;
  transition: box-shadow .3s;
  display: grid;
  box-shadow: 0 2px 8px #0000001a;
}

.cartItem-module__IXYF0W__cartItem:hover {
  box-shadow: 0 4px 16px #00000026;
}

.cartItem-module__IXYF0W__imageContainer {
  background: #f8f9fa;
  border-radius: 8px;
  width: 120px;
  height: 120px;
  overflow: hidden;
}

.cartItem-module__IXYF0W__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.cartItem-module__IXYF0W__productDetails {
  min-width: 0;
}

.cartItem-module__IXYF0W__productName {
  color: #4a3728;
  margin: 0 0 .5rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.cartItem-module__IXYF0W__productDescription {
  color: #6b5b4d;
  margin: 0 0 .75rem;
  font-size: .9rem;
  line-height: 1.5;
}

.cartItem-module__IXYF0W__productMeta {
  flex-direction: column;
  gap: .25rem;
  margin-bottom: .5rem;
  display: flex;
}

.cartItem-module__IXYF0W__unitPrice {
  color: #4a3728;
  font-size: 1rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__collection {
  color: #8b7355;
  font-size: .85rem;
  font-style: italic;
}

.cartItem-module__IXYF0W__stockStatus {
  margin-top: .5rem;
}

.cartItem-module__IXYF0W__inStock {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__outOfStock {
  color: #dc2626;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__quantitySection {
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  min-width: 120px;
  display: flex;
}

.cartItem-module__IXYF0W__quantityLabel {
  color: #4a3728;
  text-align: center;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__quantityControls {
  background: #f8f9fa;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: .25rem;
  display: flex;
}

.cartItem-module__IXYF0W__quantityBtn {
  color: #4a3728;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-weight: 600;
  transition: all .2s;
  display: flex;
}

.cartItem-module__IXYF0W__quantityBtn:hover:not(:disabled) {
  color: #fff;
  background: #8b7355;
  border-color: #8b7355;
}

.cartItem-module__IXYF0W__quantityBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartItem-module__IXYF0W__quantity {
  text-align: center;
  color: #4a3728;
  min-width: 40px;
  font-size: 1rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__updating {
  color: #8b7355;
  font-size: .75rem;
  font-style: italic;
}

.cartItem-module__IXYF0W__priceSection {
  flex-direction: column;
  align-items: flex-end;
  gap: .25rem;
  min-width: 120px;
  display: flex;
}

.cartItem-module__IXYF0W__itemTotal {
  color: #4a3728;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartItem-module__IXYF0W__priceBreakdown {
  color: #6b5b4d;
  font-size: .85rem;
}

.cartItem-module__IXYF0W__removeSection {
  align-items: flex-start;
  display: flex;
}

.cartItem-module__IXYF0W__removeBtn {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .2s;
  display: flex;
}

.cartItem-module__IXYF0W__removeBtn:hover:not(:disabled) {
  color: #b91c1c;
  background: #fee2e2;
}

.cartItem-module__IXYF0W__removeBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartItem-module__IXYF0W__removeBtn svg {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cartItem-module__IXYF0W__removing {
  font-size: 1.2rem;
  font-weight: bold;
}

@media (width <= 768px) {
  .cartItem-module__IXYF0W__cartItem {
    grid-template-columns: 80px 1fr;
    grid-template-areas: "image details"
                         "image quantity"
                         "image price"
                         "remove remove";
    gap: 1rem;
    padding: 1rem;
  }

  .cartItem-module__IXYF0W__imageContainer {
    grid-area: image;
    width: 80px;
    height: 80px;
  }

  .cartItem-module__IXYF0W__productDetails {
    grid-area: details;
  }

  .cartItem-module__IXYF0W__quantitySection {
    flex-direction: row;
    grid-area: quantity;
    justify-content: flex-start;
    align-items: center;
    min-width: auto;
  }

  .cartItem-module__IXYF0W__quantityLabel {
    margin-right: .5rem;
  }

  .cartItem-module__IXYF0W__priceSection {
    grid-area: price;
    align-items: flex-start;
    min-width: auto;
  }

  .cartItem-module__IXYF0W__removeSection {
    grid-area: remove;
    justify-content: center;
    margin-top: .5rem;
  }

  .cartItem-module__IXYF0W__productName {
    font-size: 1.1rem;
  }

  .cartItem-module__IXYF0W__itemTotal {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .cartItem-module__IXYF0W__cartItem {
    text-align: center;
    grid-template-columns: 1fr;
    grid-template-areas: "image"
                         "details"
                         "quantity"
                         "price"
                         "remove";
  }

  .cartItem-module__IXYF0W__imageContainer {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }

  .cartItem-module__IXYF0W__quantitySection {
    justify-content: center;
  }

  .cartItem-module__IXYF0W__priceSection {
    align-items: center;
  }
}

/*# sourceMappingURL=src_components_cart_CartItem_cartItem_module_css_f9ee138c._.single.css.map*/