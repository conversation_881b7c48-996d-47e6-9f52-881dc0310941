{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css"], "sourcesContent": ["/* Home Component Styles */\r\n.homeComponent {\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* Smooth scrolling for the entire page */\r\n.homeComponent {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n/* Ensure proper spacing between sections */\r\n.homeComponent > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* Add subtle transitions for section reveals */\r\n.homeComponent section {\r\n  opacity: 1;\r\n  transform: translateY(0);\r\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .homeComponent {\r\n    overflow-x: hidden;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .homeComponent {\r\n    background: white;\r\n    color: black;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAYA;;;;;AAMA;;;;;;AAOA;EACE;;;;;AAMF;EACE"}}]}