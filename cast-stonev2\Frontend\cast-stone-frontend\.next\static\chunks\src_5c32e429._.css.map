{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductImageGallery/productImageGallery.module.css"], "sourcesContent": ["/* Product Image Gallery - Sharp Rectangular Design */\r\n.imageGallery {\r\n  width: 100%;\r\n}\r\n\r\n/* Main Image Container */\r\n.mainImageContainer {\r\n  position: relative;\r\n  background: #f8f8f8;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n  aspect-ratio: 1;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.mainImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: zoom-in;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.mainImage.zoomed {\r\n  cursor: zoom-out;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.navButton {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  border: none;\r\n  width: 50px;\r\n  height: 50px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.navButton:hover {\r\n  background: rgba(0, 0, 0, 0.9);\r\n}\r\n\r\n.prevButton {\r\n  left: 10px;\r\n}\r\n\r\n.nextButton {\r\n  right: 10px;\r\n}\r\n\r\n/* Image Counter */\r\n.imageCounter {\r\n  position: absolute;\r\n  bottom: 15px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Zoom Indicator */\r\n.zoomIndicator {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #1f2937;\r\n  padding: 0.5rem;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  border: 1px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Thumbnail Container */\r\n/* .thumbnailContainer {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\r\n  gap: 0.5rem;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.thumbnail {\r\n  background: none;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  aspect-ratio: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n} */\r\n\r\n.thumbnailContainer {\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  white-space: nowrap;\r\n  padding: 8px 0;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.thumbnail {\r\n  display: inline-block;\r\n  border: none;\r\n  background: none;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 60px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border: 2px solid transparent;\r\n  border-radius: 4px;\r\n  transition: border 0.3s;\r\n}\r\n\r\n.activeThumbnail .thumbnailImage {\r\n  border-color: #2563eb; /* Highlight active */\r\n}\r\n\r\n.thumbnail:hover {\r\n  border-color: #2563eb;\r\n}\r\n\r\n.thumbnail.activeThumbnail {\r\n  border-color: #2563eb;\r\n  border-width: 3px;\r\n}\r\n\r\n\r\n\r\n/* Zoom Overlay */\r\n.zoomOverlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: zoom-out;\r\n}\r\n\r\n.zoomedImageContainer {\r\n  position: relative;\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n}\r\n\r\n.zoomedImage {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.closeZoomButton {\r\n  position: absolute;\r\n  top: -50px;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #1f2937;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.closeZoomButton:hover {\r\n  background: white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .navButton {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n  \r\n  .prevButton {\r\n    left: 5px;\r\n  }\r\n  \r\n  .nextButton {\r\n    right: 5px;\r\n  }\r\n  \r\n  .imageCounter {\r\n    bottom: 10px;\r\n    font-size: 0.8rem;\r\n    padding: 0.25rem 0.75rem;\r\n  }\r\n  \r\n  .zoomIndicator {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 0.25rem;\r\n    font-size: 0.7rem;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .zoomedImageContainer {\r\n    max-width: 95vw;\r\n    max-height: 95vh;\r\n  }\r\n  \r\n  .closeZoomButton {\r\n    top: -40px;\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .mainImageContainer {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n  \r\n  .navButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;AA6CA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;AAQA;;;;;AAQA;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;;EAOA;;;;;EAKA;;;;;EAKA;;;;;;;AAOF;EACE;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductSpecifications/productSpecifications.module.css"], "sourcesContent": ["/* Product Specifications - Sharp Rectangular Design */\r\n.specificationsContainer {\r\n  /* margin: 3rem 0; */\r\n  /* margin: 2rem 0 0 0 auto;\r\n  width: 100%;\r\n  max-width: 40vw; */\r\n\r\n  margin-top: 2rem;\r\n  /* margin-left: auto; */\r\n  width: 100%;\r\n  /* max-width: 35vw; */\r\n  /* margin-top: -20rem; */\r\n  margin-bottom: 15rem;\r\n}\r\n\r\n/* Section Styles */\r\n.section {\r\n  border: 0px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  margin-bottom: 1rem;\r\n  background: white;\r\n}\r\n\r\n.sectionHeader {\r\n  width: 100%;\r\n  background: #f5f5f5;\r\n  border: none;\r\n  border-bottom: 1px solid #ddd;\r\n  padding: 1.5rem;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color:rgb(14, 14, 14);\r\n  transition: background-color 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.sectionHeader:hover {\r\n  background: #eeeeee;\r\n}\r\n\r\n.sectionHeader.active {\r\n    /* background: #f5f5f5; */\r\n  background: #f5f5f5;\r\n  color: black;\r\n}\r\n\r\n.toggleIcon {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sectionContent {\r\n  padding: 2rem;\r\n  border-top: 1px solid #ddd;\r\n}\r\n\r\n/* Specifications Grid - Simple Lines Style */\r\n.specGrid {\r\n  display: block;\r\n  width: 100%;\r\n  background: white;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.specRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  line-height: 1.4;\r\n}\r\n\r\n.specRow:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.specLabel {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.specValue {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  flex: 1;\r\n}\r\n\r\n.specValue.inStock {\r\n  color: #28a745;\r\n  font-weight: 600;\r\n}\r\n\r\n.specValue.outOfStock {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Tags */\r\n.tagContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.tag {\r\n  background: #1e40af;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  font-size: 0.8rem;\r\n  border-radius: 0; /* Sharp corners */\r\n  border: 1px solid #1e40af;\r\n}\r\n\r\n/* Details Content */\r\n.detailsContent {\r\n  line-height: 1.6;\r\n}\r\n\r\n.description {\r\n  color: #555;\r\n  margin-bottom: 2rem;\r\n  font-size: 1rem;\r\n}\r\n\r\n.featureList h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.featureList ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.featureList li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.featureList li:before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.featureList li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Care Content */\r\n.careContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 3rem;\r\n}\r\n\r\n.careSection h4,\r\n.downloadSection h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.careSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.careSection li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.careSection li:before {\r\n  content: '•';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.careSection li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Download Links */\r\n.downloadLinks {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.downloadLink {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem;\r\n  border: 2px solid #1e40af;\r\n  color: #1e40af;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-weight: 500;\r\n}\r\n\r\n.downloadLink:hover {\r\n  background: #1e40af;\r\n  color: white;\r\n}\r\n\r\n/* Share Section */\r\n.shareSection {\r\n  margin-top: 2rem;\r\n  padding: 1.5rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  background: #f8f8f8;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.shareLabel {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  font-size: 1rem;\r\n}\r\n\r\n.shareButtons {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.shareButton {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.shareButton:hover {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .careContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .specRow {\r\n    grid-template-columns: 1fr;\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .specLabel {\r\n    font-weight: 700;\r\n  }\r\n  \r\n  .sectionHeader {\r\n    padding: 1rem;\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .sectionContent {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .shareSection {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    text-align: center;\r\n  }\r\n  \r\n  .shareButtons {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n  @media (max-width: 768px) {\r\n\r\n  .downloadLink {\r\n    padding: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .shareButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .tagContainer {\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .tag {\r\n    font-size: 0.75rem;\r\n    padding: 0.2rem 0.5rem;\r\n  }\r\n  .specRow {\r\n    display: flex !important;\r\n    flex-direction: row !important;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .specLabel {\r\n    font-weight: 600;\r\n    flex: 0 0 auto;\r\n    min-width: 100px;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  /* Mobile responsive simple layout */\r\n  .specGrid {\r\n    display: block;\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n  .specRow {\r\n    display: flex;\r\n    align-items: baseline;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .specRow:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .specLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .specValue {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n    word-break: break-word;\r\n  }\r\n\r\n  .sectionHeader {\r\n    padding: 1rem;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .sectionContent {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .shareSection {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    text-align: center;\r\n  }\r\n\r\n  .shareButtons {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AACA;;;;;;AAeA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;EA2BA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;;;;EASA;;;;;;;EAQA;;;;;EAKA;;;;;;;;EAQA;;;;EAIA;;;;;;;;EAQA;;;;;;;EAOA;;;;;EAKA;;;;EAIA;;;;;;EAMA", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/PatinaSelector/patinaSelector.module.css"], "sourcesContent": ["/* Patina Selector - Smaller Version */\r\n.patinaSelector {\r\n  margin: 1rem 0;\r\n  padding: 1rem;\r\n  border: 1px solid #ddd;\r\n  background: #fafafa;\r\n}\r\n\r\n.selectorHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1rem;\r\n  padding-bottom: 0.75rem;\r\n  border-bottom: 1px solid #2563eb;\r\n}\r\n\r\n.selectorTitle {\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0;\r\n}\r\n\r\n.selectedPatina {\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  color: #2563eb;\r\n  background: white;\r\n  padding: 0.4rem 0.75rem;\r\n  border: 1px solid #2563eb;\r\n}\r\n\r\n/* Patina Grid */\r\n.patinaGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));\r\n  gap: 0.75rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.patinaOption {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  padding: 0.75rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  text-align: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.patinaOption:hover {\r\n  border-color: #2563eb;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);\r\n}\r\n\r\n.patinaOption.selected {\r\n  border-color: #2563eb;\r\n  border-width: 2px;\r\n  background: #eff6ff;\r\n  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.patinaColor {\r\n  width: 30px;\r\n  height: 30px;\r\n  border: 1px solid #333;\r\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.patinaName {\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  line-height: 1.1;\r\n}\r\n\r\n.patinaOption.selected .patinaName {\r\n  font-weight: 700;\r\n}\r\n\r\n/* Patina Note */\r\n.patinaNote {\r\n  background: #fff3cd;\r\n  border: 1px solid #ffeaa7;\r\n  padding: 0.75rem;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.patinaNote p {\r\n  margin: 0;\r\n  font-size: 0.8rem;\r\n  color: #856404;\r\n  line-height: 1.3;\r\n}\r\n\r\n.patinaNote strong {\r\n  color: #533f03;\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 768px) {\r\n  .patinaSelector {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .selectorHeader {\r\n    flex-direction: column;\r\n    gap: 0.75rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .patinaGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .patinaOption {\r\n    padding: 0.5rem;\r\n    min-height: 70px;\r\n    gap: 0.4rem;\r\n  }\r\n\r\n  .patinaColor {\r\n    width: 25px;\r\n    height: 25px;\r\n  }\r\n\r\n  .patinaName {\r\n    font-size: 0.7rem;\r\n  }\r\n\r\n  .selectorTitle {\r\n    font-size: 0.95rem;\r\n  }\r\n\r\n  .selectedPatina {\r\n    font-size: 0.8rem;\r\n    padding: 0.35rem 0.6rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .patinaGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));\r\n  }\r\n\r\n  .patinaOption {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .patinaNote {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .patinaNote p {\r\n    font-size: 0.75rem;\r\n  }\r\n}\r\n\r\n/* Accessibility */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .patinaOption {\r\n    transition: none;\r\n  }\r\n\r\n  .patinaOption:hover {\r\n    transform: none;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;AAKA;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA", "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/RelatedProducts/relatedProducts.module.css"], "sourcesContent": ["/* Related Products - Sharp Rectangular Design */\r\n.relatedProducts {\r\n  margin: 4rem 0;\r\n  padding: 2rem 0;\r\n  border-top: 2px solid #ddd;\r\n}\r\n\r\n.sectionHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.sectionTitle {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0;\r\n}\r\n\r\n.scrollControls {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.scrollButton {\r\n  background: #2563eb;\r\n  color: white;\r\n  border: 2px solid #2563eb;\r\n  width: 45px;\r\n  height: 45px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.scrollButton:hover:not(.disabled) {\r\n  background: white;\r\n  color: #2563eb;\r\n}\r\n\r\n.scrollButton.disabled {\r\n  background: #ccc;\r\n  border-color: #ccc;\r\n  cursor: not-allowed;\r\n  opacity: 0.5;\r\n}\r\n\r\n/* Products Container */\r\n.productsContainer {\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  scrollbar-width: none; /* Firefox */\r\n  -ms-overflow-style: none; /* IE and Edge */\r\n}\r\n\r\n.productsContainer::-webkit-scrollbar {\r\n  display: none; /* Chrome, Safari, Opera */\r\n}\r\n\r\n.productsGrid {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  padding-bottom: 1rem;\r\n}\r\n\r\n/* Product Card */\r\n.productCard {\r\n  flex: 0 0 300px;\r\n  background: white;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.productCard:hover {\r\n  border-color: #2563eb;\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.productLink {\r\n  text-decoration: none;\r\n  color: inherit;\r\n  display: block;\r\n}\r\n\r\n/* Image Container */\r\n.imageContainer {\r\n  position: relative;\r\n  aspect-ratio: 1;\r\n  overflow: hidden;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.productCard:hover .productImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.outOfStockOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n/* Product Info */\r\n.productInfo {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.productName {\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0 0 1rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDetails {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.productCode {\r\n  display: block;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 0.85rem;\r\n  color: #666;\r\n  background: #f5f5f5;\r\n  padding: 0.25rem 0.5rem;\r\n  border: 1px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  margin-bottom: 0.75rem;\r\n  width: fit-content;\r\n}\r\n\r\n.availability {\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.inStock {\r\n  color: #28a745;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n.priceContainer {\r\n  border-top: 1px solid #eee;\r\n  padding-top: 1rem;\r\n}\r\n\r\n.price {\r\n  font-size: 1.3rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .productCard {\r\n    flex: 0 0 280px;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 1.75rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .relatedProducts {\r\n    margin: 3rem 0;\r\n  }\r\n  \r\n  .sectionHeader {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .scrollControls {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .productCard {\r\n    flex: 0 0 250px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.2rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 1.5rem;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .productCard {\r\n    flex: 0 0 220px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 0.75rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .productCode {\r\n    font-size: 0.8rem;\r\n    padding: 0.2rem 0.4rem;\r\n  }\r\n  \r\n  .scrollButton {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n}\r\n\r\n/* Accessibility */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .productCard {\r\n    transition: none;\r\n  }\r\n  \r\n  .productCard:hover {\r\n    transform: none;\r\n  }\r\n  \r\n  .productImage {\r\n    transition: none;\r\n  }\r\n  \r\n  .productCard:hover .productImage {\r\n    transform: none;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/products/%5Bid%5D/productPage.module.css"], "sourcesContent": ["/* Product Page Styles - Sharp Rectangular Design */\r\n.productPage {\r\n  padding-top: 6rem;\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 60vh;\r\n  color: #1e40af;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #1e40af;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #1e40af;\r\n}\r\n\r\n.errorContainer h1 {\r\n  font-size: 2rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Main Product Layout */\r\n.productMain {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  margin-bottom: 4rem;\r\n}\r\n\r\n.imageSection {\r\n  position: relative;\r\n}\r\n\r\n.detailsSection {\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* Product Title */\r\n.productTitle {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #1a1a1a;\r\n  margin-bottom: 1rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* Product Code */\r\n.productCode {\r\n  background: #f5f5f5;\r\n  border: 1px solid #ddd;\r\n  padding: 0.4rem 1rem;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 0.9rem;\r\n  color:rgb(0, 0, 0);\r\n  margin-bottom: 2rem;\r\n  border-radius: 0; /* Sharp corners */\r\n   display: inline-block;   \r\n}\r\n\r\n/* Product Info Grid */\r\n.productInfo {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.infoRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  line-height: 1.4;\r\n}\r\n\r\n.infoLabel {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.infoValue {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  flex: 1;\r\n}\r\n/* Price Section */\r\n.priceSection {\r\n  margin: 2rem 0;\r\n  padding: 1.5rem;\r\n  background: white;\r\n  /* border: 2px solid black; */\r\n  border-radius: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n  align-items: flex-start;\r\n}\r\n\r\n.priceRow {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: 2rem;\r\n  width: 100%;\r\n}\r\n\r\n\r\n.purchaseSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n  margin-top: 0; /* Remove extra space */\r\n}\r\n\r\n.price {\r\n  background:#f5f5f5;\r\n  padding: 6px;\r\n  font-size: 2rem;\r\n  font-weight: 520;\r\n  color:rgb(0, 0, 0);\r\n}\r\n\r\n/* Purchase Section */\r\n.purchaseSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n.quantitySelector {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2rem;\r\n}\r\n\r\n.quantitySelector label {\r\n  font-weight: 600;\r\n  color:rgb(0, 0, 0);\r\n}\r\n\r\n.quantityControls {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 2px solidrgb(0, 0, 0);\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n}\r\n\r\n.quantityBtn {\r\n  background: #f5f5f5;\r\n  color: black;\r\n  border: black;\r\n  padding: 0.75rem 1rem;\r\n  cursor: pointer;\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #1e40af;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  background: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantityInput {\r\n  border: none;\r\n  padding: 0.75rem 1rem;\r\n  text-align: center;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  width: 80px;\r\n  background: white;\r\n  outline: none;\r\n  color: black;\r\n}\r\n\r\n.addToCartRow {\r\n  display: grid;\r\n  grid-template-columns: 150px 1fr;\r\n  width: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.addToCartLabel {\r\nwidth: 400%;\r\n}\r\n\r\n.addToCartWrapper {\r\n  display: flex;\r\n  justify-content: center; \r\n  width: 100%;\r\n}\r\n\r\n.addToCartBtn {\r\n  background: #f5f5f5;\r\n  color: black;\r\n  border: 2px solidrgb(0, 0, 0);\r\n  padding: 1rem 2rem;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  width: 100%;   \r\n  max-width: 300px; \r\n}\r\n\r\n.addToCartBtn:hover:not(:disabled) {\r\n  background:rgb(91, 88, 88);\r\n  color:black;\r\n}\r\n\r\n.addToCartBtn:disabled {\r\n  background: #ccc;\r\n  border-color: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .productMain {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n  \r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productTitle {\r\n    font-size: 2rem;\r\n  }\r\n  .rightSection {\r\n    gap: 2rem;\r\n  }\r\n\r\n.keySpecsTable {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  margin-top: 20px;\r\n  font-size: 16px;\r\n  font-family: 'Arial', sans-serif;\r\n  color: #111;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.specRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: baseline;\r\n  padding: 0.75rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  font-size: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  min-width: 160px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.value {\r\n  color: #1f2937;\r\n  flex: 1;\r\n  text-align: left;\r\n}\r\n\r\n  /* .specRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  line-height: 1.4;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.value {\r\n  color: #1f2937;\r\n  flex: 1;\r\n} */\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .infoRow {\r\n    display: flex;\r\n    align-items: baseline;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .infoLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n  \r\n  .purchaseSection {\r\n    position: sticky;\r\n    bottom: 0;\r\n    background: white;\r\n    padding: 1rem;\r\n    border-top: 2px solid #1e40af;\r\n    margin: 2rem -1rem 0;\r\n  }\r\n  \r\n  .quantitySelector {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .quantityControls {\r\n    justify-content: center;\r\n  }\r\n\r\n.rightSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.keySpecsTable {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  margin-top: 20px;\r\n  font-size: 16px;\r\n  font-family: 'Arial', sans-serif;\r\n  color: #111;\r\n}\r\n\r\n  .infoLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .infoValue {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n  }\r\n  .productTitle {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .productCode {\r\n    font-size: 0.8rem;\r\n    padding: 0.5rem;\r\n  }\r\n  .specRow {\r\n    display: flex;\r\n    align-items: baseline;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n.label {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .value {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n    word-break: break-word;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;;;;AASA;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;AAYA;;;;;;;;AAgBA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;EAGA;;;;EAIF;;;;;;;;;;EAUA;;;;;;;;;;EAUA;;;;;;;EAOA;;;;;;;AA2BA;EACE;;;;;;;;EAQA;;;;;;;EAOA;;;;;;;;;EASA;;;;;;EAMA;;;;EAIF;;;;;;EAMA;;;;;;;;;EASE;;;;;;;;EAQA;;;;;;EAKA;;;;EAQA;;;;;EAIA;;;;;;;;EAQF;;;;;;;;EAQE", "debugId": null}}]}