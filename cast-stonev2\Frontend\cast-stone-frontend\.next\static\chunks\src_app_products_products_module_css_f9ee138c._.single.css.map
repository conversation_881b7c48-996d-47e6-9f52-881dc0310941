{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/products.module.css"], "sourcesContent": ["/* Products Page Styles - Magazine/Editorial Theme */\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 2rem 1rem;\r\n  min-height: 80vh;\r\n}\r\n\r\n/* Header */\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 2rem;\r\n  border-bottom: 2px solid #f3f4f6;\r\n}\r\n\r\n.headerContent {\r\n  flex: 1;\r\n}\r\n\r\n.title {\r\n  color: #4a3728;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.subtitle {\r\n  color: #6b5b4d;\r\n  font-size: 1.1rem;\r\n  margin: 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.headerActions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.filterToggle {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 0.75rem 1rem;\r\n  background: #8b7355;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.filterToggle:hover {\r\n  background: #6d5a47;\r\n}\r\n\r\n.filterIcon {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.resultsCount {\r\n  color: #6b5b4d;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Content Layout */\r\n.content {\r\n  display: grid;\r\n  grid-template-columns: 300px 1fr;\r\n  gap: 3rem;\r\n}\r\n\r\n/* Filters Sidebar */\r\n.filtersSidebar {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 2rem;\r\n}\r\n\r\n.filtersHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.filtersHeader h3 {\r\n  color: #4a3728;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0;\r\n}\r\n\r\n.clearFilters {\r\n  color: #dc2626;\r\n  background: none;\r\n  border: none;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  text-decoration: underline;\r\n}\r\n\r\n.clearFilters:hover {\r\n  color: #b91c1c;\r\n}\r\n\r\n/* Filter Groups */\r\n.filterGroup {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.filterGroup label {\r\n  display: block;\r\n  color: #4a3728;\r\n  font-weight: 600;\r\n  margin-bottom: 0.75rem;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.searchInput,\r\n.filterSelect,\r\n.priceInput {\r\n  width: 100%;\r\n  padding: 0.75rem;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 6px;\r\n  font-size: 0.9rem;\r\n  transition: border-color 0.2s ease;\r\n}\r\n\r\n.searchInput:focus,\r\n.filterSelect:focus,\r\n.priceInput:focus {\r\n  outline: none;\r\n  border-color: #8b7355;\r\n}\r\n\r\n.priceRange {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.priceRange span {\r\n  color: #6b5b4d;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.priceInput {\r\n  flex: 1;\r\n}\r\n\r\n.checkboxLabel {\r\n  display: flex !important;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  cursor: pointer;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.checkboxLabel input[type=\"checkbox\"] {\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #8b7355;\r\n}\r\n\r\n/* Products Section */\r\n.productsSection {\r\n  min-width: 0; /* Allow content to shrink */\r\n}\r\n\r\n/* Mobile Filters */\r\n@media (max-width: 1024px) {\r\n  .content {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n\r\n  .filtersSidebar {\r\n    position: static;\r\n    display: none;\r\n    order: -1;\r\n  }\r\n\r\n  .filtersSidebar.showFilters {\r\n    display: block;\r\n  }\r\n\r\n  .filterToggle {\r\n    display: flex;\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .headerActions {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .filtersSidebar {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .content {\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .headerActions {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .filterToggle {\r\n    justify-content: center;\r\n  }\r\n\r\n  .resultsCount {\r\n    text-align: center;\r\n  }\r\n\r\n  .filtersSidebar {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .filtersHeader {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .priceRange {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .priceRange span {\r\n    text-align: center;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AAWA;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;AAOA;;;;AAKA;EACE;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA"}}]}