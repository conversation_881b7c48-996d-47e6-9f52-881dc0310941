/* [project]/src/components/products/ProductCard/productCard.module.css [app-client] (css) */
.productCard-module__UIKE7W__productCard {
  background: #fff;
  border-radius: 8px;
  flex-direction: column;
  height: 100%;
  transition: transform .3s, box-shadow .3s;
  display: flex;
  overflow: hidden;
  box-shadow: 0 2px 8px #0000001a;
}

.productCard-module__UIKE7W__productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #00000026;
}

.productCard-module__UIKE7W__imageContainer {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.productCard-module__UIKE7W__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.productCard-module__UIKE7W__productCard:hover .productCard-module__UIKE7W__productImage {
  transform: scale(1.05);
}

.productCard-module__UIKE7W__outOfStockOverlay {
  color: #fff;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  position: absolute;
  inset: 0;
}

.productCard-module__UIKE7W__productInfo {
  flex-direction: column;
  flex: 1;
  padding: 1.5rem;
  display: flex;
}

.productCard-module__UIKE7W__productName {
  color: #1f2937;
  margin: 0 0 .75rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.productCard-module__UIKE7W__productDescription {
  color: #4b5563;
  flex: 1;
  margin: 0 0 1rem;
  font-size: .9rem;
  line-height: 1.5;
}

.productCard-module__UIKE7W__priceContainer {
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  display: flex;
}

.productCard-module__UIKE7W__price {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.productCard-module__UIKE7W__collection {
  color: #1e40af;
  font-size: .85rem;
  font-style: italic;
}

.productCard-module__UIKE7W__stockInfo {
  margin-bottom: 1rem;
}

.productCard-module__UIKE7W__inStock {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__outOfStock {
  color: #dc2626;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__actionButtons {
  flex-direction: column;
  gap: .75rem;
  margin-top: auto;
  display: flex;
}

.productCard-module__UIKE7W__viewDetailsBtn {
  color: #1e40af;
  background: none;
  border: 2px solid #1e40af;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
}

.productCard-module__UIKE7W__viewDetailsBtn:hover {
  color: #fff;
  background: #1e40af;
}

.productCard-module__UIKE7W__addToCartSection {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantitySelector {
  justify-content: center;
  align-items: center;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn {
  color: #1f2937;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-weight: 600;
  transition: all .2s;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #1e40af;
}

.productCard-module__UIKE7W__quantityBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.productCard-module__UIKE7W__quantity {
  text-align: center;
  color: #1f2937;
  min-width: 40px;
  font-weight: 600;
}

.productCard-module__UIKE7W__addToCartBtn {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .875rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.productCard-module__UIKE7W__addToCartBtn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.productCard-module__UIKE7W__addToCartBtn:disabled {
  opacity: .7;
  cursor: not-allowed;
  transform: none;
}

.productCard-module__UIKE7W__cartIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.productCard-module__UIKE7W__loading {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__loading:after {
  content: "";
  border: 2px solid #0000;
  border-top-color: currentColor;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: 1s linear infinite productCard-module__UIKE7W__spin;
}

@keyframes productCard-module__UIKE7W__spin {
  to {
    transform: rotate(360deg);
  }
}

@media (width <= 768px) {
  .productCard-module__UIKE7W__productCard {
    margin-bottom: 1rem;
  }

  .productCard-module__UIKE7W__imageContainer {
    height: 200px;
  }

  .productCard-module__UIKE7W__productInfo {
    padding: 1rem;
  }

  .productCard-module__UIKE7W__productName {
    font-size: 1.1rem;
  }

  .productCard-module__UIKE7W__price {
    font-size: 1.25rem;
  }

  .productCard-module__UIKE7W__actionButtons {
    gap: .5rem;
  }

  .productCard-module__UIKE7W__addToCartSection {
    flex-direction: column;
  }
}


/* [project]/src/components/products/ProductGrid/productGrid.module.css [app-client] (css) */
.productGrid-module__d3162q__productGrid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
  display: grid;
}

.productGrid-module__d3162q__loadingContainer {
  padding: 2rem 0;
}

.productGrid-module__d3162q__loadingGrid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  display: grid;
}

.productGrid-module__d3162q__loadingCard {
  background: #fff;
  border-radius: 8px;
  animation: 1.5s ease-in-out infinite productGrid-module__d3162q__pulse;
  overflow: hidden;
  box-shadow: 0 2px 8px #0000001a;
}

.productGrid-module__d3162q__loadingImage {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  width: 100%;
  height: 250px;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingContent {
  padding: 1.5rem;
}

.productGrid-module__d3162q__loadingTitle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 1.5rem;
  margin-bottom: .75rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingDescription {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  width: 80%;
  height: 1rem;
  margin-bottom: .5rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingPrice {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  width: 60%;
  height: 1.25rem;
  margin-bottom: 1rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

.productGrid-module__d3162q__loadingButton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 2.5rem;
  animation: 1.5s infinite productGrid-module__d3162q__shimmer;
}

@keyframes productGrid-module__d3162q__shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes productGrid-module__d3162q__pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.productGrid-module__d3162q__emptyContainer {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 4rem 2rem;
  display: flex;
}

.productGrid-module__d3162q__emptyIcon {
  color: #d1d5db;
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
}

.productGrid-module__d3162q__emptyIcon svg {
  width: 100%;
  height: 100%;
}

.productGrid-module__d3162q__emptyTitle {
  color: #1f2937;
  margin: 0 0 .75rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.productGrid-module__d3162q__emptyMessage {
  color: #6b5b4d;
  max-width: 400px;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

@media (width <= 1200px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (width <= 768px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
  }

  .productGrid-module__d3162q__emptyContainer {
    padding: 3rem 1rem;
  }

  .productGrid-module__d3162q__emptyIcon {
    width: 60px;
    height: 60px;
  }

  .productGrid-module__d3162q__emptyTitle {
    font-size: 1.25rem;
  }

  .productGrid-module__d3162q__emptyMessage {
    font-size: .9rem;
  }
}

@media (width <= 480px) {
  .productGrid-module__d3162q__productGrid, .productGrid-module__d3162q__loadingGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}


/* [project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-client] (css) */
.productImageGallery-module__AcqG6a__imageGallery {
  width: 100%;
}

.productImageGallery-module__AcqG6a__mainImageContainer {
  aspect-ratio: 1;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.productImageGallery-module__AcqG6a__mainImage {
  object-fit: cover;
  cursor: zoom-in;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.productImageGallery-module__AcqG6a__mainImage.productImageGallery-module__AcqG6a__zoomed {
  cursor: zoom-out;
}

.productImageGallery-module__AcqG6a__navButton {
  color: #fff;
  cursor: pointer;
  background: #000000b3;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.productImageGallery-module__AcqG6a__navButton:hover {
  background: #000000e6;
}

.productImageGallery-module__AcqG6a__prevButton {
  left: 10px;
}

.productImageGallery-module__AcqG6a__nextButton {
  right: 10px;
}

.productImageGallery-module__AcqG6a__imageCounter {
  color: #fff;
  background: #000c;
  border-radius: 0;
  padding: .5rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.productImageGallery-module__AcqG6a__zoomIndicator {
  color: #1f2937;
  background: #ffffffe6;
  border: 1px solid #ddd;
  border-radius: 0;
  align-items: center;
  gap: .5rem;
  padding: .5rem;
  font-size: .8rem;
  display: flex;
  position: absolute;
  top: 15px;
  right: 15px;
}

.productImageGallery-module__AcqG6a__thumbnailContainer {
  white-space: nowrap;
  padding: 8px 0;
  overflow: auto hidden;
}

.productImageGallery-module__AcqG6a__thumbnailGrid {
  gap: 8px;
  display: flex;
}

.productImageGallery-module__AcqG6a__thumbnail {
  cursor: pointer;
  background: none;
  border: none;
  outline: none;
  padding: 0;
  display: inline-block;
}

.productImageGallery-module__AcqG6a__thumbnailImage {
  object-fit: cover;
  border: 2px solid #0000;
  border-radius: 4px;
  width: 60px;
  height: 60px;
  transition: border .3s;
}

.productImageGallery-module__AcqG6a__activeThumbnail .productImageGallery-module__AcqG6a__thumbnailImage, .productImageGallery-module__AcqG6a__thumbnail:hover {
  border-color: #2563eb;
}

.productImageGallery-module__AcqG6a__thumbnail.productImageGallery-module__AcqG6a__activeThumbnail {
  border-width: 3px;
  border-color: #2563eb;
}

.productImageGallery-module__AcqG6a__zoomOverlay {
  z-index: 1000;
  cursor: zoom-out;
  background: #000000e6;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  inset: 0;
}

.productImageGallery-module__AcqG6a__zoomedImageContainer {
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
}

.productImageGallery-module__AcqG6a__zoomedImage {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

.productImageGallery-module__AcqG6a__closeZoomButton {
  color: #1f2937;
  cursor: pointer;
  background: #ffffffe6;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: background-color .3s;
  display: flex;
  position: absolute;
  top: -50px;
  right: 0;
}

.productImageGallery-module__AcqG6a__closeZoomButton:hover {
  background: #fff;
}

@media (width <= 768px) {
  .productImageGallery-module__AcqG6a__navButton {
    width: 40px;
    height: 40px;
  }

  .productImageGallery-module__AcqG6a__prevButton {
    left: 5px;
  }

  .productImageGallery-module__AcqG6a__nextButton {
    right: 5px;
  }

  .productImageGallery-module__AcqG6a__imageCounter {
    padding: .25rem .75rem;
    font-size: .8rem;
    bottom: 10px;
  }

  .productImageGallery-module__AcqG6a__zoomIndicator {
    padding: .25rem;
    font-size: .7rem;
    top: 10px;
    right: 10px;
  }

  .productImageGallery-module__AcqG6a__thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: .25rem;
  }

  .productImageGallery-module__AcqG6a__zoomedImageContainer {
    max-width: 95vw;
    max-height: 95vh;
  }

  .productImageGallery-module__AcqG6a__closeZoomButton {
    width: 35px;
    height: 35px;
    top: -40px;
  }
}

@media (width <= 480px) {
  .productImageGallery-module__AcqG6a__mainImageContainer {
    margin-bottom: .5rem;
  }

  .productImageGallery-module__AcqG6a__navButton {
    width: 35px;
    height: 35px;
  }

  .productImageGallery-module__AcqG6a__thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  }
}


/* [project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-client] (css) */
.productSpecifications-module__J4NrIa__specificationsContainer {
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 15rem;
}

.productSpecifications-module__J4NrIa__section {
  background: #fff;
  border: 0 solid #ddd;
  border-radius: 0;
  margin-bottom: 1rem;
}

.productSpecifications-module__J4NrIa__sectionHeader {
  text-align: left;
  cursor: pointer;
  color: #0e0e0e;
  background: #f5f5f5;
  border: none;
  border-bottom: 1px solid #ddd;
  border-radius: 0;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  transition: background-color .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__sectionHeader:hover {
  background: #eee;
}

.productSpecifications-module__J4NrIa__sectionHeader.productSpecifications-module__J4NrIa__active {
  color: #000;
  background: #f5f5f5;
}

.productSpecifications-module__J4NrIa__toggleIcon {
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform .3s;
}

.productSpecifications-module__J4NrIa__sectionContent {
  border-top: 1px solid #ddd;
  padding: 2rem;
}

.productSpecifications-module__J4NrIa__specGrid {
  background: #fff;
  width: 100%;
  margin-top: 1rem;
  display: block;
}

.productSpecifications-module__J4NrIa__specRow {
  border-bottom: 1px solid #e5e7eb;
  align-items: baseline;
  padding: .5rem 0;
  line-height: 1.4;
  display: flex;
}

.productSpecifications-module__J4NrIa__specRow:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__specLabel {
  color: #1f2937;
  flex-shrink: 0;
  margin-right: .5rem;
  font-size: 1rem;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__specValue {
  color: #1f2937;
  flex: 1;
  font-size: 1rem;
}

.productSpecifications-module__J4NrIa__specValue.productSpecifications-module__J4NrIa__inStock {
  color: #28a745;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__specValue.productSpecifications-module__J4NrIa__outOfStock {
  color: #dc3545;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__tagContainer {
  flex-wrap: wrap;
  gap: .5rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__tag {
  color: #fff;
  background: #1e40af;
  border: 1px solid #1e40af;
  border-radius: 0;
  padding: .25rem .75rem;
  font-size: .8rem;
}

.productSpecifications-module__J4NrIa__detailsContent {
  line-height: 1.6;
}

.productSpecifications-module__J4NrIa__description {
  color: #555;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.productSpecifications-module__J4NrIa__featureList h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications-module__J4NrIa__featureList ul {
  padding: 0;
  list-style: none;
}

.productSpecifications-module__J4NrIa__featureList li {
  border-bottom: 1px solid #f0f0f0;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.productSpecifications-module__J4NrIa__featureList li:before {
  content: "✓";
  color: #1e40af;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.productSpecifications-module__J4NrIa__featureList li:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__careContent {
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  display: grid;
}

.productSpecifications-module__J4NrIa__careSection h4, .productSpecifications-module__J4NrIa__downloadSection h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications-module__J4NrIa__careSection ul {
  padding: 0;
  list-style: none;
}

.productSpecifications-module__J4NrIa__careSection li {
  border-bottom: 1px solid #f0f0f0;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.productSpecifications-module__J4NrIa__careSection li:before {
  content: "•";
  color: #1e40af;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.productSpecifications-module__J4NrIa__careSection li:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__downloadLinks {
  flex-direction: column;
  gap: 1rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__downloadLink {
  color: #1e40af;
  border: 2px solid #1e40af;
  border-radius: 0;
  align-items: center;
  gap: .75rem;
  padding: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__downloadLink:hover {
  color: #fff;
  background: #1e40af;
}

.productSpecifications-module__J4NrIa__shareSection {
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareLabel {
  color: #1e40af;
  font-size: 1rem;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__shareButtons {
  gap: .75rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareButton {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareButton:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
}

@media (width <= 1024px) {
  .productSpecifications-module__J4NrIa__careContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (width <= 768px) {
  .productSpecifications-module__J4NrIa__specRow {
    grid-template-columns: 1fr;
    gap: .25rem;
  }

  .productSpecifications-module__J4NrIa__specLabel {
    font-weight: 700;
  }

  .productSpecifications-module__J4NrIa__downloadLink {
    padding: .75rem;
    font-size: .9rem;
  }

  .productSpecifications-module__J4NrIa__shareButton {
    width: 35px;
    height: 35px;
  }

  .productSpecifications-module__J4NrIa__tagContainer {
    gap: .25rem;
  }

  .productSpecifications-module__J4NrIa__tag {
    padding: .2rem .5rem;
    font-size: .75rem;
  }

  .productSpecifications-module__J4NrIa__specRow {
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: .5rem;
    flex-direction: row !important;
    display: flex !important;
  }

  .productSpecifications-module__J4NrIa__specLabel {
    flex: none;
    min-width: 100px;
    font-size: .9rem;
    font-weight: 600;
  }

  .productSpecifications-module__J4NrIa__specGrid {
    margin-top: .5rem;
    display: block;
  }

  .productSpecifications-module__J4NrIa__specRow {
    border-bottom: 1px solid #e5e7eb;
    align-items: baseline;
    padding: .4rem 0;
    line-height: 1.3;
    display: flex;
  }

  .productSpecifications-module__J4NrIa__specRow:last-child {
    border-bottom: none;
  }

  .productSpecifications-module__J4NrIa__specLabel {
    color: #1f2937;
    flex-shrink: 0;
    margin-right: .4rem;
    font-size: .9rem;
    font-weight: 600;
  }

  .productSpecifications-module__J4NrIa__specValue {
    color: #1f2937;
    word-break: break-word;
    flex: 1;
    font-size: .9rem;
  }

  .productSpecifications-module__J4NrIa__sectionHeader {
    padding: 1rem;
    font-size: 1rem;
  }

  .productSpecifications-module__J4NrIa__sectionContent {
    padding: 1rem;
  }

  .productSpecifications-module__J4NrIa__shareSection {
    text-align: center;
    flex-direction: column;
    align-items: stretch;
  }

  .productSpecifications-module__J4NrIa__shareButtons {
    justify-content: center;
  }
}


/* [project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-client] (css) */
.relatedProducts-module__zj5_XW__relatedProducts {
  border-top: 2px solid #ddd;
  margin: 4rem 0;
  padding: 2rem 0;
}

.relatedProducts-module__zj5_XW__sectionHeader {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__sectionTitle {
  color: #1f2937;
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.relatedProducts-module__zj5_XW__scrollControls {
  gap: .5rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__scrollButton {
  color: #fff;
  cursor: pointer;
  background: #2563eb;
  border: 2px solid #2563eb;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 45px;
  transition: all .3s;
  display: flex;
}

.relatedProducts-module__zj5_XW__scrollButton:hover:not(.relatedProducts-module__zj5_XW__disabled) {
  color: #2563eb;
  background: #fff;
}

.relatedProducts-module__zj5_XW__scrollButton.relatedProducts-module__zj5_XW__disabled {
  cursor: not-allowed;
  opacity: .5;
  background: #ccc;
  border-color: #ccc;
}

.relatedProducts-module__zj5_XW__productsContainer {
  scrollbar-width: none;
  -ms-overflow-style: none;
  overflow: auto hidden;
}

.relatedProducts-module__zj5_XW__productsContainer::-webkit-scrollbar {
  display: none;
}

.relatedProducts-module__zj5_XW__productsGrid {
  gap: 1.5rem;
  padding-bottom: 1rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__productCard {
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  flex: 0 0 300px;
  transition: all .3s;
  overflow: hidden;
}

.relatedProducts-module__zj5_XW__productCard:hover {
  border-color: #2563eb;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #2563eb26;
}

.relatedProducts-module__zj5_XW__productLink {
  color: inherit;
  text-decoration: none;
  display: block;
}

.relatedProducts-module__zj5_XW__imageContainer {
  aspect-ratio: 1;
  background: #f8f8f8;
  position: relative;
  overflow: hidden;
}

.relatedProducts-module__zj5_XW__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.relatedProducts-module__zj5_XW__productCard:hover .relatedProducts-module__zj5_XW__productImage {
  transform: scale(1.05);
}

.relatedProducts-module__zj5_XW__outOfStockOverlay {
  color: #fff;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  position: absolute;
  inset: 0;
}

.relatedProducts-module__zj5_XW__productInfo {
  padding: 1.5rem;
}

.relatedProducts-module__zj5_XW__productName {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.3;
}

.relatedProducts-module__zj5_XW__productDetails {
  margin-bottom: 1rem;
}

.relatedProducts-module__zj5_XW__productCode {
  color: #666;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 0;
  width: fit-content;
  margin-bottom: .75rem;
  padding: .25rem .5rem;
  font-family: Courier New, monospace;
  font-size: .85rem;
  display: block;
}

.relatedProducts-module__zj5_XW__availability {
  font-size: .9rem;
}

.relatedProducts-module__zj5_XW__inStock {
  color: #28a745;
  font-weight: 600;
}

.relatedProducts-module__zj5_XW__outOfStock {
  color: #dc3545;
  font-weight: 600;
}

.relatedProducts-module__zj5_XW__priceContainer {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.relatedProducts-module__zj5_XW__price {
  color: #1f2937;
  font-size: 1.3rem;
  font-weight: 700;
}

@media (width <= 1024px) {
  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 280px;
  }

  .relatedProducts-module__zj5_XW__sectionTitle {
    font-size: 1.75rem;
  }
}

@media (width <= 768px) {
  .relatedProducts-module__zj5_XW__relatedProducts {
    margin: 3rem 0;
  }

  .relatedProducts-module__zj5_XW__sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .relatedProducts-module__zj5_XW__scrollControls {
    justify-content: center;
  }

  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 250px;
  }

  .relatedProducts-module__zj5_XW__productInfo {
    padding: 1rem;
  }

  .relatedProducts-module__zj5_XW__productName {
    font-size: 1.1rem;
  }

  .relatedProducts-module__zj5_XW__price {
    font-size: 1.2rem;
  }

  .relatedProducts-module__zj5_XW__sectionTitle {
    text-align: center;
    font-size: 1.5rem;
  }
}

@media (width <= 480px) {
  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 220px;
  }

  .relatedProducts-module__zj5_XW__productInfo {
    padding: .75rem;
  }

  .relatedProducts-module__zj5_XW__productName {
    font-size: 1rem;
  }

  .relatedProducts-module__zj5_XW__price {
    font-size: 1.1rem;
  }

  .relatedProducts-module__zj5_XW__productCode {
    padding: .2rem .4rem;
    font-size: .8rem;
  }

  .relatedProducts-module__zj5_XW__scrollButton {
    width: 40px;
    height: 40px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .relatedProducts-module__zj5_XW__productCard {
    transition: none;
  }

  .relatedProducts-module__zj5_XW__productCard:hover {
    transform: none;
  }

  .relatedProducts-module__zj5_XW__productImage {
    transition: none;
  }

  .relatedProducts-module__zj5_XW__productCard:hover .relatedProducts-module__zj5_XW__productImage {
    transform: none;
  }
}


/* [project]/src/app/products/products.module.css [app-client] (css) */
.products-module__E8alaG__container {
  max-width: 1400px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 6rem 1rem 2rem;
}

.products-module__E8alaG__header {
  border-bottom: 2px solid #f3f4f6;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  display: flex;
}

.products-module__E8alaG__headerContent {
  flex: 1;
}

.products-module__E8alaG__title {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.products-module__E8alaG__subtitle {
  color: #4b5563;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.5;
}

.products-module__E8alaG__headerActions {
  align-items: center;
  gap: 1.5rem;
  display: flex;
}

.products-module__E8alaG__filterToggle {
  color: #fff;
  cursor: pointer;
  background: #2563eb;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: .75rem 1rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.products-module__E8alaG__filterToggle:hover {
  background: #1d4ed8;
}

.products-module__E8alaG__filterIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.products-module__E8alaG__resultsCount {
  color: #4b5563;
  white-space: nowrap;
  font-size: .9rem;
  font-weight: 600;
}

.products-module__E8alaG__content {
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  display: grid;
}

.products-module__E8alaG__filtersSidebar {
  background: #fff;
  border-radius: 8px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.products-module__E8alaG__filtersHeader {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  display: flex;
}

.products-module__E8alaG__filtersHeader h3 {
  color: #1f2937;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

.products-module__E8alaG__clearFilters {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: underline;
}

.products-module__E8alaG__clearFilters:hover {
  color: #b91c1c;
}

.products-module__E8alaG__filterGroup {
  margin-bottom: 2rem;
}

.products-module__E8alaG__filterGroup label {
  color: #1f2937;
  margin-bottom: .75rem;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.products-module__E8alaG__searchInput, .products-module__E8alaG__filterSelect, .products-module__E8alaG__priceInput {
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: .9rem;
  transition: border-color .2s;
}

.products-module__E8alaG__searchInput:focus, .products-module__E8alaG__filterSelect:focus, .products-module__E8alaG__priceInput:focus {
  border-color: #2563eb;
  outline: none;
}

.products-module__E8alaG__priceRange {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.products-module__E8alaG__priceRange span {
  color: #4b5563;
  font-size: .9rem;
  font-weight: 500;
}

.products-module__E8alaG__priceInput {
  flex: 1;
}

.products-module__E8alaG__checkboxLabel {
  cursor: pointer;
  align-items: center;
  gap: .5rem;
  margin-bottom: 0 !important;
  display: flex !important;
}

.products-module__E8alaG__checkboxLabel input[type="checkbox"] {
  accent-color: #2563eb;
  width: 18px;
  height: 18px;
}

.products-module__E8alaG__productsSection {
  min-width: 0;
}

@media (width <= 1024px) {
  .products-module__E8alaG__content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .products-module__E8alaG__filtersSidebar {
    order: -1;
    display: none;
    position: static;
  }

  .products-module__E8alaG__filtersSidebar.products-module__E8alaG__showFilters {
    display: block;
  }

  .products-module__E8alaG__filterToggle {
    display: flex;
  }
}

@media (width <= 768px) {
  .products-module__E8alaG__container {
    padding: 1rem;
  }

  .products-module__E8alaG__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .products-module__E8alaG__headerActions {
    justify-content: space-between;
    width: 100%;
  }

  .products-module__E8alaG__title {
    font-size: 2rem;
  }

  .products-module__E8alaG__subtitle {
    font-size: 1rem;
  }

  .products-module__E8alaG__filtersSidebar {
    padding: 1.5rem;
  }

  .products-module__E8alaG__content {
    gap: 1.5rem;
  }
}

@media (width <= 480px) {
  .products-module__E8alaG__container {
    padding: .5rem;
  }

  .products-module__E8alaG__title {
    font-size: 1.75rem;
  }

  .products-module__E8alaG__headerActions {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .products-module__E8alaG__filterToggle {
    justify-content: center;
  }

  .products-module__E8alaG__resultsCount {
    text-align: center;
  }

  .products-module__E8alaG__filtersSidebar {
    padding: 1rem;
  }

  .products-module__E8alaG__filtersHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .products-module__E8alaG__priceRange {
    flex-direction: column;
    align-items: stretch;
  }

  .products-module__E8alaG__priceRange span {
    text-align: center;
  }
}


/*# sourceMappingURL=src_8f303ca7._.css.map*/