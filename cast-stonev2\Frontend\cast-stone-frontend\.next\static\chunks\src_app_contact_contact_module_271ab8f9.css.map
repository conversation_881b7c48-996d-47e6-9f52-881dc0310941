{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/contact/contact.module.css"], "sourcesContent": ["/* Contact Page Styles - GuardianJet Inspired Design */\r\n.contactPage {\r\n  --navy-blue:hsl(224, 64.30%, 32.90%);\r\n  --dark-navy: #1e40af;\r\n  --light-navy: #3b82f6;\r\n  --white: #ffffff;\r\n  --light-gray: #f8fafc;\r\n  --medium-gray: #64748b;\r\n  --dark-gray: #334155;\r\n  --border-gray: #e2e8f0;\r\n  --shadow: rgba(30, 58, 138, 0.1);\r\n  --shadow-hover: rgba(30, 58, 138, 0.15);\r\n  --transition: all 0.3s ease;\r\n\r\n  min-height: 100vh;\r\n  background-color: var(--white);\r\n  font-family: 'Helvetica Neue', Arial, sans-serif;\r\n}\r\n\r\n/* Main Content */\r\n.mainContent {\r\n  padding: 2rem 0;\r\n  background-color: var(--white);\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.contentGrid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 3rem;\r\n  align-items: start;\r\n  min-height: 80vh;\r\n}\r\n\r\n/* Left Side - Info Section */\r\n.infoSection {\r\n  padding: 2rem 0;\r\n}\r\n\r\n.heroText {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.heroTitle {\r\n  font-size: 1.5rem;\r\n  font-weight: 400;\r\n  color: var(--dark-gray);\r\n  line-height: 1.6;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n/* Contact Cards */\r\n.contactCards {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.contactCard {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1rem;\r\n  background: var(--light-gray);\r\n  border: 1px solid var(--border-gray);\r\n  border-radius: 8px;\r\n}\r\n\r\n.cardIcon {\r\n  width: 24px;\r\n  height: 24px;\r\n  color: var(--navy-blue);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.cardContent {\r\n  flex: 1;\r\n}\r\n\r\n.cardTitle {\r\n  font-weight: 600;\r\n  color: var(--dark-gray);\r\n  margin: 0;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.cardSubtitle {\r\n  color: var(--medium-gray);\r\n  margin: 0;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n/* Company Description */\r\n.companyDescription {\r\n  margin-top: 2rem;\r\n}\r\n\r\n.descriptionText {\r\n  color: var(--dark-gray);\r\n  line-height: 1.6;\r\n  margin-bottom: 1rem;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.descriptionText:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Right Side - Form Section */\r\n.formSection {\r\n  padding: 2rem 0;\r\n}\r\n\r\n.formCard {\r\n  background: var(--light-gray);\r\n  border-radius: 8px;\r\n  padding: 2rem;\r\n  border: 1px solid var(--border-gray);\r\n}\r\n\r\n.formTitle {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--dark-gray);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.requiredField {\r\n  color: var(--medium-gray);\r\n  font-size: 0.9rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n/* Form Styles */\r\n.contactForm {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.25rem;\r\n}\r\n\r\n.formGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.label {\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  color: var(--dark-gray);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.input,\r\n.select,\r\n.textarea {\r\n  font-size: 0.95rem;\r\n  padding: 0.75rem 1rem;\r\n  border: 1px solid var(--border-gray);\r\n  border-radius: 4px;\r\n  background-color: var(--white);\r\n  color: var(--dark-gray);\r\n  transition: var(--transition);\r\n  outline: none;\r\n  width: 100%;\r\n}\r\n\r\n.input:focus,\r\n.select:focus,\r\n.textarea:focus {\r\n  border-color: var(--navy-blue);\r\n  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);\r\n}\r\n\r\n.textarea {\r\n  resize: vertical;\r\n  min-height: 100px;\r\n  font-family: inherit;\r\n}\r\n\r\n.textarea::placeholder {\r\n  color: var(--medium-gray);\r\n  opacity: 0.8;\r\n}\r\n\r\n.select {\r\n  cursor: pointer;\r\n  background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23334155\" stroke-width=\"2\"><polyline points=\"6,9 12,15 18,9\"/></svg>');\r\n  background-repeat: no-repeat;\r\n  background-position: right 0.75rem center;\r\n  background-size: 16px;\r\n  appearance: none;\r\n}\r\n\r\n/* Disclaimer */\r\n.disclaimer {\r\n  margin: 1rem 0;\r\n}\r\n\r\n.disclaimerText {\r\n  font-size: 0.8rem;\r\n  color: var(--medium-gray);\r\n  line-height: 1.4;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.disclaimerText:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Submit Button */\r\n.submitButton {\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  padding: 0.875rem 2rem;\r\n  background: var(--navy-blue);\r\n  color: var(--white);\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: var(--transition);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  margin-top: 0.5rem;\r\n  align-self: flex-start;\r\n}\r\n\r\n.submitButton:hover:not(:disabled) {\r\n  background: #c19660;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);\r\n}\r\n\r\n.submitButton:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.submitting {\r\n  background: var(--medium-gray) !important;\r\n}\r\n\r\n/* Submit Message */\r\n.submitMessage {\r\n  padding: 0.75rem 1rem;\r\n  border-radius: 4px;\r\n  margin-bottom: 1rem;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  border: 1px solid;\r\n}\r\n\r\n.submitMessage.success {\r\n  background-color: #d4edda;\r\n  color: #155724;\r\n  border-color: #c3e6cb;\r\n}\r\n\r\n.submitMessage.error {\r\n  background-color: #f8d7da;\r\n  color: #721c24;\r\n  border-color: #f5c6cb;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .contentGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n\r\n  .infoSection {\r\n    order: 2;\r\n  }\r\n\r\n  .formSection {\r\n    order: 1;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .mainContent {\r\n    padding: 1rem 0;\r\n  }\r\n\r\n  .formCard {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .heroTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .contactCards {\r\n    gap: 0.75rem;\r\n  }\r\n\r\n  .contactCard {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .descriptionText {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .formCard {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .heroTitle {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .formTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .input,\r\n  .select,\r\n  .textarea {\r\n    padding: 0.625rem 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .submitButton {\r\n    padding: 0.75rem 1.5rem;\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAmBA;;;;;AAKA;;;;;;AAMA;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;AAcA;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAOA", "debugId": null}}]}