/* [project]/src/app/products/[id]/productPage.module.css [app-client] (css) */
.productPage-module__Rcy5XW__productPage {
  background: #fff;
  min-height: 100vh;
  padding-top: 6rem;
}

.productPage-module__Rcy5XW__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.productPage-module__Rcy5XW__loadingContainer {
  color: #1e40af;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  display: flex;
}

.productPage-module__Rcy5XW__loadingSpinner {
  border: 3px solid #f3f3f3;
  border-top-color: #1e40af;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite productPage-module__Rcy5XW__spin;
}

@keyframes productPage-module__Rcy5XW__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.productPage-module__Rcy5XW__errorContainer {
  text-align: center;
  color: #1e40af;
  padding: 4rem 2rem;
}

.productPage-module__Rcy5XW__errorContainer h1 {
  margin-bottom: 1rem;
  font-size: 2rem;
}

.productPage-module__Rcy5XW__productMain {
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  display: grid;
}

.productPage-module__Rcy5XW__imageSection {
  position: relative;
}

.productPage-module__Rcy5XW__detailsSection {
  padding: 2rem 0;
}

.productPage-module__Rcy5XW__productTitle {
  color: #1a1a1a;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.productPage-module__Rcy5XW__productCode {
  color: #000;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 0;
  margin-bottom: 2rem;
  padding: .4rem 1rem;
  font-family: Courier New, monospace;
  font-size: .9rem;
  display: inline-block;
}

.productPage-module__Rcy5XW__productInfo {
  margin-bottom: 2rem;
}

.productPage-module__Rcy5XW__infoRow {
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: .75rem;
  margin-bottom: .75rem;
  display: flex;
}

.productPage-module__Rcy5XW__infoLabel {
  color: #000;
  min-width: 100px;
  font-size: .95rem;
  font-weight: 600;
}

.productPage-module__Rcy5XW__infoValue {
  color: #333;
  font-size: .95rem;
  font-weight: 500;
}

.productPage-module__Rcy5XW__priceSection {
  background: #fff;
  border-radius: 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  display: flex;
}

.productPage-module__Rcy5XW__priceRow {
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
  width: 100%;
  display: flex;
}

.productPage-module__Rcy5XW__price {
  color: #000;
  background: #f5f5f5;
  padding: 6px;
  font-size: 2rem;
  font-weight: 520;
}

.productPage-module__Rcy5XW__purchaseSection {
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
  display: flex;
}

.productPage-module__Rcy5XW__quantitySelector {
  align-items: center;
  gap: 2rem;
  display: flex;
}

.productPage-module__Rcy5XW__quantitySelector label {
  color: #000;
  font-weight: 600;
}

.productPage-module__Rcy5XW__quantityControls {
  border: 2px solidrgb(0, 0, 0);
  border-radius: 0;
  align-items: center;
  display: flex;
  overflow: hidden;
}

.productPage-module__Rcy5XW__quantityBtn {
  color: #000;
  cursor: pointer;
  background: #f5f5f5;
  border: #000;
  padding: .75rem 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  transition: background-color .2s;
}

.productPage-module__Rcy5XW__quantityBtn:hover:not(:disabled) {
  background: #1e40af;
}

.productPage-module__Rcy5XW__quantityBtn:disabled {
  cursor: not-allowed;
  background: #ccc;
}

.productPage-module__Rcy5XW__quantityInput {
  text-align: center;
  color: #000;
  background: #fff;
  border: none;
  outline: none;
  width: 80px;
  padding: .75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.productPage-module__Rcy5XW__addToCartRow {
  grid-template-columns: 150px 1fr;
  align-items: center;
  width: 100%;
  display: grid;
}

.productPage-module__Rcy5XW__addToCartLabel {
  width: 400%;
}

.productPage-module__Rcy5XW__addToCartWrapper {
  justify-content: center;
  width: 100%;
  display: flex;
}

.productPage-module__Rcy5XW__addToCartBtn {
  color: #000;
  border: 2px solidrgb(0, 0, 0);
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: .5px;
  background: #f5f5f5;
  border-radius: 0;
  width: 100%;
  max-width: 300px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.productPage-module__Rcy5XW__addToCartBtn:hover:not(:disabled) {
  color: #000;
  background: #5b5858;
}

.productPage-module__Rcy5XW__addToCartBtn:disabled {
  cursor: not-allowed;
  background: #ccc;
  border-color: #ccc;
}

@media (width <= 1024px) {
  .productPage-module__Rcy5XW__productMain {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .productPage-module__Rcy5XW__container {
    padding: 1rem;
  }

  .productPage-module__Rcy5XW__productTitle {
    font-size: 2rem;
  }

  .productPage-module__Rcy5XW__rightSection {
    gap: 2rem;
  }

  .productPage-module__Rcy5XW__specRow {
    border-bottom: 1px solid #ccc;
    justify-content: space-between;
    padding: 12px 0;
    display: flex;
  }

  .productPage-module__Rcy5XW__label {
    flex: 1;
    font-weight: 700;
  }

  .productPage-module__Rcy5XW__value {
    text-align: right;
    color: #222;
    flex: 1;
  }
}

@media (width <= 768px) {
  .productPage-module__Rcy5XW__infoRow {
    grid-template-columns: 1fr;
    gap: .25rem;
  }

  .productPage-module__Rcy5XW__infoLabel {
    font-weight: 700;
  }

  .productPage-module__Rcy5XW__purchaseSection {
    background: #fff;
    border-top: 2px solid #1e40af;
    margin: 2rem -1rem 0;
    padding: 1rem;
    position: sticky;
    bottom: 0;
  }

  .productPage-module__Rcy5XW__quantitySelector {
    flex-direction: column;
    align-items: stretch;
    gap: .5rem;
  }

  .productPage-module__Rcy5XW__quantityControls {
    justify-content: center;
  }

  .productPage-module__Rcy5XW__rightSection {
    flex-direction: column;
    gap: 2rem;
    display: flex;
  }

  .productPage-module__Rcy5XW__keySpecsTable {
    color: #111;
    border-top: 1px solid #ccc;
    width: 100%;
    max-width: 600px;
    margin-top: 20px;
    font-family: Arial, sans-serif;
    font-size: 14px;
  }

  .productPage-module__Rcy5XW__infoLabel {
    min-width: 80px;
    font-size: .9rem;
  }

  .productPage-module__Rcy5XW__infoValue {
    font-size: .9rem;
  }

  .productPage-module__Rcy5XW__productTitle, .productPage-module__Rcy5XW__price {
    font-size: 1.5rem;
  }

  .productPage-module__Rcy5XW__productCode {
    padding: .5rem;
    font-size: .8rem;
  }

  .productPage-module__Rcy5XW__specRow {
    flex-wrap: wrap;
    align-items: flex-start;
    gap: .5rem;
    flex-direction: row !important;
  }

  .productPage-module__Rcy5XW__label {
    flex: none;
    font-size: .85rem;
    font-weight: 600;
  }

  .productPage-module__Rcy5XW__value {
    text-align: left;
    word-break: break-word;
    flex: auto;
    font-size: .85rem;
    margin-top: 0 !important;
  }
}

/*# sourceMappingURL=src_app_products_%5Bid%5D_productPage_module_css_f9ee138c._.single.css.map*/