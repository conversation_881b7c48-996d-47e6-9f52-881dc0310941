/* [project]/src/components/Home/CatalogBanner/catalogBanner.module.css [app-client] (css) */
.catalogBanner-module__6ToAlG__catalogBanner {
  background: #1e3a8a;
  margin: 4rem 0;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.catalogBanner-module__6ToAlG__backgroundContainer {
  z-index: 1;
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__backgroundImage {
  object-fit: cover;
  object-position: center;
}

.catalogBanner-module__6ToAlG__backgroundOverlay {
  z-index: 2;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__container {
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.catalogBanner-module__6ToAlG__content {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.catalogBanner-module__6ToAlG__textContent {
  color: #fff;
}

.catalogBanner-module__6ToAlG__subtitle {
  text-transform: uppercase;
  letter-spacing: .15em;
  color: #white;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.catalogBanner-module__6ToAlG__title {
  color: #fff;
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.catalogBanner-module__6ToAlG__description {
  color: #ffffffe6;
  margin-bottom: 2.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.catalogBanner-module__6ToAlG__features {
  gap: 2rem;
  margin-bottom: 2rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__feature {
  color: #ffffffe6;
  align-items: center;
  gap: .75rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  display: flex;
}

.catalogBanner-module__6ToAlG__featureIcon {
  width: 40px;
  height: 40px;
  color: #white;
  background: #d4af8c33;
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  display: flex;
}

.catalogBanner-module__6ToAlG__ctaContainer {
  flex-direction: column;
  align-items: flex-start;
  gap: 2rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__ctaButton {
  background: linear-gradient(135deg, #white, #f5f4f3);
  color: #white;
  text-transform: uppercase;
  letter-spacing: .05em;
  border-radius: 50px;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #8e8b894d;
}

.catalogBanner-module__6ToAlG__ctaButton:hover {
  background: linear-gradient(135deg, #white, #white);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px #d4af8c66;
}

.catalogBanner-module__6ToAlG__ctaText {
  z-index: 2;
  position: relative;
}

.catalogBanner-module__6ToAlG__ctaIcon {
  z-index: 2;
  transition: transform .3s;
  position: relative;
}

.catalogBanner-module__6ToAlG__ctaButton:hover .catalogBanner-module__6ToAlG__ctaIcon {
  transform: translateX(4px);
}

.catalogBanner-module__6ToAlG__buttonRipple {
  z-index: 1;
  background: radial-gradient(circle, #ffffff4d 0%, #0000 70%);
  width: 100%;
  height: 100%;
  transition: transform .6s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0);
}

.catalogBanner-module__6ToAlG__ctaButton:active .catalogBanner-module__6ToAlG__buttonRipple {
  transform: scale(1);
}

.catalogBanner-module__6ToAlG__catalogStats {
  gap: 3rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__stat {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.catalogBanner-module__6ToAlG__statNumber {
  color: #white;
  margin-bottom: .25rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.catalogBanner-module__6ToAlG__statLabel {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 500;
}

.catalogBanner-module__6ToAlG__decorativeElements {
  z-index: 2;
  pointer-events: none;
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__decorativeCircle {
  border: 2px solid #d4af8c33;
  border-radius: 50%;
  width: 200px;
  height: 200px;
  animation: 6s ease-in-out infinite catalogBanner-module__6ToAlG__float;
  position: absolute;
  top: 20%;
  right: 10%;
}

.catalogBanner-module__6ToAlG__decorativeLine {
  background: linear-gradient(90deg, #0000, #d4af8c4d, #0000);
  width: 150px;
  height: 2px;
  animation: 8s ease-in-out infinite catalogBanner-module__6ToAlG__slide;
  position: absolute;
  bottom: 20%;
  left: 5%;
}

@keyframes catalogBanner-module__6ToAlG__float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes catalogBanner-module__6ToAlG__slide {
  0%, 100% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(50px);
  }
}

@media (width <= 1024px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    padding: 6rem 0;
  }

  .catalogBanner-module__6ToAlG__content {
    gap: 3rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 3rem;
  }

  .catalogBanner-module__6ToAlG__features {
    gap: 1.5rem;
  }
}

@media (width <= 768px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    margin: 3rem 0;
    padding: 4rem 0;
  }

  .catalogBanner-module__6ToAlG__container {
    padding: 0 1.5rem;
  }

  .catalogBanner-module__6ToAlG__content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 2.5rem;
  }

  .catalogBanner-module__6ToAlG__description {
    font-size: 1rem;
  }

  .catalogBanner-module__6ToAlG__features {
    justify-content: center;
    gap: 1rem;
  }

  .catalogBanner-module__6ToAlG__ctaContainer {
    align-items: center;
  }

  .catalogBanner-module__6ToAlG__catalogStats {
    gap: 2rem;
  }
}

@media (width <= 480px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    padding: 3rem 0;
  }

  .catalogBanner-module__6ToAlG__container {
    padding: 0 1rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 2rem;
  }

  .catalogBanner-module__6ToAlG__features {
    flex-direction: column;
    gap: 1rem;
  }

  .catalogBanner-module__6ToAlG__feature {
    justify-content: center;
  }

  .catalogBanner-module__6ToAlG__ctaButton {
    padding: 1rem 2rem;
    font-size: .9rem;
  }

  .catalogBanner-module__6ToAlG__catalogStats {
    gap: 1.5rem;
  }

  .catalogBanner-module__6ToAlG__statNumber {
    font-size: 2rem;
  }
}

/*# sourceMappingURL=src_components_Home_CatalogBanner_catalogBanner_module_css_f9ee138c._.single.css.map*/