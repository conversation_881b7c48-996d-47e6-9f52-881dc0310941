{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductSpecifications/productSpecifications.module.css"], "sourcesContent": ["/* Product Specifications - Sharp Rectangular Design */\r\n.specificationsContainer {\r\n  /* margin: 3rem 0; */\r\n  /* margin: 2rem 0 0 0 auto;\r\n  width: 100%;\r\n  max-width: 40vw; */\r\n\r\n  margin-top: 2rem;\r\n  /* margin-left: auto; */\r\n  width: 100%;\r\n  /* max-width: 35vw; */\r\n  /* margin-top: -20rem; */\r\n  margin-bottom: 15rem;\r\n}\r\n\r\n/* Section Styles */\r\n.section {\r\n  border: 0px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  margin-bottom: 1rem;\r\n  background: white;\r\n}\r\n\r\n.sectionHeader {\r\n  width: 100%;\r\n  background: #f5f5f5;\r\n  border: none;\r\n  border-bottom: 1px solid #ddd;\r\n  padding: 1.5rem;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color:rgb(14, 14, 14);\r\n  transition: background-color 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.sectionHeader:hover {\r\n  background: #eeeeee;\r\n}\r\n\r\n.sectionHeader.active {\r\n    /* background: #f5f5f5; */\r\n  background: #f5f5f5;\r\n  color: black;\r\n}\r\n\r\n.toggleIcon {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sectionContent {\r\n  padding: 2rem;\r\n  border-top: 1px solid #ddd;\r\n}\r\n\r\n/* Specifications Grid - Table Style */\r\n.specGrid {\r\n  display: table;\r\n  width: 100%;\r\n  border-collapse: separate;\r\n  border-spacing: 0;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.specRow {\r\n  display: table-row;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.specRow:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.specLabel {\r\n  display: table-cell;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 0.95rem;\r\n  padding: 1rem 1.5rem;\r\n  background: #f9fafb;\r\n  border-right: 1px solid #e5e7eb;\r\n  vertical-align: middle;\r\n  width: 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n.specValue {\r\n  display: table-cell;\r\n  color: #374151;\r\n  font-size: 0.95rem;\r\n  padding: 1rem 1.5rem;\r\n  background: white;\r\n  vertical-align: middle;\r\n}\r\n\r\n.specValue.inStock {\r\n  color: #28a745;\r\n  font-weight: 600;\r\n}\r\n\r\n.specValue.outOfStock {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Tags */\r\n.tagContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.tag {\r\n  background: #1e40af;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  font-size: 0.8rem;\r\n  border-radius: 0; /* Sharp corners */\r\n  border: 1px solid #1e40af;\r\n}\r\n\r\n/* Details Content */\r\n.detailsContent {\r\n  line-height: 1.6;\r\n}\r\n\r\n.description {\r\n  color: #555;\r\n  margin-bottom: 2rem;\r\n  font-size: 1rem;\r\n}\r\n\r\n.featureList h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.featureList ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.featureList li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.featureList li:before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.featureList li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Care Content */\r\n.careContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 3rem;\r\n}\r\n\r\n.careSection h4,\r\n.downloadSection h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.careSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.careSection li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.careSection li:before {\r\n  content: '•';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.careSection li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Download Links */\r\n.downloadLinks {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.downloadLink {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem;\r\n  border: 2px solid #1e40af;\r\n  color: #1e40af;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-weight: 500;\r\n}\r\n\r\n.downloadLink:hover {\r\n  background: #1e40af;\r\n  color: white;\r\n}\r\n\r\n/* Share Section */\r\n.shareSection {\r\n  margin-top: 2rem;\r\n  padding: 1.5rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  background: #f8f8f8;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.shareLabel {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  font-size: 1rem;\r\n}\r\n\r\n.shareButtons {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.shareButton {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.shareButton:hover {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .careContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .specRow {\r\n    grid-template-columns: 1fr;\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .specLabel {\r\n    font-weight: 700;\r\n  }\r\n  \r\n  .sectionHeader {\r\n    padding: 1rem;\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .sectionContent {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .shareSection {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    text-align: center;\r\n  }\r\n  \r\n  .shareButtons {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n  @media (max-width: 768px) {\r\n\r\n  .downloadLink {\r\n    padding: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .shareButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .tagContainer {\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .tag {\r\n    font-size: 0.75rem;\r\n    padding: 0.2rem 0.5rem;\r\n  }\r\n  .specRow {\r\n    display: flex !important;\r\n    flex-direction: row !important;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .specLabel {\r\n    font-weight: 600;\r\n    flex: 0 0 auto;\r\n    min-width: 100px;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  /* Mobile responsive table layout */\r\n  .specGrid {\r\n    display: block;\r\n    border: 1px solid #e5e7eb;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .specRow {\r\n    display: flex;\r\n    flex-direction: row;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    min-height: 60px;\r\n    align-items: center;\r\n  }\r\n\r\n  .specRow:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .specLabel {\r\n    display: flex;\r\n    align-items: center;\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    padding: 1rem;\r\n    background: #f9fafb;\r\n    border-right: 1px solid #e5e7eb;\r\n    width: 140px;\r\n    min-width: 140px;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .specValue {\r\n    display: flex;\r\n    align-items: center;\r\n    color: #374151;\r\n    font-size: 0.9rem;\r\n    padding: 1rem;\r\n    background: white;\r\n    flex: 1;\r\n    word-break: break-word;\r\n  }\r\n\r\n  .sectionHeader {\r\n    padding: 1rem;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .sectionContent {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .shareSection {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    text-align: center;\r\n  }\r\n\r\n  .shareButtons {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AACA;;;;;;AAeA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;EA2BA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;;;;EASA;;;;;;;EAQA;;;;;;;EAOA;;;;;;;;EAQA;;;;EAIA;;;;;;;;;;;;;;EAcA;;;;;;;;;;;EAWA;;;;;EAKA;;;;EAIA;;;;;;EAMA"}}]}