{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartItem/cartItem.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cartItem\": \"cartItem-module__IXYF0W__cartItem\",\n  \"collection\": \"cartItem-module__IXYF0W__collection\",\n  \"imageContainer\": \"cartItem-module__IXYF0W__imageContainer\",\n  \"inStock\": \"cartItem-module__IXYF0W__inStock\",\n  \"itemTotal\": \"cartItem-module__IXYF0W__itemTotal\",\n  \"outOfStock\": \"cartItem-module__IXYF0W__outOfStock\",\n  \"priceBreakdown\": \"cartItem-module__IXYF0W__priceBreakdown\",\n  \"priceSection\": \"cartItem-module__IXYF0W__priceSection\",\n  \"productDescription\": \"cartItem-module__IXYF0W__productDescription\",\n  \"productDetails\": \"cartItem-module__IXYF0W__productDetails\",\n  \"productImage\": \"cartItem-module__IXYF0W__productImage\",\n  \"productMeta\": \"cartItem-module__IXYF0W__productMeta\",\n  \"productName\": \"cartItem-module__IXYF0W__productName\",\n  \"quantity\": \"cartItem-module__IXYF0W__quantity\",\n  \"quantityBtn\": \"cartItem-module__IXYF0W__quantityBtn\",\n  \"quantityControls\": \"cartItem-module__IXYF0W__quantityControls\",\n  \"quantityLabel\": \"cartItem-module__IXYF0W__quantityLabel\",\n  \"quantitySection\": \"cartItem-module__IXYF0W__quantitySection\",\n  \"removeBtn\": \"cartItem-module__IXYF0W__removeBtn\",\n  \"removeSection\": \"cartItem-module__IXYF0W__removeSection\",\n  \"removing\": \"cartItem-module__IXYF0W__removing\",\n  \"stockStatus\": \"cartItem-module__IXYF0W__stockStatus\",\n  \"unitPrice\": \"cartItem-module__IXYF0W__unitPrice\",\n  \"updating\": \"cartItem-module__IXYF0W__updating\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartItem/CartItem.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { CartItem as CartItemType } from '@/services/types/entities';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport styles from './cartItem.module.css';\r\n\r\ninterface CartItemProps {\r\n  item: CartItemType;\r\n}\r\n\r\nconst CartItem: React.FC<CartItemProps> = ({ item }) => {\r\n  const { updateCartItem, removeFromCart, state } = useCart();\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [isRemoving, setIsRemoving] = useState(false);\r\n\r\n  const handleQuantityChange = async (newQuantity: number) => {\r\n    if (newQuantity < 1 || !item.product) return;\r\n    \r\n    if (newQuantity > item.product.stock) {\r\n      alert(`Only ${item.product.stock} items available in stock`);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsUpdating(true);\r\n      await updateCartItem(item.productId, newQuantity);\r\n    } catch (error) {\r\n      console.error('Error updating quantity:', error);\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  const handleRemove = async () => {\r\n    try {\r\n      setIsRemoving(true);\r\n      await removeFromCart(item.productId);\r\n    } catch (error) {\r\n      console.error('Error removing item:', error);\r\n    } finally {\r\n      setIsRemoving(false);\r\n    }\r\n  };\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n    }).format(price);\r\n  };\r\n\r\n  if (!item.product) {\r\n    return null;\r\n  }\r\n\r\n  const mainImage = item.product.images && item.product.images.length > 0 \r\n    ? item.product.images[0] \r\n    : '/images/placeholder-product.jpg';\r\n\r\n  const itemTotal = item.quantity * item.product.price;\r\n\r\n  return (\r\n    <div className={styles.cartItem}>\r\n      {/* Product Image */}\r\n      <div className={styles.imageContainer}>\r\n        <img\r\n          src={mainImage}\r\n          alt={item.product.name}\r\n          className={styles.productImage}\r\n        />\r\n      </div>\r\n\r\n      {/* Product Details */}\r\n      <div className={styles.productDetails}>\r\n        <h3 className={styles.productName}>{item.product.name}</h3>\r\n        \r\n        {item.product.description && (\r\n          <p className={styles.productDescription}>\r\n            {item.product.description.length > 150 \r\n              ? `${item.product.description.substring(0, 150)}...` \r\n              : item.product.description}\r\n          </p>\r\n        )}\r\n\r\n        <div className={styles.productMeta}>\r\n          <span className={styles.unitPrice}>\r\n            {formatPrice(item.product.price)} each\r\n          </span>\r\n          {item.product.collection && (\r\n            <span className={styles.collection}>\r\n              {item.product.collection.name}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {/* Stock Status */}\r\n        <div className={styles.stockStatus}>\r\n          {item.product.stock > 0 ? (\r\n            <span className={styles.inStock}>\r\n              {item.product.stock > 10 ? 'In Stock' : `Only ${item.product.stock} left`}\r\n            </span>\r\n          ) : (\r\n            <span className={styles.outOfStock}>Out of Stock</span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quantity Controls */}\r\n      <div className={styles.quantitySection}>\r\n        <label className={styles.quantityLabel}>Quantity</label>\r\n        <div className={styles.quantityControls}>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleQuantityChange(item.quantity - 1)}\r\n            disabled={item.quantity <= 1 || isUpdating || state.isLoading}\r\n            className={styles.quantityBtn}\r\n          >\r\n            -\r\n          </button>\r\n          <span className={styles.quantity}>{item.quantity}</span>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleQuantityChange(item.quantity + 1)}\r\n            disabled={item.quantity >= item.product.stock || isUpdating || state.isLoading}\r\n            className={styles.quantityBtn}\r\n          >\r\n            +\r\n          </button>\r\n        </div>\r\n        {isUpdating && (\r\n          <span className={styles.updating}>Updating...</span>\r\n        )}\r\n      </div>\r\n\r\n      {/* Price Section */}\r\n      <div className={styles.priceSection}>\r\n        <div className={styles.itemTotal}>\r\n          {formatPrice(itemTotal)}\r\n        </div>\r\n        <div className={styles.priceBreakdown}>\r\n          {item.quantity} × {formatPrice(item.product.price)}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Remove Button */}\r\n      <div className={styles.removeSection}>\r\n        <button\r\n          onClick={handleRemove}\r\n          disabled={isRemoving || state.isLoading}\r\n          className={styles.removeBtn}\r\n          title=\"Remove from cart\"\r\n        >\r\n          {isRemoving ? (\r\n            <span className={styles.removing}>...</span>\r\n          ) : (\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M3 6h18\"/>\r\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\r\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\r\n              <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"/>\r\n              <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"/>\r\n            </svg>\r\n          )}\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CartItem;\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AAEA;AACA;;;AALA;;;;AAWA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;;IACjD,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB,OAAO;QAClC,IAAI,cAAc,KAAK,CAAC,KAAK,OAAO,EAAE;QAEtC,IAAI,cAAc,KAAK,OAAO,CAAC,KAAK,EAAE;YACpC,MAAM,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC;YAC3D;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAClE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,GACtB;IAEJ,MAAM,YAAY,KAAK,QAAQ,GAAG,KAAK,OAAO,CAAC,KAAK;IAEpD,qBACE,6LAAC;QAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;;0BAE7B,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;0BACnC,cAAA,6LAAC;oBACC,KAAK;oBACL,KAAK,KAAK,OAAO,CAAC,IAAI;oBACtB,WAAW,gKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAG,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,KAAK,OAAO,CAAC,IAAI;;;;;;oBAEpD,KAAK,OAAO,CAAC,WAAW,kBACvB,6LAAC;wBAAE,WAAW,gKAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,KAAK,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,MAC/B,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAClD,KAAK,OAAO,CAAC,WAAW;;;;;;kCAIhC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;;oCAC9B,YAAY,KAAK,OAAO,CAAC,KAAK;oCAAE;;;;;;;4BAElC,KAAK,OAAO,CAAC,UAAU,kBACtB,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,KAAK,OAAO,CAAC,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;kCAC/B,KAAK,OAAO,CAAC,KAAK,GAAG,kBACpB,6LAAC;4BAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;iDAG3E,6LAAC;4BAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,eAAe;;kCACpC,6LAAC;wBAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,aAAa;kCAAE;;;;;;kCACxC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,MAAM,SAAS;gCAC7D,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;0CAGD,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,KAAK,QAAQ;;;;;;0CAChD,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI,cAAc,MAAM,SAAS;gCAC9E,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;;;;;;;oBAIF,4BACC,6LAAC;wBAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKtC,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,YAAY;;;;;;kCAEf,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;;4BAClC,KAAK,QAAQ;4BAAC;4BAAI,YAAY,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;;0BAKrD,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,aAAa;0BAClC,cAAA,6LAAC;oBACC,SAAS;oBACT,UAAU,cAAc,MAAM,SAAS;oBACvC,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;oBAC3B,OAAM;8BAEL,2BACC,6LAAC;wBAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;6CAElC,6LAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;0CACjC,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GA7JM;;QAC8C,kIAAA,CAAA,UAAO;;;KADrD;uCA+JS", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartSummary/cartSummary.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"cartSummary-module__kqlNYa__actionButtons\",\n  \"cartSummary\": \"cartSummary-module__kqlNYa__cartSummary\",\n  \"checkoutBtn\": \"cartSummary-module__kqlNYa__checkoutBtn\",\n  \"checkoutIcon\": \"cartSummary-module__kqlNYa__checkoutIcon\",\n  \"clearBtn\": \"cartSummary-module__kqlNYa__clearBtn\",\n  \"clearIcon\": \"cartSummary-module__kqlNYa__clearIcon\",\n  \"divider\": \"cartSummary-module__kqlNYa__divider\",\n  \"freeShipping\": \"cartSummary-module__kqlNYa__freeShipping\",\n  \"infoIcon\": \"cartSummary-module__kqlNYa__infoIcon\",\n  \"label\": \"cartSummary-module__kqlNYa__label\",\n  \"securityIcon\": \"cartSummary-module__kqlNYa__securityIcon\",\n  \"securityNotice\": \"cartSummary-module__kqlNYa__securityNotice\",\n  \"shippingNotice\": \"cartSummary-module__kqlNYa__shippingNotice\",\n  \"summaryDetails\": \"cartSummary-module__kqlNYa__summaryDetails\",\n  \"summaryRow\": \"cartSummary-module__kqlNYa__summaryRow\",\n  \"title\": \"cartSummary-module__kqlNYa__title\",\n  \"totalLabel\": \"cartSummary-module__kqlNYa__totalLabel\",\n  \"totalRow\": \"cartSummary-module__kqlNYa__totalRow\",\n  \"totalValue\": \"cartSummary-module__kqlNYa__totalValue\",\n  \"value\": \"cartSummary-module__kqlNYa__value\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartSummary/CartSummary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport styles from './cartSummary.module.css';\r\n\r\ninterface CartSummaryProps {\r\n  showCheckoutButton?: boolean;\r\n  showClearButton?: boolean;\r\n}\r\n\r\nconst CartSummary: React.FC<CartSummaryProps> = ({\r\n  showCheckoutButton = true,\r\n  showClearButton = true,\r\n}) => {\r\n  const { state, clearCart } = useCart();\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n    }).format(price);\r\n  };\r\n\r\n  const handleClearCart = async () => {\r\n    if (window.confirm('Are you sure you want to clear your cart?')) {\r\n      await clearCart();\r\n    }\r\n  };\r\n\r\n  if (!state.cart || state.cart.cartItems.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const subtotal = state.cart.totalAmount;\r\n  const tax = subtotal * 0.08; // 8% tax rate\r\n  const shipping = subtotal > 100 ? 0 : 15; // Free shipping over $100\r\n  const total = subtotal + tax + shipping;\r\n\r\n  return (\r\n    <div className={styles.cartSummary}>\r\n      <h2 className={styles.title}>Order Summary</h2>\r\n      \r\n      <div className={styles.summaryDetails}>\r\n        {/* Items Count */}\r\n        <div className={styles.summaryRow}>\r\n          <span className={styles.label}>Items ({state.cart.totalItems})</span>\r\n          <span className={styles.value}>{formatPrice(subtotal)}</span>\r\n        </div>\r\n\r\n        {/* Shipping */}\r\n        <div className={styles.summaryRow}>\r\n          <span className={styles.label}>\r\n            Shipping\r\n            {shipping === 0 && <span className={styles.freeShipping}> (Free)</span>}\r\n          </span>\r\n          <span className={styles.value}>\r\n            {shipping === 0 ? 'Free' : formatPrice(shipping)}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Tax */}\r\n        <div className={styles.summaryRow}>\r\n          <span className={styles.label}>Tax</span>\r\n          <span className={styles.value}>{formatPrice(tax)}</span>\r\n        </div>\r\n\r\n        {/* Divider */}\r\n        <div className={styles.divider}></div>\r\n\r\n        {/* Total */}\r\n        <div className={styles.totalRow}>\r\n          <span className={styles.totalLabel}>Total</span>\r\n          <span className={styles.totalValue}>{formatPrice(total)}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Free Shipping Notice */}\r\n      {shipping > 0 && (\r\n        <div className={styles.shippingNotice}>\r\n          <svg className={styles.infoIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\r\n            <path d=\"M12 16v-4\"/>\r\n            <path d=\"M12 8h.01\"/>\r\n          </svg>\r\n          <span>\r\n            Add {formatPrice(100 - subtotal)} more for free shipping\r\n          </span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Action Buttons */}\r\n      <div className={styles.actionButtons}>\r\n        {showCheckoutButton && (\r\n          <Link href=\"/checkout\" className={styles.checkoutBtn}>\r\n            <svg className={styles.checkoutIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M9 12l2 2 4-4\"/>\r\n            </svg>\r\n            Proceed to Checkout\r\n          </Link>\r\n        )}\r\n\r\n        {showClearButton && (\r\n          <button\r\n            onClick={handleClearCart}\r\n            disabled={state.isLoading}\r\n            className={styles.clearBtn}\r\n          >\r\n            <svg className={styles.clearIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M3 6h18\"/>\r\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\r\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\r\n            </svg>\r\n            Clear Cart\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Security Notice */}\r\n      <div className={styles.securityNotice}>\r\n        <svg className={styles.securityIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n          <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\r\n          <path d=\"M9 12l2 2 4-4\"/>\r\n        </svg>\r\n        <span>Secure Checkout</span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CartSummary;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,qBAAqB,IAAI,EACzB,kBAAkB,IAAI,EACvB;;IACC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEnC,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,8CAA8C;YAC/D,MAAM;QACR;IACF;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,OAAO;IACT;IAEA,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW;IACvC,MAAM,MAAM,WAAW,MAAM,cAAc;IAC3C,MAAM,WAAW,WAAW,MAAM,IAAI,IAAI,0BAA0B;IACpE,MAAM,QAAQ,WAAW,MAAM;IAE/B,qBACE,6LAAC;QAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,WAAW;;0BAChC,6LAAC;gBAAG,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0BAAE;;;;;;0BAE7B,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCAEnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAAQ,MAAM,IAAI,CAAC,UAAU;oCAAC;;;;;;;0CAC7D,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAE5B,aAAa,mBAAK,6LAAC;wCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;0CAE3D,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAC1B,aAAa,IAAI,SAAS,YAAY;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,OAAO;;;;;;kCAG9B,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAC7B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;0CAAE;;;;;;0CACpC,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,YAAY;;;;;;;;;;;;;;;;;;YAKpD,WAAW,mBACV,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACtE,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,6LAAC;;4BAAK;4BACC,YAAY,MAAM;4BAAU;;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;;oBACjC,oCACC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,sKAAA,CAAA,UAAM,CAAC,WAAW;;0CAClD,6LAAC;gCAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CAC1E,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;oBAKT,iCACC,6LAAC;wBACC,SAAS;wBACT,UAAU,MAAM,SAAS;wBACzB,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAE1B,6LAAC;gCAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,SAAS;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;;kDACvE,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;;;;;;;4BACJ;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1E,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId;GArHM;;QAIyB,kIAAA,CAAA,UAAO;;;KAJhC;uCAuHS", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/cart/cart.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additionalActions\": \"cart-module__-RJi4G__additionalActions\",\n  \"cartContent\": \"cart-module__-RJi4G__cartContent\",\n  \"cartItems\": \"cart-module__-RJi4G__cartItems\",\n  \"contactLink\": \"cart-module__-RJi4G__contactLink\",\n  \"container\": \"cart-module__-RJi4G__container\",\n  \"continueShoppingLink\": \"cart-module__-RJi4G__continueShoppingLink\",\n  \"emptyCart\": \"cart-module__-RJi4G__emptyCart\",\n  \"emptyIcon\": \"cart-module__-RJi4G__emptyIcon\",\n  \"emptyMessage\": \"cart-module__-RJi4G__emptyMessage\",\n  \"emptyTitle\": \"cart-module__-RJi4G__emptyTitle\",\n  \"errorIcon\": \"cart-module__-RJi4G__errorIcon\",\n  \"errorMessage\": \"cart-module__-RJi4G__errorMessage\",\n  \"header\": \"cart-module__-RJi4G__header\",\n  \"helpSection\": \"cart-module__-RJi4G__helpSection\",\n  \"itemsHeader\": \"cart-module__-RJi4G__itemsHeader\",\n  \"itemsList\": \"cart-module__-RJi4G__itemsList\",\n  \"loadingContainer\": \"cart-module__-RJi4G__loadingContainer\",\n  \"loadingSpinner\": \"cart-module__-RJi4G__loadingSpinner\",\n  \"shippingInfo\": \"cart-module__-RJi4G__shippingInfo\",\n  \"shopIcon\": \"cart-module__-RJi4G__shopIcon\",\n  \"shopNowBtn\": \"cart-module__-RJi4G__shopNowBtn\",\n  \"spin\": \"cart-module__-RJi4G__spin\",\n  \"subtitle\": \"cart-module__-RJi4G__subtitle\",\n  \"title\": \"cart-module__-RJi4G__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport CartItem from '@/components/cart/CartItem/CartItem';\r\nimport CartSummary from '@/components/cart/CartSummary/CartSummary';\r\nimport styles from './cart.module.css';\r\n\r\nexport default function CartPage() {\r\n  const { state } = useCart();\r\n\r\n  if (state.isLoading) {\r\n    return (\r\n      <div className={styles.container}>\r\n        <div className={styles.loadingContainer}>\r\n          <div className={styles.loadingSpinner}></div>\r\n          <p>Loading your cart...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!state.cart || state.cart.cartItems.length === 0) {\r\n    return (\r\n      <div className={styles.container}>\r\n        <div className={styles.emptyCart}>\r\n          <div className={styles.emptyIcon}>\r\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\r\n            </svg>\r\n          </div>\r\n          <h1 className={styles.emptyTitle}>Your Cart is Empty</h1>\r\n          <p className={styles.emptyMessage}>\r\n            Looks like you haven&apos;t added any items to your cart yet. \r\n            Start shopping to fill it up!\r\n          </p>\r\n          <Link href=\"/products\" className={styles.shopNowBtn}>\r\n            <svg className={styles.shopIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\r\n            </svg>\r\n            Start Shopping\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Header */}\r\n      <div className={styles.header}>\r\n        <h1 className={styles.title}>Shopping Cart</h1>\r\n        <p className={styles.subtitle}>\r\n          {state.cart.totalItems} {state.cart.totalItems === 1 ? 'item' : 'items'} in your cart\r\n        </p>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {state.error && (\r\n        <div className={styles.errorMessage}>\r\n          <svg className={styles.errorIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\r\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/>\r\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>\r\n          </svg>\r\n          <span>{state.error}</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Cart Content */}\r\n      <div className={styles.cartContent}>\r\n        {/* Cart Items */}\r\n        <div className={styles.cartItems}>\r\n          <div className={styles.itemsHeader}>\r\n            <h2>Items</h2>\r\n            <Link href=\"/products\" className={styles.continueShoppingLink}>\r\n              Continue Shopping\r\n            </Link>\r\n          </div>\r\n          \r\n          <div className={styles.itemsList}>\r\n            {state.cart.cartItems.map((item) => (\r\n              <CartItem key={item.id} item={item} />\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Cart Summary */}\r\n        <div className={styles.cartSummaryContainer}>\r\n          <CartSummary />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Additional Actions */}\r\n      <div className={styles.additionalActions}>\r\n        <div className={styles.helpSection}>\r\n          <h3>Need Help?</h3>\r\n          <p>\r\n            Have questions about your order? \r\n            <Link href=\"/contact\" className={styles.contactLink}> Contact us</Link> \r\n            or call (555) 123-4567\r\n          </p>\r\n        </div>\r\n\r\n        <div className={styles.shippingInfo}>\r\n          <h3>Shipping Information</h3>\r\n          <ul>\r\n            <li>Free shipping on orders over $100</li>\r\n            <li>Standard delivery: 5-7 business days</li>\r\n            <li>Express delivery: 2-3 business days</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExB,IAAI,MAAM,SAAS,EAAE;QACnB,qBACE,6LAAC;YAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,gBAAgB;;kCACrC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;;;;;kCACrC,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,qBACE,6LAAC;YAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;kCAC9B,cAAA,6LAAC;4BAAI,SAAQ;4BAAY,MAAK;4BAAO,QAAO;sCAC1C,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCAClC,6LAAC;wBAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;kCAInC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;0CACjD,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CACtE,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;;;;;;;;;;;;IAMhB;IAEA,qBACE,6LAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,6LAAC;wBAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;4BAC1B,MAAM,IAAI,CAAC,UAAU;4BAAC;4BAAE,MAAM,IAAI,CAAC,UAAU,KAAK,IAAI,SAAS;4BAAQ;;;;;;;;;;;;;YAK3E,MAAM,KAAK,kBACV,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACvE,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;;;;;;0CAC/B,6LAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAK,IAAG;;;;;;;;;;;;kCAEjC,6LAAC;kCAAM,MAAM,KAAK;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kCAEhC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0CAC9B,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAW,yIAAA,CAAA,UAAM,CAAC,oBAAoB;kDAAE;;;;;;;;;;;;0CAKjE,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;0CAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC,qJAAA,CAAA,UAAQ;wCAAe,MAAM;uCAAf,KAAK,EAAE;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,oBAAoB;kCACzC,cAAA,6LAAC,2JAAA,CAAA,UAAW;;;;;;;;;;;;;;;;0BAKhB,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,iBAAiB;;kCACtC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;;oCAAE;kDAED,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kDAAE;;;;;;oCAAkB;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;;kDACC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GA3GwB;;QACJ,kIAAA,CAAA,UAAO;;;KADH", "debugId": null}}]}