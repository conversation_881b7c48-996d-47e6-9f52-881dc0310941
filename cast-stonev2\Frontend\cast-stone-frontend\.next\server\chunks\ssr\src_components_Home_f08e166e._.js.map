{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actions\": \"heroSection-module__CcYIFG__actions\",\n  \"active\": \"heroSection-module__CcYIFG__active\",\n  \"backgroundImage\": \"heroSection-module__CcYIFG__backgroundImage\",\n  \"bounce\": \"heroSection-module__CcYIFG__bounce\",\n  \"buttonRipple\": \"heroSection-module__CcYIFG__buttonRipple\",\n  \"buttonText\": \"heroSection-module__CcYIFG__buttonText\",\n  \"container\": \"heroSection-module__CcYIFG__container\",\n  \"content\": \"heroSection-module__CcYIFG__content\",\n  \"fadeInUp\": \"heroSection-module__CcYIFG__fadeInUp\",\n  \"hero\": \"heroSection-module__CcYIFG__hero\",\n  \"imageContainer\": \"heroSection-module__CcYIFG__imageContainer\",\n  \"imageOverlay\": \"heroSection-module__CcYIFG__imageOverlay\",\n  \"imageSlide\": \"heroSection-module__CcYIFG__imageSlide\",\n  \"indicator\": \"heroSection-module__CcYIFG__indicator\",\n  \"indicatorActive\": \"heroSection-module__CcYIFG__indicatorActive\",\n  \"indicators\": \"heroSection-module__CcYIFG__indicators\",\n  \"navArrow\": \"heroSection-module__CcYIFG__navArrow\",\n  \"navArrowLeft\": \"heroSection-module__CcYIFG__navArrowLeft\",\n  \"navArrowRight\": \"heroSection-module__CcYIFG__navArrowRight\",\n  \"primaryButton\": \"heroSection-module__CcYIFG__primaryButton\",\n  \"scrollArrow\": \"heroSection-module__CcYIFG__scrollArrow\",\n  \"scrollIndicator\": \"heroSection-module__CcYIFG__scrollIndicator\",\n  \"secondaryButton\": \"heroSection-module__CcYIFG__secondaryButton\",\n  \"smoothZoom\": \"heroSection-module__CcYIFG__smoothZoom\",\n  \"subtitle\": \"heroSection-module__CcYIFG__subtitle\",\n  \"title\": \"heroSection-module__CcYIFG__title\",\n  \"titleLine1\": \"heroSection-module__CcYIFG__titleLine1\",\n  \"titleLine2\": \"heroSection-module__CcYIFG__titleLine2\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HeroSection/HeroSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport styles from './heroSection.module.css';\r\n\r\ninterface HeroSectionProps {\r\n  title?: string;\r\n  subtitle?: string;\r\n}\r\n\r\nconst HeroSection: React.FC<HeroSectionProps> = ({\r\n  subtitle = \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\r\n}) => {\r\n  // Array of hero images\r\n  const heroImages = [\r\n    '/heroSection/BANNER IMAGES/IMG_0930.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_1194.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_1206.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_1263.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_1686.JPG',\r\n    '/heroSection/BANNER IMAGES/IMG_2231.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_3920.jpg',\r\n    '/heroSection/BANNER IMAGES/IMG_8272.jpg'\r\n  ];\r\n\r\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    // Change image every 5 seconds to match the zoom animation duration\r\n    const interval = setInterval(() => {\r\n      setCurrentImageIndex((prevIndex) =>\r\n        (prevIndex + 1) % heroImages.length\r\n      );\r\n    }, 5000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [heroImages.length]);\r\n\r\n  // Navigation functions\r\n  const goToPrevious = () => {\r\n    setCurrentImageIndex((prevIndex) =>\r\n      prevIndex === 0 ? heroImages.length - 1 : prevIndex - 1\r\n    );\r\n  };\r\n\r\n  const goToNext = () => {\r\n    setCurrentImageIndex((prevIndex) =>\r\n      (prevIndex + 1) % heroImages.length\r\n    );\r\n  };\r\n\r\n  const goToSlide = (index: number) => {\r\n    setCurrentImageIndex(index);\r\n  };\r\n\r\n  return (\r\n    <section className={styles.hero}>\r\n      {/* Image Background Carousel */}\r\n      <div className={styles.imageContainer}>\r\n        {heroImages.map((imageSrc, index) => (\r\n          <div\r\n            key={`${imageSrc}-${index}`}\r\n            className={`${styles.imageSlide} ${\r\n              index === currentImageIndex ? styles.active : ''\r\n            }`}\r\n          >\r\n            <Image\r\n              src={imageSrc}\r\n              alt={`Hero background ${index + 1}`}\r\n              fill\r\n              className={styles.backgroundImage}\r\n              sizes=\"100vw\"\r\n              priority={index === 0} // Prioritize loading the first image\r\n              quality={90}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {/* Image Overlay */}\r\n        <div className={styles.imageOverlay}></div>\r\n      </div>\r\n\r\n      {/* Navigation Arrows */}\r\n      <button\r\n        className={`${styles.navArrow} ${styles.navArrowLeft}`}\r\n        onClick={goToPrevious}\r\n        aria-label=\"Previous image\"\r\n      >\r\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n          <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n        </svg>\r\n      </button>\r\n\r\n      <button\r\n        className={`${styles.navArrow} ${styles.navArrowRight}`}\r\n        onClick={goToNext}\r\n        aria-label=\"Next image\"\r\n      >\r\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n          <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n        </svg>\r\n      </button>\r\n\r\n      {/* Content */}\r\n      <div className={styles.container}>\r\n        <div className={styles.content}>\r\n          <h1 className={styles.title}>\r\n            <span className={styles.titleLine1}>Timeless Elegance in</span>\r\n            <span className={styles.titleLine2}>Cast Stone</span>\r\n          </h1>\r\n\r\n          <p className={styles.subtitle}>\r\n            {subtitle}\r\n          </p>\r\n\r\n          <div className={styles.actions}>\r\n            <Link href=\"/collections\" className={styles.primaryButton}>\r\n              <span className={styles.buttonText}>EXPLORE COLLECTION</span>\r\n              <div className={styles.buttonRipple}></div>\r\n            </Link>\r\n\r\n            <Link href=\"/our-story\" className={styles.secondaryButton}>\r\n              <span className={styles.buttonText}>WATCH OUR STORY</span>\r\n              <div className={styles.buttonRipple}></div>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Indicator Dots */}\r\n          <div className={styles.indicators}>\r\n            {heroImages.map((_, index) => (\r\n              <button\r\n                key={index}\r\n                className={`${styles.indicator} ${\r\n                  index === currentImageIndex ? styles.indicatorActive : ''\r\n                }`}\r\n                onClick={() => goToSlide(index)}\r\n                aria-label={`Go to image ${index + 1}`}\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Scroll Indicator */}\r\n      <div className={styles.scrollIndicator}>\r\n        <div className={styles.scrollArrow}>\r\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <path d=\"M7 13L12 18L17 13\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n            <path d=\"M7 6L12 11L17 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n          </svg>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default HeroSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,WAAW,qJAAqJ,EACjK;IACC,uBAAuB;IACvB,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAC,YACpB,CAAC,YAAY,CAAC,IAAI,WAAW,MAAM;QAEvC,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,WAAW,MAAM;KAAC;IAEtB,uBAAuB;IACvB,MAAM,eAAe;QACnB,qBAAqB,CAAC,YACpB,cAAc,IAAI,WAAW,MAAM,GAAG,IAAI,YAAY;IAE1D;IAEA,MAAM,WAAW;QACf,qBAAqB,CAAC,YACpB,CAAC,YAAY,CAAC,IAAI,WAAW,MAAM;IAEvC;IAEA,MAAM,YAAY,CAAC;QACjB,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAQ,WAAW,mKAAA,CAAA,UAAM,CAAC,IAAI;;0BAE7B,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,cAAc;;oBAClC,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC;4BAEC,WAAW,GAAG,mKAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC/B,UAAU,oBAAoB,mKAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;sCAEF,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,CAAC,gBAAgB,EAAE,QAAQ,GAAG;gCACnC,IAAI;gCACJ,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;gCACjC,OAAM;gCACN,UAAU,UAAU;gCACpB,SAAS;;;;;;2BAZN,GAAG,SAAS,CAAC,EAAE,OAAO;;;;;kCAkB/B,8OAAC;wBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;0BAIrC,8OAAC;gBACC,WAAW,GAAG,mKAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,mKAAA,CAAA,UAAM,CAAC,YAAY,EAAE;gBACtD,SAAS;gBACT,cAAW;0BAEX,cAAA,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;8BACnD,cAAA,8OAAC;wBAAK,GAAE;wBAAmB,QAAO;wBAAe,aAAY;wBAAI,eAAc;wBAAQ,gBAAe;;;;;;;;;;;;;;;;0BAI1G,8OAAC;gBACC,WAAW,GAAG,mKAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,mKAAA,CAAA,UAAM,CAAC,aAAa,EAAE;gBACvD,SAAS;gBACT,cAAW;0BAEX,cAAA,8OAAC;oBAAI,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;8BACnD,cAAA,8OAAC;wBAAK,GAAE;wBAAkB,QAAO;wBAAe,aAAY;wBAAI,eAAc;wBAAQ,gBAAe;;;;;;;;;;;;;;;;0BAKzG,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,SAAS;0BAC9B,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;sCAC5B,8OAAC;4BAAG,WAAW,mKAAA,CAAA,UAAM,CAAC,KAAK;;8CACzB,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;;;;;;;sCAGtC,8OAAC;4BAAE,WAAW,mKAAA,CAAA,UAAM,CAAC,QAAQ;sCAC1B;;;;;;sCAGH,8OAAC;4BAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,OAAO;;8CAC5B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAW,mKAAA,CAAA,UAAM,CAAC,aAAa;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;8CAGrC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;;sDACvD,8OAAC;4CAAK,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sDAAE;;;;;;sDACpC,8OAAC;4CAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,UAAU;sCAC9B,WAAW,GAAG,CAAC,CAAC,GAAG,sBAClB,8OAAC;oCAEC,WAAW,GAAG,mKAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAC9B,UAAU,oBAAoB,mKAAA,CAAA,UAAM,CAAC,eAAe,GAAG,IACvD;oCACF,SAAS,IAAM,UAAU;oCACzB,cAAY,CAAC,YAAY,EAAE,QAAQ,GAAG;mCALjC;;;;;;;;;;;;;;;;;;;;;0BAaf,8OAAC;gBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,eAAe;0BACpC,cAAA,8OAAC;oBAAI,WAAW,mKAAA,CAAA,UAAM,CAAC,WAAW;8BAChC,cAAA,8OAAC;wBAAI,OAAM;wBAAK,QAAO;wBAAK,SAAQ;wBAAY,MAAK;;0CACnD,8OAAC;gCAAK,GAAE;gCAAoB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;0CACvG,8OAAC;gCAAK,GAAE;gCAAkB,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjH;uCAEe", "debugId": null}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CategoriesSection/categoriesSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButton\": \"categoriesSection-module__fGUJra__actionButton\",\n  \"cardActions\": \"categoriesSection-module__fGUJra__cardActions\",\n  \"cardContent\": \"categoriesSection-module__fGUJra__cardContent\",\n  \"cardHeader\": \"categoriesSection-module__fGUJra__cardHeader\",\n  \"cardLink\": \"categoriesSection-module__fGUJra__cardLink\",\n  \"categoriesSection\": \"categoriesSection-module__fGUJra__categoriesSection\",\n  \"categoryCard\": \"categoriesSection-module__fGUJra__categoryCard\",\n  \"categoryDescription\": \"categoriesSection-module__fGUJra__categoryDescription\",\n  \"categoryImage\": \"categoriesSection-module__fGUJra__categoryImage\",\n  \"categorySubtitle\": \"categoriesSection-module__fGUJra__categorySubtitle\",\n  \"categoryTitle\": \"categoriesSection-module__fGUJra__categoryTitle\",\n  \"container\": \"categoriesSection-module__fGUJra__container\",\n  \"errorMessage\": \"categoriesSection-module__fGUJra__errorMessage\",\n  \"grid\": \"categoriesSection-module__fGUJra__grid\",\n  \"header\": \"categoriesSection-module__fGUJra__header\",\n  \"hoverEffect\": \"categoriesSection-module__fGUJra__hoverEffect\",\n  \"imageContainer\": \"categoriesSection-module__fGUJra__imageContainer\",\n  \"imageOverlay\": \"categoriesSection-module__fGUJra__imageOverlay\",\n  \"loadingCard\": \"categoriesSection-module__fGUJra__loadingCard\",\n  \"loadingContent\": \"categoriesSection-module__fGUJra__loadingContent\",\n  \"loadingGrid\": \"categoriesSection-module__fGUJra__loadingGrid\",\n  \"loadingImage\": \"categoriesSection-module__fGUJra__loadingImage\",\n  \"loadingText\": \"categoriesSection-module__fGUJra__loadingText\",\n  \"noImagePlaceholder\": \"categoriesSection-module__fGUJra__noImagePlaceholder\",\n  \"noImageText\": \"categoriesSection-module__fGUJra__noImageText\",\n  \"pulse\": \"categoriesSection-module__fGUJra__pulse\",\n  \"secondaryButton\": \"categoriesSection-module__fGUJra__secondaryButton\",\n  \"sectionSubtitle\": \"categoriesSection-module__fGUJra__sectionSubtitle\",\n  \"sectionTitle\": \"categoriesSection-module__fGUJra__sectionTitle\",\n  \"shimmer\": \"categoriesSection-module__fGUJra__shimmer\",\n  \"stats\": \"categoriesSection-module__fGUJra__stats\",\n  \"statsLabel\": \"categoriesSection-module__fGUJra__statsLabel\",\n  \"statsNumber\": \"categoriesSection-module__fGUJra__statsNumber\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CategoriesSection/CategoriesSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { collectionGetService } from '../../../services/api/collections';\r\nimport { Collection } from '../../../services/types/entities';\r\nimport styles from './categoriesSection.module.css';\r\n\r\ninterface CategoriesSectionProps {\r\n  maxCollections?: number;\r\n}\r\n\r\nconst CategoriesSection: React.FC<CategoriesSectionProps> = ({\r\n  maxCollections = 4\r\n}) => {\r\n  const [collections, setCollections] = useState<Collection[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchCollections = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        // Get published collections (level 1 - root collections for categories)\r\n        const publishedCollections = await collectionGetService.getPublished();\r\n        // Filter to get only level 1 collections and limit to maxCollections\r\n        const rootCollections = publishedCollections\r\n          .filter(collection => collection.level === 1)\r\n          .slice(0, maxCollections);\r\n        setCollections(rootCollections);\r\n      } catch (err) {\r\n        console.error('Failed to fetch collections:', err);\r\n        setError('Failed to load collections');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCollections();\r\n  }, [maxCollections]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <section className={styles.categoriesSection}>\r\n        <div className={styles.container}>\r\n          <div className={styles.header}>\r\n            <h2 className={styles.sectionTitle}>Our Collections</h2>\r\n            <p className={styles.sectionSubtitle}>\r\n              Explore our diverse range of handcrafted cast stone elements,\r\n              each designed to bring timeless elegance to your space.\r\n            </p>\r\n          </div>\r\n          <div className={styles.loadingGrid}>\r\n            {[...Array(4)].map((_, index) => (\r\n              <div key={index} className={styles.loadingCard}>\r\n                <div className={styles.loadingImage}></div>\r\n                <div className={styles.loadingContent}>\r\n                  <div className={styles.loadingText}></div>\r\n                  <div className={styles.loadingText}></div>\r\n                  <div className={styles.loadingText}></div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <section className={styles.categoriesSection}>\r\n        <div className={styles.container}>\r\n          <div className={styles.header}>\r\n            <h2 className={styles.sectionTitle}>Our Collections</h2>\r\n            <p className={styles.sectionSubtitle}>\r\n              Explore our diverse range of handcrafted cast stone elements,\r\n              each designed to bring timeless elegance to your space.\r\n            </p>\r\n          </div>\r\n          <div className={styles.errorMessage}>\r\n            <p>Unable to load collections. Please try again later.</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  const getCollectionImage = (collection: Collection): string | null => {\r\n    // Use the first image from the collection's images array\r\n    if (collection.images && collection.images.length > 0) {\r\n      return collection.images[0];\r\n    }\r\n    // Return null if no images available\r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <section className={styles.categoriesSection}>\r\n      <div className={styles.container}>\r\n        <div className={styles.header}>\r\n          <h2 className={styles.sectionTitle}>Our Collections</h2>\r\n          <p className={styles.sectionSubtitle}>\r\n            Explore our diverse range of handcrafted cast stone elements,\r\n            each designed to bring timeless elegance to your space.\r\n          </p>\r\n        </div>\r\n\r\n        <div className={styles.grid}>\r\n          {collections.map((collection, index) => (\r\n            <div\r\n              key={collection.id}\r\n              className={`${styles.categoryCard} ${styles[`card${index + 1}`]}`}\r\n            >\r\n              <Link href={`/collections/${collection.id}`} className={styles.cardLink}>\r\n                <div className={styles.imageContainer}>\r\n                  {getCollectionImage(collection) ? (\r\n                    <Image\r\n                      src={getCollectionImage(collection)!}\r\n                      alt={collection.name}\r\n                      fill\r\n                      className={styles.categoryImage}\r\n                      sizes=\"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw\"\r\n                    />\r\n                  ) : (\r\n                    <div className={styles.noImagePlaceholder}>\r\n                      <span className={styles.noImageText}>No Image</span>\r\n                    </div>\r\n                  )}\r\n                  <div className={styles.imageOverlay}></div>\r\n                </div>\r\n\r\n                <div className={styles.cardContent}>\r\n                  <div className={styles.cardHeader}>\r\n                    <div className={styles.stats}>\r\n                      <span className={styles.statsNumber}>{collection.productCount || 0}</span>\r\n                      <span className={styles.statsLabel}>Products</span>\r\n                    </div>\r\n                    <h3 className={styles.categoryTitle}>{collection.name}</h3>\r\n                    <p className={styles.categorySubtitle}>\r\n                      {collection.tags && collection.tags.length > 0\r\n                        ? collection.tags[0].toUpperCase()\r\n                        : 'COLLECTION'\r\n                      }\r\n                    </p>\r\n                  </div>\r\n\r\n                  <p className={styles.categoryDescription}>\r\n                    {collection.description || 'Explore our curated selection of handcrafted cast stone elements.'}\r\n                  </p>\r\n\r\n                  <div className={styles.cardActions}>\r\n                    <button className={styles.actionButton}>\r\n                      <span>Shop Now</span>\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                        <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    </button>\r\n                    <button className={styles.secondaryButton}>\r\n                      <span>All Products</span>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className={styles.hoverEffect}></div>\r\n              </Link>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default CategoriesSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAPA;;;;;;;AAaA,MAAM,oBAAsD,CAAC,EAC3D,iBAAiB,CAAC,EACnB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,wEAAwE;gBACxE,MAAM,uBAAuB,MAAM,4IAAA,CAAA,uBAAoB,CAAC,YAAY;gBACpE,qEAAqE;gBACrE,MAAM,kBAAkB,qBACrB,MAAM,CAAC,CAAA,aAAc,WAAW,KAAK,KAAK,GAC1C,KAAK,CAAC,GAAG;gBACZ,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAe;IAEnB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;sBAC1C,cAAA,8OAAC;gBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;0CAAE;;;;;;0CACpC,8OAAC;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;kCAC/B;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gCAAgB,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;kDAC5C,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;;;;;0DAClC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;;;;;0DAClC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;;+BAL5B;;;;;;;;;;;;;;;;;;;;;IAatB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;sBAC1C,cAAA,8OAAC;gBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;0CAAE;;;;;;0CACpC,8OAAC;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;0CAAE;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;kCACjC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,MAAM,qBAAqB,CAAC;QAC1B,yDAAyD;QACzD,IAAI,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,GAAG;YACrD,OAAO,WAAW,MAAM,CAAC,EAAE;QAC7B;QACA,qCAAqC;QACrC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAQ,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;kBAC1C,cAAA,8OAAC;YAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;sCACpC,8OAAC;4BAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;sCAAE;;;;;;;;;;;;8BAMxC,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,IAAI;8BACxB,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;4BAEC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,+KAAA,CAAA,UAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,GAAG,CAAC,EAAE;sCAEjE,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,QAAQ;;kDACrE,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;4CAClC,mBAAmB,4BAClB,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,mBAAmB;gDACxB,KAAK,WAAW,IAAI;gDACpB,IAAI;gDACJ,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;gDAC/B,OAAM;;;;;qEAGR,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,kBAAkB;0DACvC,cAAA,8OAAC;oDAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;8DAAE;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;kDAGrC,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;;kEAC/B,8OAAC;wDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,KAAK;;0EAC1B,8OAAC;gEAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;0EAAG,WAAW,YAAY,IAAI;;;;;;0EACjE,8OAAC;gEAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;0EAAE;;;;;;;;;;;;kEAEtC,8OAAC;wDAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;kEAAG,WAAW,IAAI;;;;;;kEACrD,8OAAC;wDAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,gBAAgB;kEAClC,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,IACzC,WAAW,IAAI,CAAC,EAAE,CAAC,WAAW,KAC9B;;;;;;;;;;;;0DAKR,8OAAC;gDAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,mBAAmB;0DACrC,WAAW,WAAW,IAAI;;;;;;0DAG7B,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;kEAChC,8OAAC;wDAAO,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;0EACpC,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAI,OAAM;gEAAK,QAAO;gEAAK,SAAQ;gEAAY,MAAK;0EACnD,cAAA,8OAAC;oEAAK,GAAE;oEAA4B,QAAO;oEAAe,aAAY;oEAAI,eAAc;oEAAQ,gBAAe;;;;;;;;;;;;;;;;;kEAGnH,8OAAC;wDAAO,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;kEACvC,cAAA,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;2BArD/B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;AA6DhC;uCAEe", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CatalogBanner/catalogBanner.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"backgroundContainer\": \"catalogBanner-module__6ToAlG__backgroundContainer\",\n  \"backgroundImage\": \"catalogBanner-module__6ToAlG__backgroundImage\",\n  \"backgroundOverlay\": \"catalogBanner-module__6ToAlG__backgroundOverlay\",\n  \"buttonRipple\": \"catalogBanner-module__6ToAlG__buttonRipple\",\n  \"catalogBanner\": \"catalogBanner-module__6ToAlG__catalogBanner\",\n  \"catalogStats\": \"catalogBanner-module__6ToAlG__catalogStats\",\n  \"container\": \"catalogBanner-module__6ToAlG__container\",\n  \"content\": \"catalogBanner-module__6ToAlG__content\",\n  \"ctaButton\": \"catalogBanner-module__6ToAlG__ctaButton\",\n  \"ctaContainer\": \"catalogBanner-module__6ToAlG__ctaContainer\",\n  \"ctaIcon\": \"catalogBanner-module__6ToAlG__ctaIcon\",\n  \"ctaText\": \"catalogBanner-module__6ToAlG__ctaText\",\n  \"decorativeCircle\": \"catalogBanner-module__6ToAlG__decorativeCircle\",\n  \"decorativeElements\": \"catalogBanner-module__6ToAlG__decorativeElements\",\n  \"decorativeLine\": \"catalogBanner-module__6ToAlG__decorativeLine\",\n  \"description\": \"catalogBanner-module__6ToAlG__description\",\n  \"feature\": \"catalogBanner-module__6ToAlG__feature\",\n  \"featureIcon\": \"catalogBanner-module__6ToAlG__featureIcon\",\n  \"features\": \"catalogBanner-module__6ToAlG__features\",\n  \"float\": \"catalogBanner-module__6ToAlG__float\",\n  \"slide\": \"catalogBanner-module__6ToAlG__slide\",\n  \"stat\": \"catalogBanner-module__6ToAlG__stat\",\n  \"statLabel\": \"catalogBanner-module__6ToAlG__statLabel\",\n  \"statNumber\": \"catalogBanner-module__6ToAlG__statNumber\",\n  \"subtitle\": \"catalogBanner-module__6ToAlG__subtitle\",\n  \"textContent\": \"catalogBanner-module__6ToAlG__textContent\",\n  \"title\": \"catalogBanner-module__6ToAlG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CatalogBanner/CatalogBanner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport styles from './catalogBanner.module.css';\r\n\r\ninterface CatalogBannerProps {\r\n  title?: string;\r\n  subtitle?: string;\r\n  description?: string;\r\n  ctaText?: string;\r\n  ctaHref?: string;\r\n  backgroundImage?: string;\r\n}\r\n\r\nconst CatalogBanner: React.FC<CatalogBannerProps> = ({\r\n  title = \"Explore Our Complete Catalog\",\r\n  subtitle = \"Discover Excellence\",\r\n  description = \"Browse through our comprehensive collection of handcrafted cast stone elements. From architectural details to decorative accents, find the perfect pieces to elevate your space with timeless elegance.\",\r\n  ctaText = \"View Full Catalog\",\r\n  ctaHref = \"/catalog\",\r\n  backgroundImage = \"/images/catalog-banner-bg.jpg\"\r\n}) => {\r\n  return (\r\n    <section className={styles.catalogBanner}>\r\n      <div className={styles.backgroundContainer}>\r\n        <Image\r\n          src={backgroundImage}\r\n          alt=\"Cast Stone Catalog\"\r\n          fill\r\n          className={styles.backgroundImage}\r\n          sizes=\"100vw\"\r\n          priority={false}\r\n        />\r\n        <div className={styles.backgroundOverlay}></div>\r\n      </div>\r\n\r\n      <div className={styles.container}>\r\n        <div className={styles.content}>\r\n          <div className={styles.textContent}>\r\n            <span className={styles.subtitle}>{subtitle}</span>\r\n            <h2 className={styles.title}>{title}</h2>\r\n            <p className={styles.description}>{description}</p>\r\n            \r\n            <div className={styles.features}>\r\n              <div className={styles.feature}>\r\n                <div className={styles.featureIcon}>\r\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                    <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <span>500+ Products</span>\r\n              </div>\r\n              \r\n              <div className={styles.feature}>\r\n                <div className={styles.featureIcon}>\r\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                    <path d=\"M9 11L12 14L22 4\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    <path d=\"M21 12V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V5C3.9 5 4.9 5 5 5H16\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <span>Quality Assured</span>\r\n              </div>\r\n              \r\n              <div className={styles.feature}>\r\n                <div className={styles.featureIcon}>\r\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                    <path d=\"M13 2L3 14H12L11 22L21 10H12L13 2Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                  </svg>\r\n                </div>\r\n                <span>Fast Delivery</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.ctaContainer}>\r\n            <Link href={ctaHref} className={styles.ctaButton}>\r\n              <span className={styles.ctaText}>{ctaText}</span>\r\n              <div className={styles.ctaIcon}>\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                  <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                </svg>\r\n              </div>\r\n              <div className={styles.buttonRipple}></div>\r\n            </Link>\r\n            \r\n            <div className={styles.catalogStats}>\r\n              <div className={styles.stat}>\r\n                <span className={styles.statNumber}>25+</span>\r\n                <span className={styles.statLabel}>Years Experience</span>\r\n              </div>\r\n              <div className={styles.stat}>\r\n                <span className={styles.statNumber}>1000+</span>\r\n                <span className={styles.statLabel}>Happy Clients</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Decorative Elements */}\r\n      <div className={styles.decorativeElements}>\r\n        <div className={styles.decorativeCircle}></div>\r\n        <div className={styles.decorativeLine}></div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default CatalogBanner;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgBA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,8BAA8B,EACtC,WAAW,qBAAqB,EAChC,cAAc,yMAAyM,EACvN,UAAU,mBAAmB,EAC7B,UAAU,UAAU,EACpB,kBAAkB,+BAA+B,EAClD;IACC,qBACE,8OAAC;QAAQ,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;0BACtC,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,mBAAmB;;kCACxC,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAI;wBACJ,IAAI;wBACJ,WAAW,uKAAA,CAAA,UAAM,CAAC,eAAe;wBACjC,OAAM;wBACN,UAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,iBAAiB;;;;;;;;;;;;0BAG1C,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;0BAC9B,cAAA,8OAAC;oBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;sCAC5B,8OAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;8CAChC,8OAAC;oCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;8CAAG;;;;;;8CACnC,8OAAC;oCAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,KAAK;8CAAG;;;;;;8CAC9B,8OAAC;oCAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8CAAG;;;;;;8CAEnC,8OAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC7B,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;;0EACnD,8OAAC;gEAAK,GAAE;gEAA6B,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EAChH,8OAAC;gEAAK,GAAE;gEAAoB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EACvG,8OAAC;gEAAK,GAAE;gEAAoB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;8DAG3G,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;;0EACnD,8OAAC;gEAAK,GAAE;gEAAmB,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;0EACtG,8OAAC;gEAAK,GAAE;gEAA4E,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;8DAGnK,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;kEACnD,cAAA,8OAAC;4DAAK,GAAE;4DAAqC,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;8DAG5H,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;8CACjC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM;oCAAS,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;;sDAC9C,8OAAC;4CAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAAG;;;;;;sDAClC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAC5B,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACnD,cAAA,8OAAC;oDAAK,GAAE;oDAA4B,QAAO;oDAAe,aAAY;oDAAI,eAAc;oDAAQ,gBAAe;;;;;;;;;;;;;;;;sDAGnH,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;8CAGrC,8OAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;sDACjC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,IAAI;;8DACzB,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8DAAE;;;;;;8DACpC,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;;;;;;;sDAErC,8OAAC;4CAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,IAAI;;8DACzB,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8DAAE;;;;;;8DACpC,8OAAC;oDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACvC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;;;;;kCACvC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;AAI7C;uCAEe", "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionText\": \"collectionsCarousel-module____MCqq__actionText\",\n  \"cardAction\": \"collectionsCarousel-module____MCqq__cardAction\",\n  \"cardContent\": \"collectionsCarousel-module____MCqq__cardContent\",\n  \"cardLink\": \"collectionsCarousel-module____MCqq__cardLink\",\n  \"carousel\": \"collectionsCarousel-module____MCqq__carousel\",\n  \"carouselContainer\": \"collectionsCarousel-module____MCqq__carouselContainer\",\n  \"collectionCard\": \"collectionsCarousel-module____MCqq__collectionCard\",\n  \"collectionDescription\": \"collectionsCarousel-module____MCqq__collectionDescription\",\n  \"collectionImage\": \"collectionsCarousel-module____MCqq__collectionImage\",\n  \"collectionName\": \"collectionsCarousel-module____MCqq__collectionName\",\n  \"collectionsSection\": \"collectionsCarousel-module____MCqq__collectionsSection\",\n  \"container\": \"collectionsCarousel-module____MCqq__container\",\n  \"errorContainer\": \"collectionsCarousel-module____MCqq__errorContainer\",\n  \"header\": \"collectionsCarousel-module____MCqq__header\",\n  \"headerContent\": \"collectionsCarousel-module____MCqq__headerContent\",\n  \"imageContainer\": \"collectionsCarousel-module____MCqq__imageContainer\",\n  \"imageOverlay\": \"collectionsCarousel-module____MCqq__imageOverlay\",\n  \"loadingContainer\": \"collectionsCarousel-module____MCqq__loadingContainer\",\n  \"loadingSpinner\": \"collectionsCarousel-module____MCqq__loadingSpinner\",\n  \"navButton\": \"collectionsCarousel-module____MCqq__navButton\",\n  \"navigation\": \"collectionsCarousel-module____MCqq__navigation\",\n  \"noImagePlaceholder\": \"collectionsCarousel-module____MCqq__noImagePlaceholder\",\n  \"noImageText\": \"collectionsCarousel-module____MCqq__noImageText\",\n  \"productCount\": \"collectionsCarousel-module____MCqq__productCount\",\n  \"spin\": \"collectionsCarousel-module____MCqq__spin\",\n  \"subtitle\": \"collectionsCarousel-module____MCqq__subtitle\",\n  \"title\": \"collectionsCarousel-module____MCqq__title\",\n  \"viewAllButton\": \"collectionsCarousel-module____MCqq__viewAllButton\",\n  \"viewAllContainer\": \"collectionsCarousel-module____MCqq__viewAllContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1365, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/CollectionsCarousel/CollectionsCarousel.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { collectionGetService } from '../../../services/api/collections';\r\nimport { CollectionHierarchy } from '../../../services/types/entities';\r\nimport styles from './collectionsCarousel.module.css';\r\n\r\ninterface CollectionsCarouselProps {\r\n  title?: string;\r\n  subtitle?: string;\r\n}\r\n\r\nconst CollectionsCarousel: React.FC<CollectionsCarouselProps> = ({\r\n  title = \"Featured Collections\",\r\n  subtitle = \"Explore our curated selection of handcrafted cast stone collections\"\r\n}) => {\r\n  const [collections, setCollections] = useState<CollectionHierarchy[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchCollections = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        setError(null);\r\n        const hierarchyData = await collectionGetService.getHierarchy();\r\n        setCollections(hierarchyData);\r\n      } catch (err) {\r\n        console.error('Failed to fetch collections:', err);\r\n        setError('Failed to load collections');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCollections();\r\n  }, []);\r\n\r\n  const scrollLeft = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({\r\n        left: -300,\r\n        behavior: 'smooth'\r\n      });\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({\r\n        left: 300,\r\n        behavior: 'smooth'\r\n      });\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <section className={styles.collectionsSection}>\r\n        <div className={styles.container}>\r\n          <div className={styles.header}>\r\n            <h2 className={styles.title}>{title}</h2>\r\n            <p className={styles.subtitle}>{subtitle}</p>\r\n          </div>\r\n          <div className={styles.loadingContainer}>\r\n            <div className={styles.loadingSpinner}></div>\r\n            <p>Loading collections...</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <section className={styles.collectionsSection}>\r\n        <div className={styles.container}>\r\n          <div className={styles.header}>\r\n            <h2 className={styles.title}>{title}</h2>\r\n            <p className={styles.subtitle}>{subtitle}</p>\r\n          </div>\r\n          <div className={styles.errorContainer}>\r\n            <p>Unable to load collections. Please try again later.</p>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <section className={styles.collectionsSection}>\r\n      <div className={styles.container}>\r\n        <div className={styles.header}>\r\n          <div className={styles.headerContent}>\r\n            <h2 className={styles.title}>{title}</h2>\r\n            <p className={styles.subtitle}>{subtitle}</p>\r\n          </div>\r\n\r\n          <div className={styles.navigation}>\r\n            <button\r\n              className={styles.navButton}\r\n              onClick={scrollLeft}\r\n              aria-label=\"Scroll left\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n              </svg>\r\n            </button>\r\n            <button\r\n              className={styles.navButton}\r\n              onClick={scrollRight}\r\n              aria-label=\"Scroll right\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className={styles.carouselContainer}>\r\n          <div\r\n            ref={scrollContainerRef}\r\n            className={styles.carousel}\r\n          >\r\n            {collections.map((collection) => (\r\n              <div key={collection.id} className={styles.collectionCard}>\r\n                <Link href={`/collections/${collection.id}`} className={styles.cardLink}>\r\n                  {Array.isArray(collection.images) && collection.images.length > 0 ? (\r\n                    <div className={styles.imageContainer}>\r\n                      <Image\r\n                        src={collection.images[0]}\r\n                        alt={collection.name}\r\n                        fill\r\n                        className={styles.collectionImage}\r\n                        sizes=\"(max-width: 768px) 280px, 320px\"\r\n                      />\r\n                      <div className={styles.imageOverlay}></div>\r\n                    </div>\r\n                  ) : (\r\n                    <div className={styles.imageContainer}>\r\n                      <div className={styles.noImagePlaceholder}>\r\n                        <span className={styles.noImageText}>No Image</span>\r\n                      </div>\r\n                      <div className={styles.imageOverlay}></div>\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className={styles.cardContent}>\r\n                    <div className={styles.productCount}>\r\n                      {collection.productCount} Products\r\n                    </div>\r\n                    <h3 className={styles.collectionName}>{collection.name}</h3>\r\n                    {collection.description && (\r\n                      <p className={styles.collectionDescription}>\r\n                        {collection.description}\r\n                      </p>\r\n                    )}\r\n\r\n                    <div className={styles.cardAction}>\r\n                      <span className={styles.actionText}>Explore Collection</span>\r\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                        <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n        <div className={styles.viewAllContainer}>\r\n          <Link href=\"/collections\" className={styles.viewAllButton}>\r\n            <span>View All Collections</span>\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n              <path d=\"M7 17L17 7M17 7H7M17 7V17\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n            </svg>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default CollectionsCarousel;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAPA;;;;;;;AAcA,MAAM,sBAA0D,CAAC,EAC/D,QAAQ,sBAAsB,EAC9B,WAAW,qEAAqE,EACjF;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,SAAS;gBACT,MAAM,gBAAgB,MAAM,4IAAA,CAAA,uBAAoB,CAAC,YAAY;gBAC7D,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAClC,MAAM,CAAC;gBACP,UAAU;YACZ;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAClC,MAAM;gBACN,UAAU;YACZ;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;sBAC3C,cAAA,8OAAC;gBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;0CAAG;;;;;;0CAC9B,8OAAC;gCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;;;;;;;kCAElC,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,8OAAC;gCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;;;;;;0CACrC,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;sBAC3C,cAAA,8OAAC;gBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;0CAC3B,8OAAC;gCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;0CAAG;;;;;;0CAC9B,8OAAC;gCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;;;;;;;kCAElC,8OAAC;wBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC;QAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;kBAC3C,cAAA,8OAAC;YAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;8CAClC,8OAAC;oCAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;8CAAG;;;;;;8CAC9B,8OAAC;oCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;8CAAG;;;;;;;;;;;;sCAGlC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;8CAC/B,8OAAC;oCACC,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;oCAC3B,SAAS;oCACT,cAAW;8CAEX,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;kDACnD,cAAA,8OAAC;4CAAK,GAAE;4CAAmB,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;;;;;;;;;;;;;;;;8CAG1G,8OAAC;oCACC,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;oCAC3B,SAAS;oCACT,cAAW;8CAEX,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;kDACnD,cAAA,8OAAC;4CAAK,GAAE;4CAAkB,QAAO;4CAAe,aAAY;4CAAI,eAAc;4CAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM7G,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,iBAAiB;8BACtC,cAAA,8OAAC;wBACC,KAAK;wBACL,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;kCAEzB,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;gCAAwB,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;0CACvD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;oCAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;;wCACpE,MAAM,OAAO,CAAC,WAAW,MAAM,KAAK,WAAW,MAAM,CAAC,MAAM,GAAG,kBAC9D,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;;8DACnC,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,WAAW,MAAM,CAAC,EAAE;oDACzB,KAAK,WAAW,IAAI;oDACpB,IAAI;oDACJ,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;oDACjC,OAAM;;;;;;8DAER,8OAAC;oDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;iEAGrC,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;;8DACnC,8OAAC;oDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;8DACvC,cAAA,8OAAC;wDAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;kEAAE;;;;;;;;;;;8DAEvC,8OAAC;oDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;;sDAIvC,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;;8DAChC,8OAAC;oDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;;wDAChC,WAAW,YAAY;wDAAC;;;;;;;8DAE3B,8OAAC;oDAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,cAAc;8DAAG,WAAW,IAAI;;;;;;gDACrD,WAAW,WAAW,kBACrB,8OAAC;oDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,qBAAqB;8DACvC,WAAW,WAAW;;;;;;8DAI3B,8OAAC;oDAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;sEAC/B,8OAAC;4DAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;sEAAE;;;;;;sEACpC,8OAAC;4DAAI,OAAM;4DAAK,QAAO;4DAAK,SAAQ;4DAAY,MAAK;sEACnD,cAAA,8OAAC;gEAAK,GAAE;gEAA4B,QAAO;gEAAe,aAAY;gEAAI,eAAc;gEAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BApC/G,WAAW,EAAE;;;;;;;;;;;;;;;8BA6C7B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,gBAAgB;8BACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAe,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;0CACvD,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;0CACnD,cAAA,8OAAC;oCAAK,GAAE;oCAA4B,QAAO;oCAAe,aAAY;oCAAI,eAAc;oCAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7H;uCAEe", "debugId": null}}, {"offset": {"line": 1876, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"testimonialsSection-module__2JZFAW__active\",\n  \"authorCompany\": \"testimonialsSection-module__2JZFAW__authorCompany\",\n  \"authorDetails\": \"testimonialsSection-module__2JZFAW__authorDetails\",\n  \"authorImage\": \"testimonialsSection-module__2JZFAW__authorImage\",\n  \"authorInfo\": \"testimonialsSection-module__2JZFAW__authorInfo\",\n  \"authorName\": \"testimonialsSection-module__2JZFAW__authorName\",\n  \"authorPhoto\": \"testimonialsSection-module__2JZFAW__authorPhoto\",\n  \"authorTitle\": \"testimonialsSection-module__2JZFAW__authorTitle\",\n  \"container\": \"testimonialsSection-module__2JZFAW__container\",\n  \"description\": \"testimonialsSection-module__2JZFAW__description\",\n  \"header\": \"testimonialsSection-module__2JZFAW__header\",\n  \"navDot\": \"testimonialsSection-module__2JZFAW__navDot\",\n  \"navigation\": \"testimonialsSection-module__2JZFAW__navigation\",\n  \"projectInfo\": \"testimonialsSection-module__2JZFAW__projectInfo\",\n  \"projectLabel\": \"testimonialsSection-module__2JZFAW__projectLabel\",\n  \"projectName\": \"testimonialsSection-module__2JZFAW__projectName\",\n  \"quoteIcon\": \"testimonialsSection-module__2JZFAW__quoteIcon\",\n  \"rating\": \"testimonialsSection-module__2JZFAW__rating\",\n  \"stat\": \"testimonialsSection-module__2JZFAW__stat\",\n  \"statLabel\": \"testimonialsSection-module__2JZFAW__statLabel\",\n  \"statNumber\": \"testimonialsSection-module__2JZFAW__statNumber\",\n  \"stats\": \"testimonialsSection-module__2JZFAW__stats\",\n  \"subtitle\": \"testimonialsSection-module__2JZFAW__subtitle\",\n  \"testimonialContent\": \"testimonialsSection-module__2JZFAW__testimonialContent\",\n  \"testimonialMeta\": \"testimonialsSection-module__2JZFAW__testimonialMeta\",\n  \"testimonialText\": \"testimonialsSection-module__2JZFAW__testimonialText\",\n  \"testimonialsContainer\": \"testimonialsSection-module__2JZFAW__testimonialsContainer\",\n  \"testimonialsSection\": \"testimonialsSection-module__2JZFAW__testimonialsSection\",\n  \"title\": \"testimonialsSection-module__2JZFAW__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/TestimonialsSection/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\nimport styles from './testimonialsSection.module.css';\r\n\r\ninterface Testimonial {\r\n  id: string;\r\n  name: string;\r\n  title: string;\r\n  company: string;\r\n  content: string;\r\n  image: string;\r\n  rating: number;\r\n  project: string;\r\n}\r\n\r\ninterface TestimonialsSectionProps {\r\n  testimonials?: Testimonial[];\r\n}\r\n\r\nconst defaultTestimonials: Testimonial[] = [\r\n  {\r\n    id: '1',\r\n    name: '<PERSON>',\r\n    title: 'Interior Designer',\r\n    company: 'Mitchell Design Studio',\r\n    content: 'Cast Stone has transformed our projects with their exquisite craftsmanship. The attention to detail and quality of their architectural elements is unmatched. Our clients are consistently amazed by the timeless elegance these pieces bring to their spaces.',\r\n    image: '/images/testimonials/sarah-mitchell.jpg',\r\n    rating: 5,\r\n    project: 'Luxury Residential Project'\r\n  },\r\n  {\r\n    id: '2',\r\n    name: '<PERSON>',\r\n    title: 'Architect',\r\n    company: 'Chen Architecture Group',\r\n    content: 'Working with Cast Stone has been a game-changer for our firm. Their ability to create custom pieces that perfectly match our architectural vision is remarkable. The durability and beauty of their cast stone elements make them our go-to choice for premium projects.',\r\n    image: '/images/testimonials/michael-chen.jpg',\r\n    rating: 5,\r\n    project: 'Commercial Heritage Building'\r\n  },\r\n  {\r\n    id: '3',\r\n    name: '<PERSON>',\r\n    title: 'Project Manager',\r\n    company: 'Elite Construction',\r\n    content: 'The professionalism and expertise of the Cast Stone team is exceptional. From initial consultation to final installation, every step was handled with precision. The end result exceeded our expectations and our client\\'s vision was brought to life beautifully.',\r\n    image: '/images/testimonials/emma-rodriguez.jpg',\r\n    rating: 5,\r\n    project: 'Boutique Hotel Renovation'\r\n  }\r\n];\r\n\r\nconst TestimonialsSection: React.FC<TestimonialsSectionProps> = ({\r\n  testimonials = defaultTestimonials\r\n}) => {\r\n  const [activeTestimonial, setActiveTestimonial] = useState(0);\r\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (!isAutoPlaying) return;\r\n\r\n    const interval = setInterval(() => {\r\n      setActiveTestimonial((prev) => \r\n        prev === testimonials.length - 1 ? 0 : prev + 1\r\n      );\r\n    }, 5000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAutoPlaying, testimonials.length]);\r\n\r\n  const handleTestimonialChange = (index: number) => {\r\n    setActiveTestimonial(index);\r\n    setIsAutoPlaying(false);\r\n    \r\n    // Resume auto-play after 10 seconds\r\n    setTimeout(() => setIsAutoPlaying(true), 10000);\r\n  };\r\n\r\n  const renderStars = (rating: number) => {\r\n    return Array.from({ length: 5 }, (_, index) => (\r\n      <svg\r\n        key={index}\r\n        width=\"16\"\r\n        height=\"16\"\r\n        viewBox=\"0 0 24 24\"\r\n        fill={index < rating ? \"#d4af8c\" : \"none\"}\r\n        stroke={index < rating ? \"#d4af8c\" : \"#e5e5e5\"}\r\n        strokeWidth=\"1\"\r\n      >\r\n        <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\" />\r\n      </svg>\r\n    ));\r\n  };\r\n\r\n  return (\r\n    <section className={styles.testimonialsSection}>\r\n      <div className={styles.container}>\r\n        <div className={styles.header}>\r\n          <span className={styles.subtitle}>Client Testimonials</span>\r\n          <h2 className={styles.title}>What Our Clients Say</h2>\r\n          <p className={styles.description}>\r\n            Discover why architects, designers, and homeowners trust Cast Stone \r\n            for their most important projects.\r\n          </p>\r\n        </div>\r\n\r\n        <div className={styles.testimonialsContainer}>\r\n          <div className={styles.testimonialContent}>\r\n            <div className={styles.quoteIcon}>\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path d=\"M3 21C3 17.5 5.5 15 9 15C9.5 15 10 15.1 10.5 15.2C10.2 14.2 10 13.1 10 12C10 8.7 12.7 6 16 6V4C11.6 4 8 7.6 8 12C8 12.4 8 12.7 8.1 13.1C6.8 13.6 5.7 14.4 4.9 15.5C3.8 17.1 3 19 3 21Z\" fill=\"currentColor\"/>\r\n                <path d=\"M13 21C13 17.5 15.5 15 19 15C19.5 15 20 15.1 20.5 15.2C20.2 14.2 20 13.1 20 12C20 8.7 22.7 6 26 6V4C21.6 4 18 7.6 18 12C18 12.4 18 12.7 18.1 13.1C16.8 13.6 15.7 14.4 14.9 15.5C13.8 17.1 13 19 13 21Z\" fill=\"currentColor\"/>\r\n              </svg>\r\n            </div>\r\n\r\n            <div className={styles.testimonialText}>\r\n              <p className={styles.testimonialContent}>\r\n                &quot;{testimonials[activeTestimonial].content}&quot;\r\n              </p>\r\n              \r\n              <div className={styles.rating}>\r\n                {renderStars(testimonials[activeTestimonial].rating)}\r\n              </div>\r\n              \r\n              <div className={styles.projectInfo}>\r\n                <span className={styles.projectLabel}>Project:</span>\r\n                <span className={styles.projectName}>\r\n                  {testimonials[activeTestimonial].project}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className={styles.testimonialMeta}>\r\n            <div className={styles.authorInfo}>\r\n              <div className={styles.authorImage}>\r\n                <Image\r\n                  src={testimonials[activeTestimonial].image}\r\n                  alt={testimonials[activeTestimonial].name}\r\n                  fill\r\n                  className={styles.authorPhoto}\r\n                  sizes=\"80px\"\r\n                  onError={(e) => {\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.src = '/images/placeholder-avatar.jpg';\r\n                  }}\r\n                />\r\n              </div>\r\n              \r\n              <div className={styles.authorDetails}>\r\n                <h4 className={styles.authorName}>\r\n                  {testimonials[activeTestimonial].name}\r\n                </h4>\r\n                <p className={styles.authorTitle}>\r\n                  {testimonials[activeTestimonial].title}\r\n                </p>\r\n                <p className={styles.authorCompany}>\r\n                  {testimonials[activeTestimonial].company}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className={styles.navigation}>\r\n              {testimonials.map((_, index) => (\r\n                <button\r\n                  key={index}\r\n                  className={`${styles.navDot} ${\r\n                    index === activeTestimonial ? styles.active : ''\r\n                  }`}\r\n                  onClick={() => handleTestimonialChange(index)}\r\n                  aria-label={`View testimonial ${index + 1}`}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className={styles.stats}>\r\n          <div className={styles.stat}>\r\n            <span className={styles.statNumber}>25+</span>\r\n            <span className={styles.statLabel}>Years Experience</span>\r\n          </div>\r\n          <div className={styles.stat}>\r\n            <span className={styles.statNumber}>1000+</span>\r\n            <span className={styles.statLabel}>Projects Completed</span>\r\n          </div>\r\n          <div className={styles.stat}>\r\n            <span className={styles.statNumber}>98%</span>\r\n            <span className={styles.statLabel}>Client Satisfaction</span>\r\n          </div>\r\n          <div className={styles.stat}>\r\n            <span className={styles.statNumber}>50+</span>\r\n            <span className={styles.statLabel}>Awards Won</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TestimonialsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAqBA,MAAM,sBAAqC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;CACD;AAED,MAAM,sBAA0D,CAAC,EAC/D,eAAe,mBAAmB,EACnC;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,qBAAqB,CAAC,OACpB,SAAS,aAAa,MAAM,GAAG,IAAI,IAAI,OAAO;QAElD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAe,aAAa,MAAM;KAAC;IAEvC,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;QACrB,iBAAiB;QAEjB,oCAAoC;QACpC,WAAW,IAAM,iBAAiB,OAAO;IAC3C;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,8OAAC;gBAEC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAM,QAAQ,SAAS,YAAY;gBACnC,QAAQ,QAAQ,SAAS,YAAY;gBACrC,aAAY;0BAEZ,cAAA,8OAAC;oBAAQ,QAAO;;;;;;eARX;;;;;IAWX;IAEA,qBACE,8OAAC;QAAQ,WAAW,mLAAA,CAAA,UAAM,CAAC,mBAAmB;kBAC5C,cAAA,8OAAC;YAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,QAAQ;sCAAE;;;;;;sCAClC,8OAAC;4BAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;sCAAE;;;;;;sCAC7B,8OAAC;4BAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;sCAAE;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,qBAAqB;;sCAC1C,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;;8CACvC,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAC9B,cAAA,8OAAC;wCAAI,OAAM;wCAAK,QAAO;wCAAK,SAAQ;wCAAY,MAAK;;0DACnD,8OAAC;gDAAK,GAAE;gDAAyL,MAAK;;;;;;0DACtM,8OAAC;gDAAK,GAAE;gDAAyM,MAAK;;;;;;;;;;;;;;;;;8CAI1N,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;;sDACpC,8OAAC;4CAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,kBAAkB;;gDAAE;gDAChC,YAAY,CAAC,kBAAkB,CAAC,OAAO;gDAAC;;;;;;;sDAGjD,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,MAAM;sDAC1B,YAAY,YAAY,CAAC,kBAAkB,CAAC,MAAM;;;;;;sDAGrD,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;;8DAChC,8OAAC;oDAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,YAAY;8DAAE;;;;;;8DACtC,8OAAC;oDAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;8DAChC,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,eAAe;;8CACpC,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;;sDAC/B,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;sDAChC,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,YAAY,CAAC,kBAAkB,CAAC,KAAK;gDAC1C,KAAK,YAAY,CAAC,kBAAkB,CAAC,IAAI;gDACzC,IAAI;gDACJ,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,OAAM;gDACN,SAAS,CAAC;oDACR,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,GAAG,GAAG;gDACf;;;;;;;;;;;sDAIJ,8OAAC;4CAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;;8DAClC,8OAAC;oDAAG,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8DAC7B,YAAY,CAAC,kBAAkB,CAAC,IAAI;;;;;;8DAEvC,8OAAC;oDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,WAAW;8DAC7B,YAAY,CAAC,kBAAkB,CAAC,KAAK;;;;;;8DAExC,8OAAC;oDAAE,WAAW,mLAAA,CAAA,UAAM,CAAC,aAAa;8DAC/B,YAAY,CAAC,kBAAkB,CAAC,OAAO;;;;;;;;;;;;;;;;;;8CAK9C,8OAAC;oCAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAC9B,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4CAEC,WAAW,GAAG,mLAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,EAC3B,UAAU,oBAAoB,mLAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;4CACF,SAAS,IAAM,wBAAwB;4CACvC,cAAY,CAAC,iBAAiB,EAAE,QAAQ,GAAG;2CALtC;;;;;;;;;;;;;;;;;;;;;;8BAYf,8OAAC;oBAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,KAAK;;sCAC1B,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;sCAErC,8OAAC;4BAAI,WAAW,mLAAA,CAAA,UAAM,CAAC,IAAI;;8CACzB,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,UAAU;8CAAE;;;;;;8CACpC,8OAAC;oCAAK,WAAW,mLAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C;uCAEe", "debugId": null}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/homeComponent.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"homeComponent\": \"homeComponent-module__wtPNwW__homeComponent\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/Home/HomeComponent.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport HeroSection from './HeroSection/HeroSection';\r\nimport CategoriesSection from './CategoriesSection/CategoriesSection';\r\nimport CatalogBanner from './CatalogBanner/CatalogBanner';\r\nimport CollectionsCarousel from './CollectionsCarousel/CollectionsCarousel';\r\nimport TestimonialsSection from './TestimonialsSection/TestimonialsSection';\r\nimport styles from './homeComponent.module.css';\r\n\r\ninterface HomeComponentProps {\r\n  title?: string;\r\n  subtitle?: string;\r\n}\r\n\r\nconst HomeComponent: React.FC<HomeComponentProps> = ({\r\n  title = \"Timeless Elegance in Cast Stone\",\r\n  subtitle = \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\r\n}) => {\r\n  return (\r\n    <main className={styles.homeComponent}>\r\n      {/* Hero Section with Image Carousel */}\r\n      <HeroSection\r\n        title={title}\r\n        subtitle={subtitle}\r\n      />\r\n\r\n      {/* Categories Grid Section */}\r\n      <CategoriesSection />\r\n\r\n      {/* Catalog Banner */}\r\n      <CatalogBanner />\r\n\r\n      {/* Collections Carousel */}\r\n      <CollectionsCarousel />\r\n\r\n      {/* Testimonials Section */}\r\n      <TestimonialsSection />\r\n    </main>\r\n  );\r\n};\r\n\r\nexport default HomeComponent;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAeA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,iCAAiC,EACzC,WAAW,qJAAqJ,EACjK;IACC,qBACE,8OAAC;QAAK,WAAW,sJAAA,CAAA,UAAM,CAAC,aAAa;;0BAEnC,8OAAC,wJAAA,CAAA,UAAW;gBACV,OAAO;gBACP,UAAU;;;;;;0BAIZ,8OAAC,oKAAA,CAAA,UAAiB;;;;;0BAGlB,8OAAC,4JAAA,CAAA,UAAa;;;;;0BAGd,8OAAC,wKAAA,CAAA,UAAmB;;;;;0BAGpB,8OAAC,wKAAA,CAAA,UAAmB;;;;;;;;;;;AAG1B;uCAEe", "debugId": null}}]}