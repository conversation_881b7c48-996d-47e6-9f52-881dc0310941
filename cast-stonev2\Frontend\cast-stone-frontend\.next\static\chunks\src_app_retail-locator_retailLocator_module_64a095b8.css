/* [project]/src/app/retail-locator/retailLocator.module.css [app-client] (css) */
.retailLocator-module__o1q4Zq__container {
  color: #fff;
  background: #1a1a2e;
  min-height: 100vh;
  padding: 2rem 1rem;
}

.retailLocator-module__o1q4Zq__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
}

.retailLocator-module__o1q4Zq__title {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 3rem;
  font-weight: 700;
}

.retailLocator-module__o1q4Zq__subtitle {
  color: #b0b0b0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.retailLocator-module__o1q4Zq__content {
  max-width: 1000px;
  margin: 0 auto;
}

.retailLocator-module__o1q4Zq__comingSoon {
  text-align: center;
  background: #2a2a3e;
  border: 2px solid #3a3a4e;
  border-radius: 16px;
  padding: 4rem 2rem;
}

.retailLocator-module__o1q4Zq__icon {
  color: #4a90e2;
  margin-bottom: 2rem;
}

.retailLocator-module__o1q4Zq__comingSoon h2 {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 600;
}

.retailLocator-module__o1q4Zq__comingSoon > p {
  color: #b0b0b0;
  max-width: 600px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.1rem;
  line-height: 1.6;
}

.retailLocator-module__o1q4Zq__features {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  display: grid;
}

.retailLocator-module__o1q4Zq__feature {
  background: #3a3a4e;
  border: 1px solid #4a4a5e;
  border-radius: 12px;
  padding: 2rem;
}

.retailLocator-module__o1q4Zq__feature h4 {
  color: #4a90e2;
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.retailLocator-module__o1q4Zq__feature p {
  color: #b0b0b0;
  line-height: 1.5;
}

.retailLocator-module__o1q4Zq__contactInfo {
  background: #3a3a4e;
  border: 1px solid #4a4a5e;
  border-radius: 12px;
  margin-top: 2rem;
  padding: 2rem;
}

.retailLocator-module__o1q4Zq__contactInfo h3 {
  color: #4a90e2;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.retailLocator-module__o1q4Zq__contactInfo > p {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
}

.retailLocator-module__o1q4Zq__contactDetails {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.retailLocator-module__o1q4Zq__contactItem {
  color: #e0e0e0;
  font-size: 1rem;
}

.retailLocator-module__o1q4Zq__contactItem strong {
  color: #fff;
  margin-right: .5rem;
}

@media (width <= 768px) {
  .retailLocator-module__o1q4Zq__title, .retailLocator-module__o1q4Zq__comingSoon h2 {
    font-size: 2rem;
  }

  .retailLocator-module__o1q4Zq__features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .retailLocator-module__o1q4Zq__container {
    padding: 1rem;
  }

  .retailLocator-module__o1q4Zq__comingSoon {
    padding: 2rem 1rem;
  }

  .retailLocator-module__o1q4Zq__contactDetails {
    text-align: left;
  }
}


/*# sourceMappingURL=src_app_retail-locator_retailLocator_module_64a095b8.css.map*/