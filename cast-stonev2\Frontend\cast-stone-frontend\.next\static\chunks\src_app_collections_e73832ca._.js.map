{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/collections/collections.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cardContent\": \"collections-module__IO8dua__cardContent\",\n  \"cardFooter\": \"collections-module__IO8dua__cardFooter\",\n  \"collectionCard\": \"collections-module__IO8dua__collectionCard\",\n  \"collectionDescription\": \"collections-module__IO8dua__collectionDescription\",\n  \"collectionImage\": \"collections-module__IO8dua__collectionImage\",\n  \"collectionName\": \"collections-module__IO8dua__collectionName\",\n  \"collectionsGrid\": \"collections-module__IO8dua__collectionsGrid\",\n  \"container\": \"collections-module__IO8dua__container\",\n  \"emptyState\": \"collections-module__IO8dua__emptyState\",\n  \"error\": \"collections-module__IO8dua__error\",\n  \"header\": \"collections-module__IO8dua__header\",\n  \"imageContainer\": \"collections-module__IO8dua__imageContainer\",\n  \"loading\": \"collections-module__IO8dua__loading\",\n  \"placeholderImage\": \"collections-module__IO8dua__placeholderImage\",\n  \"subtitle\": \"collections-module__IO8dua__subtitle\",\n  \"title\": \"collections-module__IO8dua__title\",\n  \"viewCollection\": \"collections-module__IO8dua__viewCollection\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/collections/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Collection } from '@/services/types/entities';\r\nimport { collectionService } from '@/services';\r\nimport styles from './collections.module.css';\r\n\r\nexport default function CollectionsPage() {\r\n  const [collections, setCollections] = useState<Collection[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    fetchCollections();\r\n  }, []);\r\n\r\n  const fetchCollections = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const data = await collectionService.get.getPublished();\r\n      setCollections(data);\r\n    } catch (error) {\r\n      console.error('Error fetching collections:', error);\r\n      setError('Failed to load collections');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={styles.container}>\r\n        <div className={styles.loading}>Loading collections...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className={styles.container}>\r\n        <div className={styles.error}>{error}</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.header}>\r\n        <h1 className={styles.title}>Our Collections</h1>\r\n        <p className={styles.subtitle}>\r\n          Explore our curated collections of handcrafted cast stone pieces\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.collectionsGrid}>\r\n        {collections.map((collection) => (\r\n          <Link\r\n            key={collection.id}\r\n            href={`/collections/${collection.id}`}\r\n            className={styles.collectionCard}\r\n          >\r\n            <div className={styles.imageContainer}>\r\n              {collection.images && collection.images.length > 0 ? (\r\n                <Image\r\n                  src={collection.images[0]}\r\n                  alt={collection.name}\r\n                  className={styles.collectionImage}\r\n                  width={350}\r\n                  height={250}\r\n                  style={{ objectFit: 'cover' }}\r\n                />\r\n              ) : (\r\n                <div className={styles.placeholderImage}>\r\n                  <span>No Image</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className={styles.cardContent}>\r\n              <h3 className={styles.collectionName}>{collection.name}</h3>\r\n              {collection.description && (\r\n                <p className={styles.collectionDescription}>\r\n                  {collection.description}\r\n                </p>\r\n              )}\r\n              <div className={styles.cardFooter}>\r\n                <span className={styles.viewCollection}>View Collection →</span>\r\n              </div>\r\n            </div>\r\n          </Link>\r\n        ))}\r\n      </div>\r\n\r\n      {collections.length === 0 && (\r\n        <div className={styles.emptyState}>\r\n          <h3>No collections available</h3>\r\n          <p>Check back soon for new collections.</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,aAAa;YACb,MAAM,OAAO,MAAM,iKAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,YAAY;YACrD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,OAAO;0BAAE;;;;;;;;;;;IAGtC;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,KAAK;0BAAG;;;;;;;;;;;IAGrC;IAEA,qBACE,6LAAC;QAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAG,WAAW,uJAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,6LAAC;wBAAE,WAAW,uJAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,eAAe;0BACnC,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,+JAAA,CAAA,UAAI;wBAEH,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;wBACrC,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;;0CAEhC,6LAAC;gCAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;0CAClC,WAAW,MAAM,IAAI,WAAW,MAAM,CAAC,MAAM,GAAG,kBAC/C,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,WAAW,MAAM,CAAC,EAAE;oCACzB,KAAK,WAAW,IAAI;oCACpB,WAAW,uJAAA,CAAA,UAAM,CAAC,eAAe;oCACjC,OAAO;oCACP,QAAQ;oCACR,OAAO;wCAAE,WAAW;oCAAQ;;;;;yDAG9B,6LAAC;oCAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,gBAAgB;8CACrC,cAAA,6LAAC;kDAAK;;;;;;;;;;;;;;;;0CAIZ,6LAAC;gCAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;wCAAG,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;kDAAG,WAAW,IAAI;;;;;;oCACrD,WAAW,WAAW,kBACrB,6LAAC;wCAAE,WAAW,uJAAA,CAAA,UAAM,CAAC,qBAAqB;kDACvC,WAAW,WAAW;;;;;;kDAG3B,6LAAC;wCAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,UAAU;kDAC/B,cAAA,6LAAC;4CAAK,WAAW,uJAAA,CAAA,UAAM,CAAC,cAAc;sDAAE;;;;;;;;;;;;;;;;;;uBA5BvC,WAAW,EAAE;;;;;;;;;;YAmCvB,YAAY,MAAM,KAAK,mBACtB,6LAAC;gBAAI,WAAW,uJAAA,CAAA,UAAM,CAAC,UAAU;;kCAC/B,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;AAKb;GA7FwB;KAAA", "debugId": null}}]}