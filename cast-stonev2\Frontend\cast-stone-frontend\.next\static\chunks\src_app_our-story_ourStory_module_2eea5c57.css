/* [project]/src/app/our-story/ourStory.module.css [app-client] (css) */
.ourStory-module__6h0toW__OurStoryroot {
  --cast-stone-brown: #4a3728;
  --cast-stone-light-brown: #6b4e3d;
  --cast-stone-cream: #faf9f7;
  --cast-stone-white: #fff;
  --cast-stone-gray: #8b7355;
  --cast-stone-shadow: #4a37281a;
  --cast-stone-shadow-hover: #4a372826;
  --transition-smooth: all .3s cubic-bezier(.4, 0, .2, 1);
  --transition-fast: all .2s cubic-bezier(.4, 0, .2, 1);
}

.ourStory-module__6h0toW__storyPage {
  background-color: var(--cast-stone-white);
  min-height: 100vh;
}

.ourStory-module__6h0toW__heroSection {
  background: linear-gradient(135deg, #8b4513 0%, sienna 50%, #8b4513 100%);
  padding: 8rem 0 6rem;
  position: relative;
  overflow: hidden;
}

.ourStory-module__6h0toW__heroSection:before {
  content: "";
  opacity: .3;
  background: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.02)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>");
  position: absolute;
  inset: 0;
}

.ourStory-module__6h0toW__heroContainer {
  text-align: center;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.ourStory-module__6h0toW__heroTitle {
  color: var(--cast-stone-white);
  text-shadow: 2px 2px 4px #0000004d;
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 4rem;
  font-weight: 700;
  animation: 1s ease-out forwards ourStory-module__6h0toW__fadeInUp;
}

.ourStory-module__6h0toW__heroSubtitle {
  color: #ffffffe6;
  text-shadow: 1px 1px 2px #0000004d;
  opacity: 0;
  max-width: 700px;
  margin: 0 auto;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
  animation: 1s ease-out .3s forwards ourStory-module__6h0toW__fadeInUp;
  transform: translateY(30px);
}

.ourStory-module__6h0toW__mainContent {
  background-color: var(--cast-stone-white);
  padding: 6rem 0;
}

.ourStory-module__6h0toW__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.ourStory-module__6h0toW__sectionTitle {
  color: var(--cast-stone-brown);
  text-align: center;
  letter-spacing: -.01em;
  margin-bottom: 3rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
  position: relative;
}

.ourStory-module__6h0toW__sectionTitle:after {
  content: "";
  background: linear-gradient(90deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  border-radius: 2px;
  width: 80px;
  height: 3px;
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
}

.ourStory-module__6h0toW__introSection {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  margin-bottom: 6rem;
  padding: 4rem 0;
  display: grid;
}

.ourStory-module__6h0toW__introContent {
  flex-direction: column;
  gap: 1.5rem;
  display: flex;
}

.ourStory-module__6h0toW__introText {
  color: var(--cast-stone-gray);
  text-align: justify;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
}

.ourStory-module__6h0toW__introImage {
  position: relative;
}

.ourStory-module__6h0toW__imagePlaceholder {
  background: linear-gradient(135deg, var(--cast-stone-cream), #f0ede8);
  border: 2px solid #4a37281a;
  border-radius: 16px;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 400px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.ourStory-module__6h0toW__imagePlaceholder:before {
  content: "";
  background: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"texture\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><rect width=\"20\" height=\"20\" fill=\"rgba(74,55,40,0.02)\"/><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(74,55,40,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23texture)\"/></svg>");
  position: absolute;
  inset: 0;
}

.ourStory-module__6h0toW__imageText {
  color: var(--cast-stone-brown);
  z-index: 2;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.2rem;
  font-weight: 600;
  position: relative;
}

.ourStory-module__6h0toW__heritageSection {
  background: var(--cast-stone-cream);
  border-radius: 24px;
  margin-bottom: 6rem;
  padding: 4rem 0;
}

.ourStory-module__6h0toW__heritageGrid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  display: grid;
}

.ourStory-module__6h0toW__heritageCard {
  background: var(--cast-stone-white);
  text-align: center;
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  transition: var(--transition-smooth);
  border: 1px solid #4a372814;
  border-radius: 16px;
  padding: 2.5rem;
}

.ourStory-module__6h0toW__heritageCard:hover {
  box-shadow: 0 16px 40px var(--cast-stone-shadow-hover);
  transform: translateY(-8px);
}

.ourStory-module__6h0toW__heritageIcon {
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  width: 80px;
  height: 80px;
  color: var(--cast-stone-white);
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  margin: 0 auto 1.5rem;
  display: flex;
}

.ourStory-module__6h0toW__heritageTitle {
  color: var(--cast-stone-brown);
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
}

.ourStory-module__6h0toW__heritageText {
  color: var(--cast-stone-gray);
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
}

.ourStory-module__6h0toW__timelineSection {
  margin-bottom: 6rem;
  padding: 4rem 0;
}

.ourStory-module__6h0toW__timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.ourStory-module__6h0toW__timeline:before {
  content: "";
  background: linear-gradient(180deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  width: 3px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.ourStory-module__6h0toW__timelineItem {
  align-items: center;
  margin-bottom: 3rem;
  display: flex;
  position: relative;
}

.ourStory-module__6h0toW__timelineItem:nth-child(odd) {
  flex-direction: row;
}

.ourStory-module__6h0toW__timelineItem:nth-child(2n) {
  flex-direction: row-reverse;
}

.ourStory-module__6h0toW__timelineYear {
  color: var(--cast-stone-white);
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  text-align: center;
  z-index: 2;
  min-width: 100px;
  box-shadow: 0 4px 12px var(--cast-stone-shadow);
  border-radius: 50px;
  padding: 1rem 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
}

.ourStory-module__6h0toW__timelineContent {
  background: var(--cast-stone-white);
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  border: 1px solid #4a372814;
  border-radius: 16px;
  flex: 1;
  margin: 0 2rem;
  padding: 2rem;
}

.ourStory-module__6h0toW__timelineTitle {
  color: var(--cast-stone-brown);
  margin-bottom: .5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.3rem;
  font-weight: 600;
}

.ourStory-module__6h0toW__timelineText {
  color: var(--cast-stone-gray);
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
}

.ourStory-module__6h0toW__valuesSection {
  background: var(--cast-stone-cream);
  border-radius: 24px;
  margin-bottom: 6rem;
  padding: 4rem 0;
}

.ourStory-module__6h0toW__valuesGrid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  display: grid;
}

.ourStory-module__6h0toW__valueCard {
  background: var(--cast-stone-white);
  box-shadow: 0 8px 24px var(--cast-stone-shadow);
  transition: var(--transition-smooth);
  border: 1px solid #4a372814;
  border-radius: 16px;
  padding: 2rem;
}

.ourStory-module__6h0toW__valueCard:hover {
  box-shadow: 0 12px 32px var(--cast-stone-shadow-hover);
  transform: translateY(-4px);
}

.ourStory-module__6h0toW__valueTitle {
  color: var(--cast-stone-brown);
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.3rem;
  font-weight: 600;
}

.ourStory-module__6h0toW__valueText {
  color: var(--cast-stone-gray);
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
}

.ourStory-module__6h0toW__ctaSection {
  text-align: center;
  background: linear-gradient(135deg, var(--cast-stone-brown), var(--cast-stone-light-brown));
  color: var(--cast-stone-white);
  border-radius: 24px;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.ourStory-module__6h0toW__ctaSection:before {
  content: "";
  opacity: .3;
  background: url("data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"cta-grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23cta-grain)\"/></svg>");
  position: absolute;
  inset: 0;
}

.ourStory-module__6h0toW__ctaContent {
  z-index: 2;
  position: relative;
}

.ourStory-module__6h0toW__ctaTitle {
  letter-spacing: -.01em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
}

.ourStory-module__6h0toW__ctaText {
  opacity: .9;
  max-width: 600px;
  margin-bottom: 2.5rem;
  margin-left: auto;
  margin-right: auto;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.2rem;
}

.ourStory-module__6h0toW__ctaButtons {
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  display: flex;
}

.ourStory-module__6h0toW__primaryButton, .ourStory-module__6h0toW__secondaryButton {
  transition: var(--transition-smooth);
  text-transform: uppercase;
  letter-spacing: .1em;
  text-align: center;
  border-radius: 50px;
  min-width: 200px;
  padding: 1.25rem 2.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
}

.ourStory-module__6h0toW__primaryButton {
  background: var(--cast-stone-white);
  color: var(--cast-stone-brown);
  border: 2px solid var(--cast-stone-white);
}

.ourStory-module__6h0toW__primaryButton:hover {
  color: var(--cast-stone-white);
  background: none;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #fff3;
}

.ourStory-module__6h0toW__secondaryButton {
  color: var(--cast-stone-white);
  border: 2px solid var(--cast-stone-white);
  background: none;
}

.ourStory-module__6h0toW__secondaryButton:hover {
  background: var(--cast-stone-white);
  color: var(--cast-stone-brown);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #fff3;
}

@keyframes ourStory-module__6h0toW__fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (width <= 1024px) {
  .ourStory-module__6h0toW__introSection {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .ourStory-module__6h0toW__heroTitle {
    font-size: 3rem;
  }

  .ourStory-module__6h0toW__sectionTitle {
    font-size: 2rem;
  }
}

@media (width <= 768px) {
  .ourStory-module__6h0toW__heroSection {
    padding: 6rem 0 4rem;
  }

  .ourStory-module__6h0toW__heroTitle {
    font-size: 2.5rem;
  }

  .ourStory-module__6h0toW__heroSubtitle {
    font-size: 1.1rem;
  }

  .ourStory-module__6h0toW__mainContent {
    padding: 4rem 0;
  }

  .ourStory-module__6h0toW__container {
    padding: 0 1rem;
  }

  .ourStory-module__6h0toW__timeline:before {
    left: 2rem;
  }

  .ourStory-module__6h0toW__timelineItem {
    padding-left: 4rem;
    flex-direction: row !important;
  }

  .ourStory-module__6h0toW__timelineYear {
    min-width: 80px;
    padding: .75rem 1rem;
    font-size: 1.2rem;
    position: absolute;
    left: 0;
  }

  .ourStory-module__6h0toW__timelineContent {
    margin-left: 2rem;
    margin-right: 0;
  }

  .ourStory-module__6h0toW__ctaButtons {
    flex-direction: column;
    align-items: center;
  }
}

@media (width <= 480px) {
  .ourStory-module__6h0toW__heroTitle {
    font-size: 2rem;
  }

  .ourStory-module__6h0toW__heroSubtitle {
    font-size: 1rem;
  }

  .ourStory-module__6h0toW__sectionTitle {
    font-size: 1.75rem;
  }

  .ourStory-module__6h0toW__ctaTitle {
    font-size: 2rem;
  }

  .ourStory-module__6h0toW__heritageCard, .ourStory-module__6h0toW__valueCard, .ourStory-module__6h0toW__timelineContent {
    padding: 1.5rem;
  }
}


/*# sourceMappingURL=src_app_our-story_ourStory_module_2eea5c57.css.map*/