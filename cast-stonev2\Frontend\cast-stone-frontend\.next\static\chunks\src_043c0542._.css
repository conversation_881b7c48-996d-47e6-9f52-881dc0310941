/* [project]/src/components/cart/CartItem/cartItem.module.css [app-client] (css) */
.cartItem-module__IXYF0W__cartItem {
  background: #fff;
  border-radius: 8px;
  grid-template-columns: 120px 1fr auto auto auto;
  align-items: start;
  gap: 1.5rem;
  padding: 1.5rem;
  transition: box-shadow .3s;
  display: grid;
  box-shadow: 0 2px 8px #0000001a;
}

.cartItem-module__IXYF0W__cartItem:hover {
  box-shadow: 0 4px 16px #00000026;
}

.cartItem-module__IXYF0W__imageContainer {
  background: #f8f9fa;
  border-radius: 8px;
  width: 120px;
  height: 120px;
  overflow: hidden;
}

.cartItem-module__IXYF0W__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.cartItem-module__IXYF0W__productDetails {
  min-width: 0;
}

.cartItem-module__IXYF0W__productName {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.cartItem-module__IXYF0W__productDescription {
  color: #4b5563;
  margin: 0 0 .75rem;
  font-size: .9rem;
  line-height: 1.5;
}

.cartItem-module__IXYF0W__productMeta {
  flex-direction: column;
  gap: .25rem;
  margin-bottom: .5rem;
  display: flex;
}

.cartItem-module__IXYF0W__unitPrice {
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__collection {
  color: #2563eb;
  font-size: .85rem;
  font-style: italic;
}

.cartItem-module__IXYF0W__stockStatus {
  margin-top: .5rem;
}

.cartItem-module__IXYF0W__inStock {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__outOfStock {
  color: #dc2626;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__quantitySection {
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  min-width: 120px;
  display: flex;
}

.cartItem-module__IXYF0W__quantityLabel {
  color: #1f2937;
  text-align: center;
  font-size: .85rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__quantityControls {
  background: #f8f9fa;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: .25rem;
  display: flex;
}

.cartItem-module__IXYF0W__quantityBtn {
  color: #1f2937;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-weight: 600;
  transition: all .2s;
  display: flex;
}

.cartItem-module__IXYF0W__quantityBtn:hover:not(:disabled) {
  color: #fff;
  background: #2563eb;
  border-color: #2563eb;
}

.cartItem-module__IXYF0W__quantityBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartItem-module__IXYF0W__quantity {
  text-align: center;
  color: #1f2937;
  min-width: 40px;
  font-size: 1rem;
  font-weight: 600;
}

.cartItem-module__IXYF0W__updating {
  color: #2563eb;
  font-size: .75rem;
  font-style: italic;
}

.cartItem-module__IXYF0W__priceSection {
  flex-direction: column;
  align-items: flex-end;
  gap: .25rem;
  min-width: 120px;
  display: flex;
}

.cartItem-module__IXYF0W__itemTotal {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartItem-module__IXYF0W__priceBreakdown {
  color: #4b5563;
  font-size: .85rem;
}

.cartItem-module__IXYF0W__removeSection {
  align-items: flex-start;
  display: flex;
}

.cartItem-module__IXYF0W__removeBtn {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .2s;
  display: flex;
}

.cartItem-module__IXYF0W__removeBtn:hover:not(:disabled) {
  color: #b91c1c;
  background: #fee2e2;
}

.cartItem-module__IXYF0W__removeBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartItem-module__IXYF0W__removeBtn svg {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cartItem-module__IXYF0W__removing {
  font-size: 1.2rem;
  font-weight: bold;
}

@media (width <= 768px) {
  .cartItem-module__IXYF0W__cartItem {
    grid-template-columns: 80px 1fr;
    grid-template-areas: "image details"
                         "image quantity"
                         "image price"
                         "remove remove";
    gap: 1rem;
    padding: 1rem;
  }

  .cartItem-module__IXYF0W__imageContainer {
    grid-area: image;
    width: 80px;
    height: 80px;
  }

  .cartItem-module__IXYF0W__productDetails {
    grid-area: details;
  }

  .cartItem-module__IXYF0W__quantitySection {
    flex-direction: row;
    grid-area: quantity;
    justify-content: flex-start;
    align-items: center;
    min-width: auto;
  }

  .cartItem-module__IXYF0W__quantityLabel {
    margin-right: .5rem;
  }

  .cartItem-module__IXYF0W__priceSection {
    grid-area: price;
    align-items: flex-start;
    min-width: auto;
  }

  .cartItem-module__IXYF0W__removeSection {
    grid-area: remove;
    justify-content: center;
    margin-top: .5rem;
  }

  .cartItem-module__IXYF0W__productName {
    font-size: 1.1rem;
  }

  .cartItem-module__IXYF0W__itemTotal {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .cartItem-module__IXYF0W__cartItem {
    text-align: center;
    grid-template-columns: 1fr;
    grid-template-areas: "image"
                         "details"
                         "quantity"
                         "price"
                         "remove";
  }

  .cartItem-module__IXYF0W__imageContainer {
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }

  .cartItem-module__IXYF0W__quantitySection {
    justify-content: center;
  }

  .cartItem-module__IXYF0W__priceSection {
    align-items: center;
  }
}


/* [project]/src/components/cart/CartSummary/cartSummary.module.css [app-client] (css) */
.cartSummary-module__kqlNYa__cartSummary {
  background: #fff;
  border-radius: 8px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.cartSummary-module__kqlNYa__title {
  color: #1f2937;
  text-align: center;
  border-bottom: 2px solid #f3f4f6;
  margin: 0 0 1.5rem;
  padding-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__summaryDetails {
  margin-bottom: 1.5rem;
}

.cartSummary-module__kqlNYa__summaryRow {
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  display: flex;
}

.cartSummary-module__kqlNYa__label {
  color: #4b5563;
  font-size: 1rem;
  font-weight: 500;
}

.cartSummary-module__kqlNYa__value {
  color: #1f2937;
  font-size: 1rem;
  font-weight: 600;
}

.cartSummary-module__kqlNYa__freeShipping {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.cartSummary-module__kqlNYa__divider {
  background: #e5e7eb;
  height: 1px;
  margin: 1rem 0;
}

.cartSummary-module__kqlNYa__totalRow {
  border-top: 2px solid #2563eb;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding: 1rem 0;
  display: flex;
}

.cartSummary-module__kqlNYa__totalLabel {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__totalValue {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__shippingNotice {
  color: #0369a1;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  margin-bottom: 1.5rem;
  padding: .75rem;
  font-size: .9rem;
  display: flex;
}

.cartSummary-module__kqlNYa__infoIcon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.cartSummary-module__kqlNYa__actionButtons {
  flex-direction: column;
  gap: .75rem;
  margin-bottom: 1.5rem;
  display: flex;
}

.cartSummary-module__kqlNYa__checkoutBtn {
  color: #fff;
  cursor: pointer;
  background: #2563eb;
  border: none;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.cartSummary-module__kqlNYa__checkoutBtn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px #2563eb4d;
}

.cartSummary-module__kqlNYa__checkoutIcon {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cartSummary-module__kqlNYa__clearBtn {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: 2px solid #dc2626;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.cartSummary-module__kqlNYa__clearBtn:hover:not(:disabled) {
  color: #fff;
  background: #dc2626;
}

.cartSummary-module__kqlNYa__clearBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartSummary-module__kqlNYa__clearIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.cartSummary-module__kqlNYa__securityNotice {
  color: #059669;
  text-align: center;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  font-size: .9rem;
  font-weight: 600;
  display: flex;
}

.cartSummary-module__kqlNYa__securityIcon {
  stroke-width: 2px;
  width: 16px;
  height: 16px;
}

@media (width <= 1024px) {
  .cartSummary-module__kqlNYa__cartSummary {
    margin-top: 2rem;
    position: static;
  }
}

@media (width <= 768px) {
  .cartSummary-module__kqlNYa__cartSummary {
    margin-top: 1.5rem;
    padding: 1.5rem;
  }

  .cartSummary-module__kqlNYa__title {
    margin-bottom: 1rem;
    font-size: 1.25rem;
  }

  .cartSummary-module__kqlNYa__totalLabel {
    font-size: 1.1rem;
  }

  .cartSummary-module__kqlNYa__totalValue {
    font-size: 1.25rem;
  }

  .cartSummary-module__kqlNYa__checkoutBtn {
    padding: .875rem 1.25rem;
    font-size: .95rem;
  }

  .cartSummary-module__kqlNYa__actionButtons {
    gap: .5rem;
  }
}

@media (width <= 480px) {
  .cartSummary-module__kqlNYa__cartSummary {
    padding: 1rem;
  }

  .cartSummary-module__kqlNYa__summaryRow {
    margin-bottom: .5rem;
  }

  .cartSummary-module__kqlNYa__label, .cartSummary-module__kqlNYa__value {
    font-size: .9rem;
  }

  .cartSummary-module__kqlNYa__totalRow {
    padding: .75rem 0;
  }

  .cartSummary-module__kqlNYa__checkoutBtn {
    padding: .75rem 1rem;
    font-size: .9rem;
  }

  .cartSummary-module__kqlNYa__clearBtn {
    padding: .625rem .875rem;
    font-size: .85rem;
  }
}


/* [project]/src/app/cart/cart.module.css [app-client] (css) */
.cart-module__-RJi4G__container {
  max-width: 1200px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.cart-module__-RJi4G__header {
  text-align: center;
  border-bottom: 2px solid #f3f4f6;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
}

.cart-module__-RJi4G__title {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.cart-module__-RJi4G__subtitle {
  color: #4b5563;
  margin: 0;
  font-size: 1.1rem;
}

.cart-module__-RJi4G__loadingContainer {
  color: #4b5563;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  display: flex;
}

.cart-module__-RJi4G__loadingSpinner {
  border: 3px solid #f3f4f6;
  border-top-color: #2563eb;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite cart-module__-RJi4G__spin;
}

@keyframes cart-module__-RJi4G__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.cart-module__-RJi4G__emptyCart {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
  display: flex;
}

.cart-module__-RJi4G__emptyIcon {
  color: #d1d5db;
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
}

.cart-module__-RJi4G__emptyIcon svg {
  stroke-width: 1.5px;
  width: 100%;
  height: 100%;
}

.cart-module__-RJi4G__emptyTitle {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 2rem;
  font-weight: 700;
}

.cart-module__-RJi4G__emptyMessage {
  color: #4b5563;
  max-width: 500px;
  margin: 0 0 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.cart-module__-RJi4G__shopNowBtn {
  color: #fff;
  background: #2563eb;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
}

.cart-module__-RJi4G__shopNowBtn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px #2563eb4d;
}

.cart-module__-RJi4G__shopIcon {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cart-module__-RJi4G__errorMessage {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  align-items: center;
  gap: .75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  display: flex;
}

.cart-module__-RJi4G__errorIcon {
  stroke-width: 2px;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.cart-module__-RJi4G__cartContent {
  grid-template-columns: 1fr 350px;
  gap: 3rem;
  margin-bottom: 3rem;
  display: grid;
}

.cart-module__-RJi4G__cartItems {
  min-width: 0;
}

.cart-module__-RJi4G__itemsHeader {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  display: flex;
}

.cart-module__-RJi4G__itemsHeader h2 {
  color: #1f2937;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.cart-module__-RJi4G__continueShoppingLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  transition: color .2s;
}

.cart-module__-RJi4G__continueShoppingLink:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.cart-module__-RJi4G__itemsList {
  flex-direction: column;
  gap: 1.5rem;
  display: flex;
}

.cart-module__-RJi4G__additionalActions {
  border-top: 2px solid #f3f4f6;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding-top: 2rem;
  display: grid;
}

.cart-module__-RJi4G__helpSection, .cart-module__-RJi4G__shippingInfo {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.cart-module__-RJi4G__helpSection h3, .cart-module__-RJi4G__shippingInfo h3 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.cart-module__-RJi4G__helpSection p {
  color: #4b5563;
  margin: 0;
  line-height: 1.6;
}

.cart-module__-RJi4G__contactLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
}

.cart-module__-RJi4G__contactLink:hover {
  text-decoration: underline;
}

.cart-module__-RJi4G__shippingInfo ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.cart-module__-RJi4G__shippingInfo li {
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.cart-module__-RJi4G__shippingInfo li:last-child {
  border-bottom: none;
}

.cart-module__-RJi4G__shippingInfo li:before {
  content: "✓";
  color: #059669;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@media (width <= 1024px) {
  .cart-module__-RJi4G__cartContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (width <= 768px) {
  .cart-module__-RJi4G__container {
    padding: 1rem;
  }

  .cart-module__-RJi4G__header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .cart-module__-RJi4G__title {
    font-size: 2rem;
  }

  .cart-module__-RJi4G__subtitle {
    font-size: 1rem;
  }

  .cart-module__-RJi4G__cartContent {
    gap: 1.5rem;
  }

  .cart-module__-RJi4G__additionalActions {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cart-module__-RJi4G__helpSection, .cart-module__-RJi4G__shippingInfo {
    padding: 1rem;
  }

  .cart-module__-RJi4G__emptyIcon {
    width: 80px;
    height: 80px;
  }

  .cart-module__-RJi4G__emptyTitle {
    font-size: 1.5rem;
  }

  .cart-module__-RJi4G__emptyMessage {
    font-size: 1rem;
  }
}

@media (width <= 480px) {
  .cart-module__-RJi4G__container {
    padding: .5rem;
  }

  .cart-module__-RJi4G__title {
    font-size: 1.75rem;
  }

  .cart-module__-RJi4G__itemsHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: .5rem;
  }

  .cart-module__-RJi4G__shopNowBtn {
    padding: .875rem 1.5rem;
    font-size: 1rem;
  }
}


/*# sourceMappingURL=src_043c0542._.css.map*/