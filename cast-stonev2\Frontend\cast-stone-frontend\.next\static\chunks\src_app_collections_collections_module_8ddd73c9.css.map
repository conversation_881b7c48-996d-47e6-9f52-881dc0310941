{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/collections/collections.module.css"], "sourcesContent": [".container {\r\n  min-height: 100vh;\r\n  background: #1a1a2e;\r\n  color: white;\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.title {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 1.2rem;\r\n  color: #b0b0b0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.loading,\r\n.error {\r\n  text-align: center;\r\n  padding: 3rem;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.error {\r\n  color: #ff6b6b;\r\n}\r\n\r\n.collectionsGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.collectionCard {\r\n  background: #2a2a3e;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n  text-decoration: none;\r\n  color: inherit;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.collectionCard:hover {\r\n  transform: translateY(-5px);\r\n  border-color: #4a90e2;\r\n  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);\r\n}\r\n\r\n.imageContainer {\r\n  width: 100%;\r\n  height: 250px;\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.collectionImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.collectionCard:hover .collectionImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.placeholderImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #3a3a4e;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #888;\r\n  font-size: 1rem;\r\n}\r\n\r\n.cardContent {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.collectionName {\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n  color: white;\r\n}\r\n\r\n.collectionDescription {\r\n  color: #b0b0b0;\r\n  line-height: 1.5;\r\n  margin-bottom: 1rem;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.cardFooter {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.viewCollection {\r\n  color: #4a90e2;\r\n  font-weight: 500;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.emptyState {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #888;\r\n}\r\n\r\n.emptyState h3 {\r\n  font-size: 1.5rem;\r\n  margin-bottom: 0.5rem;\r\n  color: white;\r\n}\r\n\r\n.emptyState p {\r\n  font-size: 1rem;\r\n  color: #b0b0b0;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .collectionsGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;EACE;;;;EAIA;;;;;EAKA", "debugId": null}}]}