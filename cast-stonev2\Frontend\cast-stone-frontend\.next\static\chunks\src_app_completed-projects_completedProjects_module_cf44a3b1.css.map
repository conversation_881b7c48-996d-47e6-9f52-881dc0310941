{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/completed-projects/completedProjects.module.css"], "sourcesContent": [".container {\r\n  min-height: 100vh;\r\n  background: #1a1a2e;\r\n  color: white;\r\n  padding: 2rem 1rem;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.title {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 1.2rem;\r\n  color: #b0b0b0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.content {\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.comingSoon {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  background: #2a2a3e;\r\n  border-radius: 16px;\r\n  border: 2px solid #3a3a4e;\r\n}\r\n\r\n.icon {\r\n  color: #4a90e2;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.comingSoon h2 {\r\n  font-size: 2.5rem;\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n}\r\n\r\n.comingSoon > p {\r\n  font-size: 1.1rem;\r\n  color: #b0b0b0;\r\n  line-height: 1.6;\r\n  margin-bottom: 3rem;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.features {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  margin-top: 3rem;\r\n}\r\n\r\n.feature {\r\n  background: #3a3a4e;\r\n  padding: 2rem;\r\n  border-radius: 12px;\r\n  border: 1px solid #4a4a5e;\r\n}\r\n\r\n.feature h4 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n  color: #4a90e2;\r\n}\r\n\r\n.feature p {\r\n  color: #b0b0b0;\r\n  line-height: 1.5;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .comingSoon h2 {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .features {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .comingSoon {\r\n    padding: 2rem 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;EACE;;;;EAQA;;;;;EAKA;;;;EAIA", "debugId": null}}]}