/* [project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-client] (css) */
.productImageGallery-module__AcqG6a__imageGallery {
  width: 100%;
}

.productImageGallery-module__AcqG6a__mainImageContainer {
  aspect-ratio: 1;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.productImageGallery-module__AcqG6a__mainImage {
  object-fit: cover;
  cursor: zoom-in;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.productImageGallery-module__AcqG6a__mainImage.productImageGallery-module__AcqG6a__zoomed {
  cursor: zoom-out;
}

.productImageGallery-module__AcqG6a__navButton {
  color: #fff;
  cursor: pointer;
  background: #000000b3;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.productImageGallery-module__AcqG6a__navButton:hover {
  background: #000000e6;
}

.productImageGallery-module__AcqG6a__prevButton {
  left: 10px;
}

.productImageGallery-module__AcqG6a__nextButton {
  right: 10px;
}

.productImageGallery-module__AcqG6a__imageCounter {
  color: #fff;
  background: #000c;
  border-radius: 0;
  padding: .5rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
}

.productImageGallery-module__AcqG6a__zoomIndicator {
  color: #4a3728;
  background: #ffffffe6;
  border: 1px solid #ddd;
  border-radius: 0;
  align-items: center;
  gap: .5rem;
  padding: .5rem;
  font-size: .8rem;
  display: flex;
  position: absolute;
  top: 15px;
  right: 15px;
}

.productImageGallery-module__AcqG6a__thumbnailContainer {
  white-space: nowrap;
  padding: 8px 0;
  overflow: auto hidden;
}

.productImageGallery-module__AcqG6a__thumbnailGrid {
  gap: 8px;
  display: flex;
}

.productImageGallery-module__AcqG6a__thumbnail {
  cursor: pointer;
  background: none;
  border: none;
  outline: none;
  padding: 0;
  display: inline-block;
}

.productImageGallery-module__AcqG6a__thumbnailImage {
  object-fit: cover;
  border: 2px solid #0000;
  border-radius: 4px;
  width: 60px;
  height: 60px;
  transition: border .3s;
}

.productImageGallery-module__AcqG6a__activeThumbnail .productImageGallery-module__AcqG6a__thumbnailImage {
  border-color: #0070f3;
}

.productImageGallery-module__AcqG6a__thumbnail:hover {
  border-color: #4a3728;
}

.productImageGallery-module__AcqG6a__thumbnail.productImageGallery-module__AcqG6a__activeThumbnail {
  border-width: 3px;
  border-color: #4a3728;
}

.productImageGallery-module__AcqG6a__zoomOverlay {
  z-index: 1000;
  cursor: zoom-out;
  background: #000000e6;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  inset: 0;
}

.productImageGallery-module__AcqG6a__zoomedImageContainer {
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
}

.productImageGallery-module__AcqG6a__zoomedImage {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

.productImageGallery-module__AcqG6a__closeZoomButton {
  color: #4a3728;
  cursor: pointer;
  background: #ffffffe6;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: background-color .3s;
  display: flex;
  position: absolute;
  top: -50px;
  right: 0;
}

.productImageGallery-module__AcqG6a__closeZoomButton:hover {
  background: #fff;
}

@media (width <= 768px) {
  .productImageGallery-module__AcqG6a__navButton {
    width: 40px;
    height: 40px;
  }

  .productImageGallery-module__AcqG6a__prevButton {
    left: 5px;
  }

  .productImageGallery-module__AcqG6a__nextButton {
    right: 5px;
  }

  .productImageGallery-module__AcqG6a__imageCounter {
    padding: .25rem .75rem;
    font-size: .8rem;
    bottom: 10px;
  }

  .productImageGallery-module__AcqG6a__zoomIndicator {
    padding: .25rem;
    font-size: .7rem;
    top: 10px;
    right: 10px;
  }

  .productImageGallery-module__AcqG6a__thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: .25rem;
  }

  .productImageGallery-module__AcqG6a__zoomedImageContainer {
    max-width: 95vw;
    max-height: 95vh;
  }

  .productImageGallery-module__AcqG6a__closeZoomButton {
    width: 35px;
    height: 35px;
    top: -40px;
  }
}

@media (width <= 480px) {
  .productImageGallery-module__AcqG6a__mainImageContainer {
    margin-bottom: .5rem;
  }

  .productImageGallery-module__AcqG6a__navButton {
    width: 35px;
    height: 35px;
  }

  .productImageGallery-module__AcqG6a__thumbnailGrid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  }
}


/* [project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-client] (css) */
.productSpecifications-module__J4NrIa__specificationsContainer {
  width: 100%;
  margin-top: 2rem;
  margin-bottom: 15rem;
}

.productSpecifications-module__J4NrIa__section {
  background: #fff;
  border: 0 solid #ddd;
  border-radius: 0;
  margin-bottom: 1rem;
}

.productSpecifications-module__J4NrIa__sectionHeader {
  text-align: left;
  cursor: pointer;
  color: #0e0e0e;
  background: #f5f5f5;
  border: none;
  border-bottom: 1px solid #ddd;
  border-radius: 0;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  transition: background-color .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__sectionHeader:hover {
  background: #eee;
}

.productSpecifications-module__J4NrIa__sectionHeader.productSpecifications-module__J4NrIa__active {
  color: #000;
  background: #f5f5f5;
}

.productSpecifications-module__J4NrIa__toggleIcon {
  font-size: 1.5rem;
  font-weight: bold;
  transition: transform .3s;
}

.productSpecifications-module__J4NrIa__sectionContent {
  border-top: 1px solid #ddd;
  padding: 2rem;
}

.productSpecifications-module__J4NrIa__specGrid {
  flex-direction: column;
  gap: 1rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__specRow {
  border-bottom: 1px solid #f0f0f0;
  grid-template-columns: 200px 1fr;
  gap: 1rem;
  padding: .75rem 0;
  display: grid;
}

.productSpecifications-module__J4NrIa__specRow:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__specLabel {
  color: #000;
  font-size: .95rem;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__specValue {
  color: #333;
  font-size: .95rem;
}

.productSpecifications-module__J4NrIa__specValue.productSpecifications-module__J4NrIa__inStock {
  color: #28a745;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__specValue.productSpecifications-module__J4NrIa__outOfStock {
  color: #dc3545;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__tagContainer {
  flex-wrap: wrap;
  gap: .5rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__tag {
  color: #fff;
  background: #1e40af;
  border: 1px solid #1e40af;
  border-radius: 0;
  padding: .25rem .75rem;
  font-size: .8rem;
}

.productSpecifications-module__J4NrIa__detailsContent {
  line-height: 1.6;
}

.productSpecifications-module__J4NrIa__description {
  color: #555;
  margin-bottom: 2rem;
  font-size: 1rem;
}

.productSpecifications-module__J4NrIa__featureList h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications-module__J4NrIa__featureList ul {
  padding: 0;
  list-style: none;
}

.productSpecifications-module__J4NrIa__featureList li {
  border-bottom: 1px solid #f0f0f0;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.productSpecifications-module__J4NrIa__featureList li:before {
  content: "✓";
  color: #1e40af;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.productSpecifications-module__J4NrIa__featureList li:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__careContent {
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  display: grid;
}

.productSpecifications-module__J4NrIa__careSection h4, .productSpecifications-module__J4NrIa__downloadSection h4 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.productSpecifications-module__J4NrIa__careSection ul {
  padding: 0;
  list-style: none;
}

.productSpecifications-module__J4NrIa__careSection li {
  border-bottom: 1px solid #f0f0f0;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.productSpecifications-module__J4NrIa__careSection li:before {
  content: "•";
  color: #1e40af;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.productSpecifications-module__J4NrIa__careSection li:last-child {
  border-bottom: none;
}

.productSpecifications-module__J4NrIa__downloadLinks {
  flex-direction: column;
  gap: 1rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__downloadLink {
  color: #1e40af;
  border: 2px solid #1e40af;
  border-radius: 0;
  align-items: center;
  gap: .75rem;
  padding: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__downloadLink:hover {
  color: #fff;
  background: #1e40af;
}

.productSpecifications-module__J4NrIa__shareSection {
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1.5rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareLabel {
  color: #1e40af;
  font-size: 1rem;
  font-weight: 600;
}

.productSpecifications-module__J4NrIa__shareButtons {
  gap: .75rem;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareButton {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: none;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .3s;
  display: flex;
}

.productSpecifications-module__J4NrIa__shareButton:hover {
  background: #6b4e3d;
  transform: translateY(-2px);
}

@media (width <= 1024px) {
  .productSpecifications-module__J4NrIa__careContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (width <= 768px) {
  .productSpecifications-module__J4NrIa__specRow {
    grid-template-columns: 1fr;
    gap: .25rem;
  }

  .productSpecifications-module__J4NrIa__specLabel {
    font-weight: 700;
  }

  .productSpecifications-module__J4NrIa__downloadLink {
    padding: .75rem;
    font-size: .9rem;
  }

  .productSpecifications-module__J4NrIa__shareButton {
    width: 35px;
    height: 35px;
  }

  .productSpecifications-module__J4NrIa__tagContainer {
    gap: .25rem;
  }

  .productSpecifications-module__J4NrIa__tag {
    padding: .2rem .5rem;
    font-size: .75rem;
  }

  .productSpecifications-module__J4NrIa__specRow {
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: .5rem;
    flex-direction: row !important;
    display: flex !important;
  }

  .productSpecifications-module__J4NrIa__specLabel {
    flex: none;
    min-width: 100px;
    font-size: .9rem;
    font-weight: 600;
  }

  .productSpecifications-module__J4NrIa__specValue {
    text-align: left;
    word-break: break-word;
    flex: auto;
    font-size: .9rem;
  }

  .productSpecifications-module__J4NrIa__sectionHeader {
    padding: 1rem;
    font-size: 1rem;
  }

  .productSpecifications-module__J4NrIa__sectionContent {
    padding: 1rem;
  }

  .productSpecifications-module__J4NrIa__shareSection {
    text-align: center;
    flex-direction: column;
    align-items: stretch;
  }

  .productSpecifications-module__J4NrIa__shareButtons {
    justify-content: center;
  }
}


/* [project]/src/components/products/PatinaSelector/patinaSelector.module.css [app-client] (css) */
.patinaSelector-module__M14LiG__patinaSelector {
  background: #fafafa;
  border: 1px solid #ddd;
  margin: 1rem 0;
  padding: 1rem;
}

.patinaSelector-module__M14LiG__selectorHeader {
  border-bottom: 1px solid #2563eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: .75rem;
  display: flex;
}

.patinaSelector-module__M14LiG__selectorTitle {
  color: #1f2937;
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
}

.patinaSelector-module__M14LiG__selectedPatina {
  color: #2563eb;
  background: #fff;
  border: 1px solid #2563eb;
  padding: .4rem .75rem;
  font-size: .85rem;
  font-weight: 600;
}

.patinaSelector-module__M14LiG__patinaGrid {
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: .75rem;
  margin-bottom: 1rem;
  display: grid;
}

.patinaSelector-module__M14LiG__patinaOption {
  cursor: pointer;
  text-align: center;
  background: #fff;
  border: 1px solid #ddd;
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  min-height: 80px;
  padding: .75rem;
  transition: all .3s;
  display: flex;
}

.patinaSelector-module__M14LiG__patinaOption:hover {
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px #2563eb1a;
}

.patinaSelector-module__M14LiG__patinaOption.patinaSelector-module__M14LiG__selected {
  background: #eff6ff;
  border-width: 2px;
  border-color: #2563eb;
  box-shadow: 0 2px 8px #2563eb26;
}

.patinaSelector-module__M14LiG__patinaColor {
  border: 1px solid #333;
  width: 30px;
  height: 30px;
  box-shadow: inset 0 1px 2px #0000001a;
}

.patinaSelector-module__M14LiG__patinaName {
  color: #1f2937;
  font-size: .75rem;
  font-weight: 600;
  line-height: 1.1;
}

.patinaSelector-module__M14LiG__patinaOption.patinaSelector-module__M14LiG__selected .patinaSelector-module__M14LiG__patinaName {
  font-weight: 700;
}

.patinaSelector-module__M14LiG__patinaNote {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  margin-top: .75rem;
  padding: .75rem;
}

.patinaSelector-module__M14LiG__patinaNote p {
  color: #856404;
  margin: 0;
  font-size: .8rem;
  line-height: 1.3;
}

.patinaSelector-module__M14LiG__patinaNote strong {
  color: #533f03;
}

@media (width <= 768px) {
  .patinaSelector-module__M14LiG__patinaSelector {
    padding: .75rem;
  }

  .patinaSelector-module__M14LiG__selectorHeader {
    text-align: center;
    flex-direction: column;
    gap: .75rem;
  }

  .patinaSelector-module__M14LiG__patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaOption {
    gap: .4rem;
    min-height: 70px;
    padding: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaColor {
    width: 25px;
    height: 25px;
  }

  .patinaSelector-module__M14LiG__patinaName {
    font-size: .7rem;
  }

  .patinaSelector-module__M14LiG__selectorTitle {
    font-size: .95rem;
  }

  .patinaSelector-module__M14LiG__selectedPatina {
    padding: .35rem .6rem;
    font-size: .8rem;
  }
}

@media (width <= 480px) {
  .patinaSelector-module__M14LiG__patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  }

  .patinaSelector-module__M14LiG__patinaOption {
    min-height: 60px;
  }

  .patinaSelector-module__M14LiG__patinaNote {
    padding: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaNote p {
    font-size: .75rem;
  }
}

@media (prefers-reduced-motion: reduce) {
  .patinaSelector-module__M14LiG__patinaOption {
    transition: none;
  }

  .patinaSelector-module__M14LiG__patinaOption:hover {
    transform: none;
  }
}


/* [project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-client] (css) */
.relatedProducts-module__zj5_XW__relatedProducts {
  border-top: 2px solid #ddd;
  margin: 4rem 0;
  padding: 2rem 0;
}

.relatedProducts-module__zj5_XW__sectionHeader {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__sectionTitle {
  color: #1f2937;
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.relatedProducts-module__zj5_XW__scrollControls {
  gap: .5rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__scrollButton {
  color: #fff;
  cursor: pointer;
  background: #2563eb;
  border: 2px solid #2563eb;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 45px;
  transition: all .3s;
  display: flex;
}

.relatedProducts-module__zj5_XW__scrollButton:hover:not(.relatedProducts-module__zj5_XW__disabled) {
  color: #2563eb;
  background: #fff;
}

.relatedProducts-module__zj5_XW__scrollButton.relatedProducts-module__zj5_XW__disabled {
  cursor: not-allowed;
  opacity: .5;
  background: #ccc;
  border-color: #ccc;
}

.relatedProducts-module__zj5_XW__productsContainer {
  scrollbar-width: none;
  -ms-overflow-style: none;
  overflow: auto hidden;
}

.relatedProducts-module__zj5_XW__productsContainer::-webkit-scrollbar {
  display: none;
}

.relatedProducts-module__zj5_XW__productsGrid {
  gap: 1.5rem;
  padding-bottom: 1rem;
  display: flex;
}

.relatedProducts-module__zj5_XW__productCard {
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  flex: 0 0 300px;
  transition: all .3s;
  overflow: hidden;
}

.relatedProducts-module__zj5_XW__productCard:hover {
  border-color: #2563eb;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #2563eb26;
}

.relatedProducts-module__zj5_XW__productLink {
  color: inherit;
  text-decoration: none;
  display: block;
}

.relatedProducts-module__zj5_XW__imageContainer {
  aspect-ratio: 1;
  background: #f8f8f8;
  position: relative;
  overflow: hidden;
}

.relatedProducts-module__zj5_XW__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.relatedProducts-module__zj5_XW__productCard:hover .relatedProducts-module__zj5_XW__productImage {
  transform: scale(1.05);
}

.relatedProducts-module__zj5_XW__outOfStockOverlay {
  color: #fff;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  position: absolute;
  inset: 0;
}

.relatedProducts-module__zj5_XW__productInfo {
  padding: 1.5rem;
}

.relatedProducts-module__zj5_XW__productName {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.3;
}

.relatedProducts-module__zj5_XW__productDetails {
  margin-bottom: 1rem;
}

.relatedProducts-module__zj5_XW__productCode {
  color: #666;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 0;
  width: fit-content;
  margin-bottom: .75rem;
  padding: .25rem .5rem;
  font-family: Courier New, monospace;
  font-size: .85rem;
  display: block;
}

.relatedProducts-module__zj5_XW__availability {
  font-size: .9rem;
}

.relatedProducts-module__zj5_XW__inStock {
  color: #28a745;
  font-weight: 600;
}

.relatedProducts-module__zj5_XW__outOfStock {
  color: #dc3545;
  font-weight: 600;
}

.relatedProducts-module__zj5_XW__priceContainer {
  border-top: 1px solid #eee;
  padding-top: 1rem;
}

.relatedProducts-module__zj5_XW__price {
  color: #1f2937;
  font-size: 1.3rem;
  font-weight: 700;
}

@media (width <= 1024px) {
  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 280px;
  }

  .relatedProducts-module__zj5_XW__sectionTitle {
    font-size: 1.75rem;
  }
}

@media (width <= 768px) {
  .relatedProducts-module__zj5_XW__relatedProducts {
    margin: 3rem 0;
  }

  .relatedProducts-module__zj5_XW__sectionHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .relatedProducts-module__zj5_XW__scrollControls {
    justify-content: center;
  }

  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 250px;
  }

  .relatedProducts-module__zj5_XW__productInfo {
    padding: 1rem;
  }

  .relatedProducts-module__zj5_XW__productName {
    font-size: 1.1rem;
  }

  .relatedProducts-module__zj5_XW__price {
    font-size: 1.2rem;
  }

  .relatedProducts-module__zj5_XW__sectionTitle {
    text-align: center;
    font-size: 1.5rem;
  }
}

@media (width <= 480px) {
  .relatedProducts-module__zj5_XW__productCard {
    flex: 0 0 220px;
  }

  .relatedProducts-module__zj5_XW__productInfo {
    padding: .75rem;
  }

  .relatedProducts-module__zj5_XW__productName {
    font-size: 1rem;
  }

  .relatedProducts-module__zj5_XW__price {
    font-size: 1.1rem;
  }

  .relatedProducts-module__zj5_XW__productCode {
    padding: .2rem .4rem;
    font-size: .8rem;
  }

  .relatedProducts-module__zj5_XW__scrollButton {
    width: 40px;
    height: 40px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .relatedProducts-module__zj5_XW__productCard {
    transition: none;
  }

  .relatedProducts-module__zj5_XW__productCard:hover {
    transform: none;
  }

  .relatedProducts-module__zj5_XW__productImage {
    transition: none;
  }

  .relatedProducts-module__zj5_XW__productCard:hover .relatedProducts-module__zj5_XW__productImage {
    transform: none;
  }
}


/* [project]/src/app/products/[id]/productPage.module.css [app-client] (css) */
.productPage-module__Rcy5XW__productPage {
  background: #fff;
  min-height: 100vh;
  padding-top: 6rem;
}

.productPage-module__Rcy5XW__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.productPage-module__Rcy5XW__loadingContainer {
  color: #1e40af;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  display: flex;
}

.productPage-module__Rcy5XW__loadingSpinner {
  border: 3px solid #f3f3f3;
  border-top-color: #1e40af;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite productPage-module__Rcy5XW__spin;
}

@keyframes productPage-module__Rcy5XW__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.productPage-module__Rcy5XW__errorContainer {
  text-align: center;
  color: #1e40af;
  padding: 4rem 2rem;
}

.productPage-module__Rcy5XW__errorContainer h1 {
  margin-bottom: 1rem;
  font-size: 2rem;
}

.productPage-module__Rcy5XW__productMain {
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  display: grid;
}

.productPage-module__Rcy5XW__imageSection {
  position: relative;
}

.productPage-module__Rcy5XW__detailsSection {
  padding: 2rem 0;
}

.productPage-module__Rcy5XW__productTitle {
  color: #1a1a1a;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.productPage-module__Rcy5XW__productCode {
  color: #000;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 0;
  margin-bottom: 2rem;
  padding: .4rem 1rem;
  font-family: Courier New, monospace;
  font-size: .9rem;
  display: inline-block;
}

.productPage-module__Rcy5XW__productInfo {
  margin-bottom: 2rem;
}

.productPage-module__Rcy5XW__infoRow {
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: .75rem;
  margin-bottom: .75rem;
  display: flex;
}

.productPage-module__Rcy5XW__infoLabel {
  color: #000;
  min-width: 100px;
  font-size: .95rem;
  font-weight: 600;
}

.productPage-module__Rcy5XW__infoValue {
  color: #333;
  font-size: .95rem;
  font-weight: 500;
}

.productPage-module__Rcy5XW__priceSection {
  background: #fff;
  border-radius: 0;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1.5rem;
  display: flex;
}

.productPage-module__Rcy5XW__priceRow {
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
  width: 100%;
  display: flex;
}

.productPage-module__Rcy5XW__price {
  color: #000;
  background: #f5f5f5;
  padding: 6px;
  font-size: 2rem;
  font-weight: 520;
}

.productPage-module__Rcy5XW__purchaseSection {
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
  display: flex;
}

.productPage-module__Rcy5XW__quantitySelector {
  align-items: center;
  gap: 2rem;
  display: flex;
}

.productPage-module__Rcy5XW__quantitySelector label {
  color: #000;
  font-weight: 600;
}

.productPage-module__Rcy5XW__quantityControls {
  border: 2px solidrgb(0, 0, 0);
  border-radius: 0;
  align-items: center;
  display: flex;
  overflow: hidden;
}

.productPage-module__Rcy5XW__quantityBtn {
  color: #000;
  cursor: pointer;
  background: #f5f5f5;
  border: #000;
  padding: .75rem 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  transition: background-color .2s;
}

.productPage-module__Rcy5XW__quantityBtn:hover:not(:disabled) {
  background: #1e40af;
}

.productPage-module__Rcy5XW__quantityBtn:disabled {
  cursor: not-allowed;
  background: #ccc;
}

.productPage-module__Rcy5XW__quantityInput {
  text-align: center;
  color: #000;
  background: #fff;
  border: none;
  outline: none;
  width: 80px;
  padding: .75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.productPage-module__Rcy5XW__addToCartRow {
  grid-template-columns: 150px 1fr;
  align-items: center;
  width: 100%;
  display: grid;
}

.productPage-module__Rcy5XW__addToCartLabel {
  width: 400%;
}

.productPage-module__Rcy5XW__addToCartWrapper {
  justify-content: center;
  width: 100%;
  display: flex;
}

.productPage-module__Rcy5XW__addToCartBtn {
  color: #000;
  border: 2px solidrgb(0, 0, 0);
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: .5px;
  background: #f5f5f5;
  border-radius: 0;
  width: 100%;
  max-width: 300px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.productPage-module__Rcy5XW__addToCartBtn:hover:not(:disabled) {
  color: #000;
  background: #5b5858;
}

.productPage-module__Rcy5XW__addToCartBtn:disabled {
  cursor: not-allowed;
  background: #ccc;
  border-color: #ccc;
}

@media (width <= 1024px) {
  .productPage-module__Rcy5XW__productMain {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .productPage-module__Rcy5XW__container {
    padding: 1rem;
  }

  .productPage-module__Rcy5XW__productTitle {
    font-size: 2rem;
  }

  .productPage-module__Rcy5XW__rightSection {
    gap: 2rem;
  }

  .productPage-module__Rcy5XW__specRow {
    border-bottom: 1px solid #ccc;
    justify-content: space-between;
    padding: 12px 0;
    display: flex;
  }

  .productPage-module__Rcy5XW__label {
    flex: 1;
    font-weight: 700;
  }

  .productPage-module__Rcy5XW__value {
    text-align: right;
    color: #222;
    flex: 1;
  }
}

@media (width <= 768px) {
  .productPage-module__Rcy5XW__infoRow {
    grid-template-columns: 1fr;
    gap: .25rem;
  }

  .productPage-module__Rcy5XW__infoLabel {
    font-weight: 700;
  }

  .productPage-module__Rcy5XW__purchaseSection {
    background: #fff;
    border-top: 2px solid #1e40af;
    margin: 2rem -1rem 0;
    padding: 1rem;
    position: sticky;
    bottom: 0;
  }

  .productPage-module__Rcy5XW__quantitySelector {
    flex-direction: column;
    align-items: stretch;
    gap: .5rem;
  }

  .productPage-module__Rcy5XW__quantityControls {
    justify-content: center;
  }

  .productPage-module__Rcy5XW__rightSection {
    flex-direction: column;
    gap: 2rem;
    display: flex;
  }

  .productPage-module__Rcy5XW__keySpecsTable {
    color: #111;
    border-top: 1px solid #ccc;
    width: 100%;
    max-width: 600px;
    margin-top: 20px;
    font-family: Arial, sans-serif;
    font-size: 14px;
  }

  .productPage-module__Rcy5XW__infoLabel {
    min-width: 80px;
    font-size: .9rem;
  }

  .productPage-module__Rcy5XW__infoValue {
    font-size: .9rem;
  }

  .productPage-module__Rcy5XW__productTitle, .productPage-module__Rcy5XW__price {
    font-size: 1.5rem;
  }

  .productPage-module__Rcy5XW__productCode {
    padding: .5rem;
    font-size: .8rem;
  }

  .productPage-module__Rcy5XW__specRow {
    flex-wrap: wrap;
    align-items: flex-start;
    gap: .5rem;
    flex-direction: row !important;
  }

  .productPage-module__Rcy5XW__label {
    flex: none;
    font-size: .85rem;
    font-weight: 600;
  }

  .productPage-module__Rcy5XW__value {
    text-align: left;
    word-break: break-word;
    flex: auto;
    font-size: .85rem;
    margin-top: 0 !important;
  }
}


/*# sourceMappingURL=src_5c32e429._.css.map*/