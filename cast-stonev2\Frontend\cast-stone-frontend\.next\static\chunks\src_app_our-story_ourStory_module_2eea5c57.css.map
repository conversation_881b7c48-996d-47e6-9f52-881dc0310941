{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/our-story/ourStory.module.css"], "sourcesContent": ["/* Our Story Page Styles - Blue and White Theme */\r\n.OurStoryroot {\r\n  --cast-stone-blue: #2563eb;\r\n  --cast-stone-light-blue: #3b82f6;\r\n  --cast-stone-blue-50: #eff6ff;\r\n  --cast-stone-white: #ffffff;\r\n  --cast-stone-dark-text: #1f2937;\r\n  --cast-stone-gray-text: #4b5563;\r\n  --cast-stone-shadow: rgba(37, 99, 235, 0.1);\r\n  --cast-stone-shadow-hover: rgba(37, 99, 235, 0.15);\r\n  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n/* Page Container */\r\n.storyPage {\r\n  min-height: 100vh;\r\n  background-color: var(--cast-stone-white);\r\n}\r\n\r\n/* Hero Section */\r\n.heroSection {\r\n  background: linear-gradient(135deg, var(--cast-stone-blue) 0%, var(--cast-stone-light-blue) 50%, var(--cast-stone-blue) 100%);\r\n  padding: 8rem 0 6rem;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.heroSection::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.02)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\r\n  opacity: 0.3;\r\n}\r\n\r\n.heroContainer {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.heroTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 4rem;\r\n  font-weight: 700;\r\n  color: var(--cast-stone-white);\r\n  margin-bottom: 1.5rem;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\r\n  letter-spacing: -0.02em;\r\n  animation: fadeInUp 1s ease-out forwards;\r\n}\r\n\r\n.heroSubtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 400;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  max-width: 700px;\r\n  margin: 0 auto;\r\n  line-height: 1.6;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n  animation: fadeInUp 1s ease-out 0.3s forwards;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n/* Main Content */\r\n.mainContent {\r\n  padding: 6rem 0;\r\n  background-color: var(--cast-stone-white);\r\n}\r\n\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Section Titles */\r\n.sectionTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: var(--cast-stone-dark-text);\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  letter-spacing: -0.01em;\r\n  position: relative;\r\n}\r\n\r\n.sectionTitle::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: -1rem;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  width: 80px;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  border-radius: 2px;\r\n}\r\n\r\n/* Introduction Section */\r\n.introSection {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  margin-bottom: 6rem;\r\n  padding: 4rem 0;\r\n}\r\n\r\n.introContent {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.introText {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.1rem;\r\n  line-height: 1.8;\r\n  color: var(--cast-stone-gray-text);\r\n  text-align: justify;\r\n}\r\n\r\n.introImage {\r\n  position: relative;\r\n}\r\n\r\n.imagePlaceholder {\r\n  width: 100%;\r\n  height: 400px;\r\n  background: linear-gradient(135deg, var(--cast-stone-blue-50), #f8fafc);\r\n  border-radius: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2px solid rgba(37, 99, 235, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.imagePlaceholder::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"texture\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><rect width=\"20\" height=\"20\" fill=\"rgba(37,99,235,0.02)\"/><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(37,99,235,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23texture)\"/></svg>');\r\n}\r\n\r\n.imageText {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.2rem;\r\n  color: var(--cast-stone-dark-text);\r\n  font-weight: 600;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* Heritage Section */\r\n.heritageSection {\r\n  margin-bottom: 6rem;\r\n  padding: 4rem 0;\r\n  background: var(--cast-stone-blue-50);\r\n  border-radius: 24px;\r\n}\r\n\r\n.heritageGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n  margin-top: 3rem;\r\n}\r\n\r\n.heritageCard {\r\n  background: var(--cast-stone-white);\r\n  padding: 2.5rem;\r\n  border-radius: 16px;\r\n  text-align: center;\r\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\r\n  transition: var(--transition-smooth);\r\n  border: 1px solid rgba(37, 99, 235, 0.08);\r\n}\r\n\r\n.heritageCard:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 16px 40px var(--cast-stone-shadow-hover);\r\n}\r\n\r\n.heritageIcon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  border-radius: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto 1.5rem;\r\n  color: var(--cast-stone-white);\r\n}\r\n\r\n.heritageTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: var(--cast-stone-dark-text);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.heritageText {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n  color: var(--cast-stone-gray-text);\r\n}\r\n\r\n/* Timeline Section */\r\n.timelineSection {\r\n  margin-bottom: 6rem;\r\n  padding: 4rem 0;\r\n}\r\n\r\n.timeline {\r\n  position: relative;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.timeline::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 50%;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 3px;\r\n  background: linear-gradient(180deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  transform: translateX(-50%);\r\n}\r\n\r\n.timelineItem {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 3rem;\r\n  position: relative;\r\n}\r\n\r\n.timelineItem:nth-child(odd) {\r\n  flex-direction: row;\r\n}\r\n\r\n.timelineItem:nth-child(even) {\r\n  flex-direction: row-reverse;\r\n}\r\n\r\n.timelineYear {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--cast-stone-white);\r\n  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  padding: 1rem 1.5rem;\r\n  border-radius: 50px;\r\n  min-width: 100px;\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 2;\r\n  box-shadow: 0 4px 12px var(--cast-stone-shadow);\r\n}\r\n\r\n.timelineContent {\r\n  flex: 1;\r\n  background: var(--cast-stone-white);\r\n  padding: 2rem;\r\n  border-radius: 16px;\r\n  margin: 0 2rem;\r\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\r\n  border: 1px solid rgba(37, 99, 235, 0.08);\r\n}\r\n\r\n.timelineTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: var(--cast-stone-dark-text);\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.timelineText {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n  color: var(--cast-stone-gray-text);\r\n}\r\n\r\n/* Values Section */\r\n.valuesSection {\r\n  margin-bottom: 6rem;\r\n  padding: 4rem 0;\r\n  background: var(--cast-stone-blue-50);\r\n  border-radius: 24px;\r\n}\r\n\r\n.valuesGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 2rem;\r\n  margin-top: 3rem;\r\n}\r\n\r\n.valueCard {\r\n  background: var(--cast-stone-white);\r\n  padding: 2rem;\r\n  border-radius: 16px;\r\n  box-shadow: 0 8px 24px var(--cast-stone-shadow);\r\n  transition: var(--transition-smooth);\r\n  border: 1px solid rgba(37, 99, 235, 0.08);\r\n}\r\n\r\n.valueCard:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 12px 32px var(--cast-stone-shadow-hover);\r\n}\r\n\r\n.valueTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  color: var(--cast-stone-dark-text);\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.valueText {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n  color: var(--cast-stone-gray-text);\r\n}\r\n\r\n/* Call to Action Section */\r\n.ctaSection {\r\n  text-align: center;\r\n  padding: 4rem 0;\r\n  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));\r\n  border-radius: 24px;\r\n  color: var(--cast-stone-white);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.ctaSection::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"cta-grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.03)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23cta-grain)\"/></svg>');\r\n  opacity: 0.3;\r\n}\r\n\r\n.ctaContent {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.ctaTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.01em;\r\n}\r\n\r\n.ctaText {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.2rem;\r\n  margin-bottom: 2.5rem;\r\n  opacity: 0.9;\r\n  max-width: 600px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.ctaButtons {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.primaryButton,\r\n.secondaryButton {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  padding: 1.25rem 2.5rem;\r\n  border-radius: 50px;\r\n  text-decoration: none;\r\n  transition: var(--transition-smooth);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n  min-width: 200px;\r\n  text-align: center;\r\n}\r\n\r\n.primaryButton {\r\n  background: var(--cast-stone-white);\r\n  color: var(--cast-stone-blue);\r\n  border: 2px solid var(--cast-stone-white);\r\n}\r\n\r\n.primaryButton:hover {\r\n  background: transparent;\r\n  color: var(--cast-stone-white);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.secondaryButton {\r\n  background: transparent;\r\n  color: var(--cast-stone-white);\r\n  border: 2px solid var(--cast-stone-white);\r\n}\r\n\r\n.secondaryButton:hover {\r\n  background: var(--cast-stone-white);\r\n  color: var(--cast-stone-blue);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .introSection {\r\n    grid-template-columns: 1fr;\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .heroTitle {\r\n    font-size: 3rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .heroSection {\r\n    padding: 6rem 0 4rem;\r\n  }\r\n  \r\n  .heroTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .heroSubtitle {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .mainContent {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .timeline::before {\r\n    left: 2rem;\r\n  }\r\n  \r\n  .timelineItem {\r\n    flex-direction: row !important;\r\n    padding-left: 4rem;\r\n  }\r\n  \r\n  .timelineYear {\r\n    position: absolute;\r\n    left: 0;\r\n    min-width: 80px;\r\n    font-size: 1.2rem;\r\n    padding: 0.75rem 1rem;\r\n  }\r\n  \r\n  .timelineContent {\r\n    margin-left: 2rem;\r\n    margin-right: 0;\r\n  }\r\n  \r\n  .ctaButtons {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .heroTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .heroSubtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .ctaTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .heritageCard,\r\n  .valueCard,\r\n  .timelineContent {\r\n    padding: 1.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAWA;;;;;;;;;AASA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;AAQA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;;;;;;;;;;;;AAYA;EACE;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;;EAQA;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}