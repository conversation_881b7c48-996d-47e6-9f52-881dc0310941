/* [project]/src/components/products/PatinaSelector/patinaSelector.module.css [app-client] (css) */
.patinaSelector-module__M14LiG__patinaSelector {
  background: #fafafa;
  border: 1px solid #ddd;
  margin: 1rem 0;
  padding: 1rem;
}

.patinaSelector-module__M14LiG__selectorHeader {
  border-bottom: 1px solid #4a3728;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: .75rem;
  display: flex;
}

.patinaSelector-module__M14LiG__selectorTitle {
  color: #4a3728;
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
}

.patinaSelector-module__M14LiG__selectedPatina {
  color: #4a3728;
  background: #fff;
  border: 1px solid #4a3728;
  padding: .4rem .75rem;
  font-size: .85rem;
  font-weight: 600;
}

.patinaSelector-module__M14LiG__patinaGrid {
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: .75rem;
  margin-bottom: 1rem;
  display: grid;
}

.patinaSelector-module__M14LiG__patinaOption {
  cursor: pointer;
  text-align: center;
  background: #fff;
  border: 1px solid #ddd;
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  min-height: 80px;
  padding: .75rem;
  transition: all .3s;
  display: flex;
}

.patinaSelector-module__M14LiG__patinaOption:hover {
  border-color: #4a3728;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px #4a37281a;
}

.patinaSelector-module__M14LiG__patinaOption.patinaSelector-module__M14LiG__selected {
  background: #f8f6f3;
  border-width: 2px;
  border-color: #4a3728;
  box-shadow: 0 2px 8px #4a372826;
}

.patinaSelector-module__M14LiG__patinaColor {
  border: 1px solid #333;
  width: 30px;
  height: 30px;
  box-shadow: inset 0 1px 2px #0000001a;
}

.patinaSelector-module__M14LiG__patinaName {
  color: #4a3728;
  font-size: .75rem;
  font-weight: 600;
  line-height: 1.1;
}

.patinaSelector-module__M14LiG__patinaOption.patinaSelector-module__M14LiG__selected .patinaSelector-module__M14LiG__patinaName {
  font-weight: 700;
}

.patinaSelector-module__M14LiG__patinaNote {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  margin-top: .75rem;
  padding: .75rem;
}

.patinaSelector-module__M14LiG__patinaNote p {
  color: #856404;
  margin: 0;
  font-size: .8rem;
  line-height: 1.3;
}

.patinaSelector-module__M14LiG__patinaNote strong {
  color: #533f03;
}

@media (width <= 768px) {
  .patinaSelector-module__M14LiG__patinaSelector {
    padding: .75rem;
  }

  .patinaSelector-module__M14LiG__selectorHeader {
    text-align: center;
    flex-direction: column;
    gap: .75rem;
  }

  .patinaSelector-module__M14LiG__patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaOption {
    gap: .4rem;
    min-height: 70px;
    padding: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaColor {
    width: 25px;
    height: 25px;
  }

  .patinaSelector-module__M14LiG__patinaName {
    font-size: .7rem;
  }

  .patinaSelector-module__M14LiG__selectorTitle {
    font-size: .95rem;
  }

  .patinaSelector-module__M14LiG__selectedPatina {
    padding: .35rem .6rem;
    font-size: .8rem;
  }
}

@media (width <= 480px) {
  .patinaSelector-module__M14LiG__patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  }

  .patinaSelector-module__M14LiG__patinaOption {
    min-height: 60px;
  }

  .patinaSelector-module__M14LiG__patinaNote {
    padding: .5rem;
  }

  .patinaSelector-module__M14LiG__patinaNote p {
    font-size: .75rem;
  }
}

@media (prefers-reduced-motion: reduce) {
  .patinaSelector-module__M14LiG__patinaOption {
    transition: none;
  }

  .patinaSelector-module__M14LiG__patinaOption:hover {
    transform: none;
  }
}

/*# sourceMappingURL=src_components_products_PatinaSelector_patinaSelector_module_css_f9ee138c._.single.css.map*/