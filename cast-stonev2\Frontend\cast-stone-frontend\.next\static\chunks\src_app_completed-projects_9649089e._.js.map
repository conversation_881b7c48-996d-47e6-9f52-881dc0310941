{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/completed-projects/completedProjects.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"comingSoon\": \"completedProjects-module__5ybc9G__comingSoon\",\n  \"container\": \"completedProjects-module__5ybc9G__container\",\n  \"content\": \"completedProjects-module__5ybc9G__content\",\n  \"feature\": \"completedProjects-module__5ybc9G__feature\",\n  \"features\": \"completedProjects-module__5ybc9G__features\",\n  \"header\": \"completedProjects-module__5ybc9G__header\",\n  \"icon\": \"completedProjects-module__5ybc9G__icon\",\n  \"subtitle\": \"completedProjects-module__5ybc9G__subtitle\",\n  \"title\": \"completedProjects-module__5ybc9G__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/completed-projects/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styles from './completedProjects.module.css';\r\n\r\nexport default function CompletedProjectsPage() {\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.header}>\r\n        <h1 className={styles.title}>Completed Projects</h1>\r\n        <p className={styles.subtitle}>\r\n          Explore our portfolio of stunning cast stone installations and transformations\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.content}>\r\n        <div className={styles.comingSoon}>\r\n          <div className={styles.icon}>\r\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\r\n            </svg>\r\n          </div>\r\n          <h2>Coming Soon</h2>\r\n          <p>\r\n            We&apos;re currently curating our most impressive completed projects to showcase here.\r\n            Check back soon to see beautiful installations, before & after transformations,\r\n            and customer success stories.\r\n          </p>\r\n          <div className={styles.features}>\r\n            <div className={styles.feature}>\r\n              <h4>Project Galleries</h4>\r\n              <p>High-quality photos of completed installations</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Case Studies</h4>\r\n              <p>Detailed project breakdowns and customer testimonials</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Before & After</h4>\r\n              <p>Transformation stories showcasing our craftsmanship</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,6LAAC;wBAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;0BAC5B,cAAA,6LAAC;oBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,6LAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,IAAI;sCACzB,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAKH,6LAAC;4BAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;;8CAC7B,6LAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;8CAEL,6LAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;8CAEL,6LAAC;oCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;KAzCwB", "debugId": null}}]}