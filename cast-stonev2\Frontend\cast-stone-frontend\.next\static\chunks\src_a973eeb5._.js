(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "activeThumbnail": "productImageGallery-module__AcqG6a__activeThumbnail",
  "closeZoomButton": "productImageGallery-module__AcqG6a__closeZoomButton",
  "imageCounter": "productImageGallery-module__AcqG6a__imageCounter",
  "imageGallery": "productImageGallery-module__AcqG6a__imageGallery",
  "mainImage": "productImageGallery-module__AcqG6a__mainImage",
  "mainImageContainer": "productImageGallery-module__AcqG6a__mainImageContainer",
  "navButton": "productImageGallery-module__AcqG6a__navButton",
  "nextButton": "productImageGallery-module__AcqG6a__nextButton",
  "prevButton": "productImageGallery-module__AcqG6a__prevButton",
  "thumbnail": "productImageGallery-module__AcqG6a__thumbnail",
  "thumbnailContainer": "productImageGallery-module__AcqG6a__thumbnailContainer",
  "thumbnailGrid": "productImageGallery-module__AcqG6a__thumbnailGrid",
  "thumbnailImage": "productImageGallery-module__AcqG6a__thumbnailImage",
  "zoomIndicator": "productImageGallery-module__AcqG6a__zoomIndicator",
  "zoomOverlay": "productImageGallery-module__AcqG6a__zoomOverlay",
  "zoomed": "productImageGallery-module__AcqG6a__zoomed",
  "zoomedImage": "productImageGallery-module__AcqG6a__zoomedImage",
  "zoomedImageContainer": "productImageGallery-module__AcqG6a__zoomedImageContainer",
});
}}),
"[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @next/next/no-img-element */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const ProductImageGallery = ({ images, productName })=>{
    _s();
    const [selectedImageIndex, setSelectedImageIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isZoomed, setIsZoomed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Use placeholder if no images provided
    const galleryImages = images.length > 0 ? images : [
        '/images/placeholder-product.jpg'
    ];
    const currentImage = galleryImages[selectedImageIndex];
    const handleThumbnailClick = (index)=>{
        setSelectedImageIndex(index);
        setIsZoomed(false);
    };
    const handleMainImageClick = ()=>{
        setIsZoomed(!isZoomed);
    };
    const handlePrevImage = ()=>{
        setSelectedImageIndex((prev)=>prev === 0 ? galleryImages.length - 1 : prev - 1);
        setIsZoomed(false);
    };
    const handleNextImage = ()=>{
        setSelectedImageIndex((prev)=>prev === galleryImages.length - 1 ? 0 : prev + 1);
        setIsZoomed(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].imageGallery,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mainImageContainer,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                        src: currentImage,
                        alt: `${productName} - Image ${selectedImageIndex + 1}`,
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mainImage} ${isZoomed ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].zoomed : ''}`,
                        onClick: handleMainImageClick
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                        lineNumber: 53,
                        columnNumber: 9
                    }, this),
                    galleryImages.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navButton} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].prevButton}`,
                                onClick: handlePrevImage,
                                "aria-label": "Previous image",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M15 18L9 12L15 6",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 69,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                    lineNumber: 68,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                lineNumber: 63,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].navButton} ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].nextButton}`,
                                onClick: handleNextImage,
                                "aria-label": "Next image",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M9 18L15 12L9 6",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 85,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                    lineNumber: 84,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                lineNumber: 79,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true),
                    galleryImages.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].imageCounter,
                        children: [
                            selectedImageIndex + 1,
                            " / ",
                            galleryImages.length
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].zoomIndicator,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                width: "20",
                                height: "20",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                        cx: "11",
                                        cy: "11",
                                        r: "8",
                                        stroke: "currentColor",
                                        strokeWidth: "2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 107,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M21 21L16.65 16.65",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 108,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                                        x1: "11",
                                        y1: "8",
                                        x2: "11",
                                        y2: "14",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 109,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                                        x1: "8",
                                        y1: "11",
                                        x2: "14",
                                        y2: "11",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 110,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            "Click to zoom"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                lineNumber: 52,
                columnNumber: 7
            }, this),
            galleryImages.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].thumbnailContainer,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].thumbnailGrid,
                    children: galleryImages.map((image, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].thumbnail} ${index === selectedImageIndex ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].activeThumbnail : ''}`,
                            onClick: ()=>handleThumbnailClick(index),
                            "aria-label": `View image ${index + 1}`,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                src: image,
                                alt: `${productName} thumbnail ${index + 1}`,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].thumbnailImage
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                lineNumber: 129,
                                columnNumber: 17
                            }, this)
                        }, index, false, {
                            fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                            lineNumber: 121,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                    lineNumber: 119,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                lineNumber: 118,
                columnNumber: 9
            }, this),
            isZoomed && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].zoomOverlay,
                onClick: ()=>setIsZoomed(false),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].zoomedImageContainer,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                            src: currentImage,
                            alt: `${productName} - Zoomed view`,
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].zoomedImage
                        }, void 0, false, {
                            fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$productImageGallery$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].closeZoomButton,
                            onClick: ()=>setIsZoomed(false),
                            "aria-label": "Close zoom view",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                width: "24",
                                height: "24",
                                viewBox: "0 0 24 24",
                                fill: "none",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                                        x1: "18",
                                        y1: "6",
                                        x2: "6",
                                        y2: "18",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 155,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("line", {
                                        x1: "6",
                                        y1: "6",
                                        x2: "18",
                                        y2: "18",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                                lineNumber: 154,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                            lineNumber: 149,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                    lineNumber: 143,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
                lineNumber: 142,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, this);
};
_s(ProductImageGallery, "aLIxOcb/1RV5q6veAlESuGXPPCs=");
_c = ProductImageGallery;
const __TURBOPACK__default__export__ = ProductImageGallery;
var _c;
__turbopack_context__.k.register(_c, "ProductImageGallery");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "productSpecifications-module__J4NrIa__active",
  "careContent": "productSpecifications-module__J4NrIa__careContent",
  "careSection": "productSpecifications-module__J4NrIa__careSection",
  "description": "productSpecifications-module__J4NrIa__description",
  "detailsContent": "productSpecifications-module__J4NrIa__detailsContent",
  "downloadLink": "productSpecifications-module__J4NrIa__downloadLink",
  "downloadLinks": "productSpecifications-module__J4NrIa__downloadLinks",
  "downloadSection": "productSpecifications-module__J4NrIa__downloadSection",
  "featureList": "productSpecifications-module__J4NrIa__featureList",
  "inStock": "productSpecifications-module__J4NrIa__inStock",
  "outOfStock": "productSpecifications-module__J4NrIa__outOfStock",
  "section": "productSpecifications-module__J4NrIa__section",
  "sectionContent": "productSpecifications-module__J4NrIa__sectionContent",
  "sectionHeader": "productSpecifications-module__J4NrIa__sectionHeader",
  "shareButton": "productSpecifications-module__J4NrIa__shareButton",
  "shareButtons": "productSpecifications-module__J4NrIa__shareButtons",
  "shareLabel": "productSpecifications-module__J4NrIa__shareLabel",
  "shareSection": "productSpecifications-module__J4NrIa__shareSection",
  "specGrid": "productSpecifications-module__J4NrIa__specGrid",
  "specLabel": "productSpecifications-module__J4NrIa__specLabel",
  "specRow": "productSpecifications-module__J4NrIa__specRow",
  "specValue": "productSpecifications-module__J4NrIa__specValue",
  "specificationsContainer": "productSpecifications-module__J4NrIa__specificationsContainer",
  "tag": "productSpecifications-module__J4NrIa__tag",
  "tagContainer": "productSpecifications-module__J4NrIa__tagContainer",
  "toggleIcon": "productSpecifications-module__J4NrIa__toggleIcon",
});
}}),
"[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const ProductSpecifications = ({ product })=>{
    _s();
    const [activeSection, setActiveSection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('specifications');
    const toggleSection = (section)=>{
        setActiveSection(activeSection === section ? null : section);
    };
    const hasSpecifications = product.productSpecifications && Object.values(product.productSpecifications).some((value)=>typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined);
    const hasDetails = product.productDetails && Object.values(product.productDetails).some((value)=>typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined);
    const hasDownloadableContent = product.downloadableContent && Object.values(product.downloadableContent).some((value)=>typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specificationsContainer,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].section,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionHeader} ${activeSection === 'specifications' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ''}`,
                        onClick: ()=>toggleSection('specifications'),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Product Specifications"
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 44,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].toggleIcon,
                                children: activeSection === 'specifications' ? '−' : '+'
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 45,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, this),
                    activeSection === 'specifications' && hasSpecifications && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionContent,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specGrid,
                            children: [
                                product.productSpecifications?.material && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Material:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 56,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: product.productSpecifications.material
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 57,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 55,
                                    columnNumber: 17
                                }, this),
                                product.productSpecifications?.dimensions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Dimensions:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 63,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: product.productSpecifications.dimensions
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 64,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 62,
                                    columnNumber: 17
                                }, this),
                                product.productSpecifications?.totalWeight && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Total Weight:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 70,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: product.productSpecifications.totalWeight
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 71,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 69,
                                    columnNumber: 17
                                }, this),
                                product.productSpecifications?.weightWithWater && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Weight With Water:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 77,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: product.productSpecifications.weightWithWater
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 78,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 76,
                                    columnNumber: 17
                                }, this),
                                product.productSpecifications?.waterVolume && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Water Volume:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 105,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: product.productSpecifications.waterVolume
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 106,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 104,
                                    columnNumber: 17
                                }, this),
                                product.tags && product.tags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                            children: "Tags:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 135,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].tagContainer,
                                                children: product.tags.map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].tag,
                                                        children: tag
                                                    }, index, false, {
                                                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                        lineNumber: 139,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                lineNumber: 137,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 136,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 134,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                            lineNumber: 53,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 52,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].section,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionHeader} ${activeSection === 'details' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ''}`,
                        onClick: ()=>toggleSection('details'),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Product Details"
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 160,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].toggleIcon,
                                children: activeSection === 'details' ? '−' : '+'
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 161,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this),
                    activeSection === 'details' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionContent,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].detailsContent,
                            children: [
                                product.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].description,
                                    children: product.description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 170,
                                    columnNumber: 17
                                }, this),
                                hasDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specGrid,
                                    children: [
                                        product.productDetails?.upc && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "UPC:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 177,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.upc
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 176,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.indoorUseOnly && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Indoor Use Only:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 184,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.indoorUseOnly
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 183,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.assemblyRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Assembly Required:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.assemblyRequired
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 192,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 190,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.easeOfAssembly && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Ease of Assembly:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 198,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.easeOfAssembly
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 197,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.assistanceRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Assistance Required:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.assistanceRequired
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 206,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 204,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.splashLevel && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Splash Level:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 212,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.splashLevel
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 213,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 211,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.soundLevel && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Sound Level:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 219,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.soundLevel
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 218,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.soundType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Sound Type:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 226,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.soundType
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 227,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 225,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.replacementPumpKit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Replacement Pump Kit:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 233,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.replacementPumpKit
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 232,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.electricalCordLength && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Electrical Cord Length:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.electricalCordLength
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 241,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 239,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.pumpSize && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Pump Size:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 247,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.pumpSize
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 248,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 246,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.shipMethod && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Ship Method:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 254,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.shipMethod
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 255,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 253,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.drainage_Info && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Drainage Info:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 260,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.drainage_Info
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 259,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.inside_Top && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Inside Top:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.inside_Top
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 268,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 266,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.inside_Bottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Inside Bottom:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 274,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.inside_Bottom
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 275,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 273,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.inside_Height && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Inside Height:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 281,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.inside_Height
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 282,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 280,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.factory_Code && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Factory Code:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 288,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.factory_Code
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 289,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 287,
                                            columnNumber: 21
                                        }, this),
                                        product.productDetails?.catalogPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specLabel,
                                                    children: "Catalog Page:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 294,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specValue,
                                                    children: product.productDetails.catalogPage
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                            lineNumber: 293,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 174,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                            lineNumber: 168,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 167,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                lineNumber: 153,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].section,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionHeader} ${activeSection === 'care' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ''}`,
                        onClick: ()=>toggleSection('care'),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: "Product Care and Downloadable Content"
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 313,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].toggleIcon,
                                children: activeSection === 'care' ? '−' : '+'
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 314,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 307,
                        columnNumber: 9
                    }, this),
                    activeSection === 'care' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionContent,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].careContent,
                            children: hasDownloadableContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].downloadSection,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        children: "Downloadable Content:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                        lineNumber: 324,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].downloadLinks,
                                        children: [
                                            product.downloadableContent?.care && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                href: product.downloadableContent.care,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].downloadLink,
                                                children: "📄 Care Instructions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                lineNumber: 327,
                                                columnNumber: 23
                                            }, this),
                                            product.downloadableContent?.productInstructions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                href: product.downloadableContent.productInstructions,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].downloadLink,
                                                children: "📋 Product Instructions"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                lineNumber: 338,
                                                columnNumber: 23
                                            }, this),
                                            product.downloadableContent?.cad && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                href: product.downloadableContent.cad,
                                                target: "_blank",
                                                rel: "noopener noreferrer",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].downloadLink,
                                                children: "📐 CAD Files"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                                lineNumber: 349,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                        lineNumber: 325,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 323,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                            lineNumber: 321,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 320,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                lineNumber: 306,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].shareSection,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].shareLabel,
                        children: "Share"
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 368,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].shareButtons,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].shareButton,
                                "aria-label": "Share on Facebook",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "20",
                                    height: "20",
                                    viewBox: "0 0 24 24",
                                    fill: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                        lineNumber: 372,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 371,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 370,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$productSpecifications$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].shareButton,
                                "aria-label": "Share on Pinterest",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "20",
                                    height: "20",
                                    viewBox: "0 0 24 24",
                                    fill: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.**************.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-12.014C24.007 5.36 18.641.001 12.017.001z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                        lineNumber: 378,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                    lineNumber: 377,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                                lineNumber: 376,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                        lineNumber: 369,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
                lineNumber: 367,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx",
        lineNumber: 35,
        columnNumber: 5
    }, this);
};
_s(ProductSpecifications, "DjAFizMs0tW/iHR1A4dOIAeT9/s=");
_c = ProductSpecifications;
const __TURBOPACK__default__export__ = ProductSpecifications;
var _c;
__turbopack_context__.k.register(_c, "ProductSpecifications");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/products/PatinaSelector/patinaSelector.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "patinaColor": "patinaSelector-module__M14LiG__patinaColor",
  "patinaGrid": "patinaSelector-module__M14LiG__patinaGrid",
  "patinaName": "patinaSelector-module__M14LiG__patinaName",
  "patinaNote": "patinaSelector-module__M14LiG__patinaNote",
  "patinaOption": "patinaSelector-module__M14LiG__patinaOption",
  "patinaSelector": "patinaSelector-module__M14LiG__patinaSelector",
  "selected": "patinaSelector-module__M14LiG__selected",
  "selectedPatina": "patinaSelector-module__M14LiG__selectedPatina",
  "selectorHeader": "patinaSelector-module__M14LiG__selectorHeader",
  "selectorTitle": "patinaSelector-module__M14LiG__selectorTitle",
});
}}),
"[project]/src/components/products/PatinaSelector/PatinaSelector.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/products/PatinaSelector/patinaSelector.module.css [app-client] (css module)");
'use client';
;
;
const PatinaSelector = ({ selectedPatina, onPatinaChange })=>{
    // Patina options based on the reference design
    const patinaOptions = [
        {
            name: 'Alpine Stone',
            color: '#D4C4A8',
            description: 'Light cream stone finish'
        },
        {
            name: 'Aged Stone',
            color: '#B8A082',
            description: 'Weathered natural stone'
        },
        {
            name: 'Charcoal',
            color: '#5A5A5A',
            description: 'Dark charcoal finish'
        },
        {
            name: 'Limestone',
            color: '#E6DCC6',
            description: 'Classic limestone color'
        },
        {
            name: 'Sandstone',
            color: '#C9B299',
            description: 'Warm sandstone tone'
        },
        {
            name: 'Slate Gray',
            color: '#708090',
            description: 'Cool slate gray'
        },
        {
            name: 'Terra Cotta',
            color: '#B87333',
            description: 'Earthy terra cotta'
        },
        {
            name: 'Antique White',
            color: '#F5F5DC',
            description: 'Soft antique white'
        },
        {
            name: 'Weathered Bronze',
            color: '#8B7355',
            description: 'Bronze patina finish'
        },
        {
            name: 'Natural Stone',
            color: '#A0A0A0',
            description: 'Natural stone gray'
        },
        {
            name: 'Moss Green',
            color: '#8FBC8F',
            description: 'Subtle moss green'
        },
        {
            name: 'Rust',
            color: '#B7410E',
            description: 'Oxidized rust finish'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaSelector,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selectorHeader,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selectorTitle,
                        children: "Select Patina"
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                        lineNumber: 40,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selectedPatina,
                        children: selectedPatina
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaGrid,
                children: patinaOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaOption} ${selectedPatina === option.name ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].selected : ''}`,
                        onClick: ()=>onPatinaChange(option.name),
                        title: `${option.name} - ${option.description}`,
                        "aria-label": `Select ${option.name} patina`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaColor,
                                style: {
                                    backgroundColor: option.color
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaName,
                                children: option.name
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this)
                        ]
                    }, option.name, true, {
                        fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                lineNumber: 44,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$patinaSelector$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].patinaNote,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                            children: "Note:"
                        }, void 0, false, {
                            fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                            lineNumber: 66,
                            columnNumber: 11
                        }, this),
                        " Patina colors are representative. Actual finish may vary due to the handcrafted nature of cast stone. Contact us for physical samples."
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                    lineNumber: 65,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/products/PatinaSelector/PatinaSelector.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
};
_c = PatinaSelector;
const __TURBOPACK__default__export__ = PatinaSelector;
var _c;
__turbopack_context__.k.register(_c, "PatinaSelector");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "availability": "relatedProducts-module__zj5_XW__availability",
  "disabled": "relatedProducts-module__zj5_XW__disabled",
  "imageContainer": "relatedProducts-module__zj5_XW__imageContainer",
  "inStock": "relatedProducts-module__zj5_XW__inStock",
  "outOfStock": "relatedProducts-module__zj5_XW__outOfStock",
  "outOfStockOverlay": "relatedProducts-module__zj5_XW__outOfStockOverlay",
  "price": "relatedProducts-module__zj5_XW__price",
  "priceContainer": "relatedProducts-module__zj5_XW__priceContainer",
  "productCard": "relatedProducts-module__zj5_XW__productCard",
  "productCode": "relatedProducts-module__zj5_XW__productCode",
  "productDetails": "relatedProducts-module__zj5_XW__productDetails",
  "productImage": "relatedProducts-module__zj5_XW__productImage",
  "productInfo": "relatedProducts-module__zj5_XW__productInfo",
  "productLink": "relatedProducts-module__zj5_XW__productLink",
  "productName": "relatedProducts-module__zj5_XW__productName",
  "productsContainer": "relatedProducts-module__zj5_XW__productsContainer",
  "productsGrid": "relatedProducts-module__zj5_XW__productsGrid",
  "relatedProducts": "relatedProducts-module__zj5_XW__relatedProducts",
  "scrollButton": "relatedProducts-module__zj5_XW__scrollButton",
  "scrollControls": "relatedProducts-module__zj5_XW__scrollControls",
  "sectionHeader": "relatedProducts-module__zj5_XW__sectionHeader",
  "sectionTitle": "relatedProducts-module__zj5_XW__sectionTitle",
});
}}),
"[project]/src/components/products/RelatedProducts/RelatedProducts.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @next/next/no-img-element */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const RelatedProducts = ({ products })=>{
    _s();
    const [currentIndex, setCurrentIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [canScrollLeft, setCanScrollLeft] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [canScrollRight, setCanScrollRight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const scrollContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    const updateScrollButtons = ()=>{
        if (scrollContainerRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
            setCanScrollLeft(scrollLeft > 0);
            setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
        }
    };
    const scrollLeft = ()=>{
        if (scrollContainerRef.current) {
            const cardWidth = 320; // Card width + gap
            scrollContainerRef.current.scrollBy({
                left: -cardWidth,
                behavior: 'smooth'
            });
        }
    };
    const scrollRight = ()=>{
        if (scrollContainerRef.current) {
            const cardWidth = 320; // Card width + gap
            scrollContainerRef.current.scrollBy({
                left: cardWidth,
                behavior: 'smooth'
            });
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RelatedProducts.useEffect": ()=>{
            const scrollContainer = scrollContainerRef.current;
            if (scrollContainer) {
                updateScrollButtons();
                scrollContainer.addEventListener('scroll', updateScrollButtons);
                return ({
                    "RelatedProducts.useEffect": ()=>scrollContainer.removeEventListener('scroll', updateScrollButtons)
                })["RelatedProducts.useEffect"];
            }
        }
    }["RelatedProducts.useEffect"], [
        products
    ]);
    if (!products || products.length === 0) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].relatedProducts,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionHeader,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sectionTitle,
                        children: "You May Also Like"
                    }, void 0, false, {
                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    products.length > 3 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].scrollControls,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].scrollButton} ${!canScrollLeft ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].disabled : ''}`,
                                onClick: scrollLeft,
                                disabled: !canScrollLeft,
                                "aria-label": "Scroll left",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "20",
                                    height: "20",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M15 18L9 12L15 6",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                        lineNumber: 76,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                    lineNumber: 75,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                lineNumber: 69,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].scrollButton} ${!canScrollRight ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].disabled : ''}`,
                                onClick: scrollRight,
                                disabled: !canScrollRight,
                                "aria-label": "Scroll right",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "20",
                                    height: "20",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M9 18L15 12L9 6",
                                        stroke: "currentColor",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                        lineNumber: 93,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                    lineNumber: 92,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                lineNumber: 86,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productsContainer,
                ref: scrollContainerRef,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productsGrid,
                    children: products.map((product)=>{
                        const mainImage = product.images && product.images.length > 0 ? product.images[0] : '/images/placeholder-product.jpg';
                        const isInStock = product.stock > 0;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productCard,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: `/products/${product.id}`,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productLink,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].imageContainer,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                src: mainImage,
                                                alt: product.name,
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productImage
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                lineNumber: 122,
                                                columnNumber: 21
                                            }, this),
                                            !isInStock && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].outOfStockOverlay,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "Out of Stock"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                lineNumber: 128,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                        lineNumber: 121,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productInfo,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productName,
                                                children: product.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                lineNumber: 135,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productDetails,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productCode,
                                                        children: [
                                                            "P-",
                                                            product.id.toString().padStart(3, '0'),
                                                            "-AS"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                        lineNumber: 138,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].availability,
                                                        children: isInStock ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].inStock,
                                                            children: "Available in 14 Colors And 3 sizes"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                            lineNumber: 144,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].outOfStock,
                                                            children: "Currently Out of Stock"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                            lineNumber: 148,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                        lineNumber: 142,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                lineNumber: 137,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].priceContainer,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$relatedProducts$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].price,
                                                    children: formatPrice(product.price)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                    lineNumber: 156,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                                lineNumber: 155,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                        lineNumber: 134,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                                lineNumber: 120,
                                columnNumber: 17
                            }, this)
                        }, product.id, false, {
                            fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                            lineNumber: 119,
                            columnNumber: 15
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                    lineNumber: 110,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
                lineNumber: 106,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/products/RelatedProducts/RelatedProducts.tsx",
        lineNumber: 63,
        columnNumber: 5
    }, this);
};
_s(RelatedProducts, "7uqGMzrHYzgH8lmJ8sJPZHxGy2U=");
_c = RelatedProducts;
const __TURBOPACK__default__export__ = RelatedProducts;
var _c;
__turbopack_context__.k.register(_c, "RelatedProducts");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/products/[id]/productPage.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "addToCartBtn": "productPage-module__Rcy5XW__addToCartBtn",
  "addToCartLabel": "productPage-module__Rcy5XW__addToCartLabel",
  "addToCartRow": "productPage-module__Rcy5XW__addToCartRow",
  "addToCartWrapper": "productPage-module__Rcy5XW__addToCartWrapper",
  "container": "productPage-module__Rcy5XW__container",
  "detailsSection": "productPage-module__Rcy5XW__detailsSection",
  "errorContainer": "productPage-module__Rcy5XW__errorContainer",
  "imageSection": "productPage-module__Rcy5XW__imageSection",
  "infoLabel": "productPage-module__Rcy5XW__infoLabel",
  "infoRow": "productPage-module__Rcy5XW__infoRow",
  "infoValue": "productPage-module__Rcy5XW__infoValue",
  "keySpecsTable": "productPage-module__Rcy5XW__keySpecsTable",
  "label": "productPage-module__Rcy5XW__label",
  "loadingContainer": "productPage-module__Rcy5XW__loadingContainer",
  "loadingSpinner": "productPage-module__Rcy5XW__loadingSpinner",
  "price": "productPage-module__Rcy5XW__price",
  "priceRow": "productPage-module__Rcy5XW__priceRow",
  "priceSection": "productPage-module__Rcy5XW__priceSection",
  "productCode": "productPage-module__Rcy5XW__productCode",
  "productInfo": "productPage-module__Rcy5XW__productInfo",
  "productMain": "productPage-module__Rcy5XW__productMain",
  "productPage": "productPage-module__Rcy5XW__productPage",
  "productTitle": "productPage-module__Rcy5XW__productTitle",
  "purchaseSection": "productPage-module__Rcy5XW__purchaseSection",
  "quantityBtn": "productPage-module__Rcy5XW__quantityBtn",
  "quantityControls": "productPage-module__Rcy5XW__quantityControls",
  "quantityInput": "productPage-module__Rcy5XW__quantityInput",
  "quantitySelector": "productPage-module__Rcy5XW__quantitySelector",
  "rightSection": "productPage-module__Rcy5XW__rightSection",
  "specRow": "productPage-module__Rcy5XW__specRow",
  "spin": "productPage-module__Rcy5XW__spin",
  "value": "productPage-module__Rcy5XW__value",
});
}}),
"[project]/src/app/products/[id]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "default": (()=>ProductPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/products/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CartContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/CartContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$ProductImageGallery$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/products/ProductImageGallery/ProductImageGallery.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$ProductSpecifications$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/products/ProductSpecifications/ProductSpecifications.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$PatinaSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/products/PatinaSelector/PatinaSelector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$RelatedProducts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/products/RelatedProducts/RelatedProducts.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/app/products/[id]/productPage.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
function ProductPage() {
    _s();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const productId = parseInt(params.id);
    const { addToCart } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CartContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"])();
    const [product, setProduct] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [relatedProducts, setRelatedProducts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [quantity, setQuantity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [selectedPatina, setSelectedPatina] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('Alpine Stone');
    const [isAddingToCart, setIsAddingToCart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProductPage.useEffect": ()=>{
            if (productId) {
                fetchProductData();
            }
        }
    }["ProductPage.useEffect"], [
        productId
    ]);
    const fetchProductData = async ()=>{
        try {
            setIsLoading(true);
            setError(null);
            const [productData, relatedData] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["productService"].get.getById(productId),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["productService"].get.getRecommendations(productId, 6)
            ]);
            setProduct(productData);
            setRelatedProducts(relatedData);
        } catch (err) {
            console.error('Error fetching product:', err);
            setError('Failed to load product details');
        } finally{
            setIsLoading(false);
        }
    };
    const handleAddToCart = async ()=>{
        if (!product) return;
        try {
            setIsAddingToCart(true);
            await addToCart(product.id, quantity);
        // You could add a success notification here
        } catch (err) {
            console.error('Error adding to cart:', err);
        // You could add an error notification here
        } finally{
            setIsAddingToCart(false);
        }
    };
    const formatPrice = (price)=>{
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loadingContainer,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loadingSpinner
                }, void 0, false, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: "Loading product details..."
                }, void 0, false, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/products/[id]/page.tsx",
            lineNumber: 78,
            columnNumber: 7
        }, this);
    }
    if (error || !product) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorContainer,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    children: "Product Not Found"
                }, void 0, false, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 88,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    children: error || 'The requested product could not be found.'
                }, void 0, false, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 89,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/products/[id]/page.tsx",
            lineNumber: 87,
            columnNumber: 7
        }, this);
    }
    const isInStock = product.stock > 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productPage,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].container,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productMain,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].imageSection,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductImageGallery$2f$ProductImageGallery$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                images: product.images,
                                productName: product.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                lineNumber: 103,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/products/[id]/page.tsx",
                            lineNumber: 102,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].detailsSection,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productTitle,
                                    children: product.name
                                }, void 0, false, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 111,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productCode,
                                    children: [
                                        "Product Code: ",
                                        product.productCode || `P-${product.id.toString().padStart(3, '0')}-AS`
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this),
                                product.productSpecifications && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        console.log("Full productSpecifications object:", product.productSpecifications),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].keySpecsTable,
                                            children: [
                                                product.productSpecifications.pieces && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Pieces:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 124,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.pieces
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 125,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 123,
                                                    columnNumber: 19
                                                }, this),
                                                product.productSpecifications.material && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Material:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 130,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.material
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 131,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 129,
                                                    columnNumber: 19
                                                }, this),
                                                product.productSpecifications.dimensions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Dimensions:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 136,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.dimensions
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 137,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 135,
                                                    columnNumber: 19
                                                }, this),
                                                product.productSpecifications.totalWeight && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Total Weight:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 142,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.totalWeight
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 143,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 141,
                                                    columnNumber: 19
                                                }, this),
                                                product.productSpecifications.photographed_In && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Photographed In:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 148,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.photographed_In
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 149,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 147,
                                                    columnNumber: 19
                                                }, this),
                                                product.productSpecifications.base_Dimensions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].specRow,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].label,
                                                            children: "Base Dimensions:"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 154,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].value,
                                                            children: product.productSpecifications.base_Dimensions
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                                            lineNumber: 155,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 153,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/products/[id]/page.tsx",
                                            lineNumber: 121,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].productInfo,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].infoRow,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].infoLabel,
                                                children: "Availability:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 165,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].infoValue,
                                                children: isInStock ? `In Stock` : 'Out of Stock'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 166,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/products/[id]/page.tsx",
                                        lineNumber: 164,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 163,
                                    columnNumber: 14
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].priceSection,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].priceRow,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].price,
                                                children: formatPrice(product.price)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 181,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 187,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].quantitySelector,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "quantity",
                                                        children: "Quantity:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/products/[id]/page.tsx",
                                                        lineNumber: 189,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].quantityControls,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                type: "button",
                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),
                                                                disabled: quantity <= 1,
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].quantityBtn,
                                                                children: "-"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                                lineNumber: 191,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                id: "quantity",
                                                                type: "number",
                                                                value: quantity,
                                                                onChange: (e)=>setQuantity(Math.max(1, parseInt(e.target.value) || 1)),
                                                                min: "1",
                                                                max: product.stock,
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].quantityInput
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                                lineNumber: 199,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                type: "button",
                                                                onClick: ()=>setQuantity(Math.min(product.stock, quantity + 1)),
                                                                disabled: quantity >= product.stock,
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].quantityBtn,
                                                                children: "+"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                                lineNumber: 208,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/products/[id]/page.tsx",
                                                        lineNumber: 190,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 188,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].addToCartRow,
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].addToCartLabel,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: handleAddToCart,
                                                        disabled: isAddingToCart,
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$products$2f5b$id$5d2f$productPage$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].addToCartBtn,
                                                        children: isAddingToCart ? 'Adding...' : 'Add to Cart'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/products/[id]/page.tsx",
                                                        lineNumber: 222,
                                                        columnNumber: 17
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                                    lineNumber: 220,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/products/[id]/page.tsx",
                                                lineNumber: 219,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/products/[id]/page.tsx",
                                        lineNumber: 179,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 178,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$PatinaSelector$2f$PatinaSelector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    selectedPatina: selectedPatina,
                                    onPatinaChange: setSelectedPatina
                                }, void 0, false, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 234,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$ProductSpecifications$2f$ProductSpecifications$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    product: product
                                }, void 0, false, {
                                    fileName: "[project]/src/app/products/[id]/page.tsx",
                                    lineNumber: 241,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/products/[id]/page.tsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$products$2f$RelatedProducts$2f$RelatedProducts$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    products: relatedProducts
                }, void 0, false, {
                    fileName: "[project]/src/app/products/[id]/page.tsx",
                    lineNumber: 246,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/products/[id]/page.tsx",
            lineNumber: 98,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/products/[id]/page.tsx",
        lineNumber: 97,
        columnNumber: 5
    }, this);
}
_s(ProductPage, "if+JHI6pcfpfCBU6HDf9TBQyovQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CartContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"]
    ];
});
_c = ProductPage;
var _c;
__turbopack_context__.k.register(_c, "ProductPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_a973eeb5._.js.map