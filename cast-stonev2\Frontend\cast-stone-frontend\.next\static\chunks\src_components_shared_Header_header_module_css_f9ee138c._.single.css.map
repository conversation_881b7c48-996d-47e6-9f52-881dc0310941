{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Header/header.module.css"], "sourcesContent": ["/* Magazine/Editorial Theme Variables */\r\n.Headerroot {\r\n  --cast-stone-brown: #4a3728;\r\n  --cast-stone-light-brown: #6b4e3d;\r\n  --cast-stone-cream: #faf9f7;\r\n  --cast-stone-white: #ffffff;\r\n  --cast-stone-gray: #8b7355;\r\n  --cast-stone-shadow: rgba(74, 55, 40, 0.1);\r\n  --cast-stone-shadow-hover: rgba(74, 55, 40, 0.15);\r\n  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n/* Header Styles - Completely transparent design */\r\n.header {\r\n  background: transparent;\r\n  backdrop-filter: none;\r\n  border: none;\r\n  padding: 0;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 1000;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* Header when scrolled - stays transparent */\r\n.header.scrolled {\r\n  background: transparent;\r\n  backdrop-filter: none;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 1rem 2rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  height: 70px;\r\n}\r\n\r\n/* Logo Styles */\r\n.logo {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.logoLink {\r\n  text-decoration: none;\r\n  color: rgba(255, 255, 255, 0.95);\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.scrolled .logoLink {\r\n  color: rgba(255, 255, 255, 0.95);\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.logoLink:hover {\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.logoText {\r\n  font-size: 1.8rem;\r\n  font-weight: 600;\r\n  letter-spacing: 0.1em;\r\n  line-height: 1;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.logoSubtext {\r\n  font-size: 0.75rem;\r\n  font-weight: 400;\r\n  letter-spacing: 0.1em;\r\n  text-transform: uppercase;\r\n  color: var(--cast-stone-gray);\r\n  margin-top: 2px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n}\r\n\r\n/* Navigation Styles */\r\n.nav {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navList {\r\n  display: flex;\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  gap: 0;\r\n  align-items: center;\r\n}\r\n\r\n.navItem {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navLink,\r\n.navButton {\r\n  text-decoration: none;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 500;\r\n  font-size: 0.9rem;\r\n  padding: 1rem 1.5rem;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  letter-spacing: 0.05em;\r\n  text-transform: uppercase;\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.scrolled .navLink,\r\n.scrolled .navButton {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.navLink:hover,\r\n.navButton:hover {\r\n  color: rgba(255, 255, 255, 1);\r\n  transform: translateY(-1px);\r\n  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n.scrolled .navLink:hover,\r\n.scrolled .navButton:hover {\r\n  color: rgba(255, 255, 255, 1);\r\n  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n.navLink::after,\r\n.navButton::after {\r\n  content: '';\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 50%;\r\n  width: 0;\r\n  height: 2px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  transition: all 0.3s ease;\r\n  transform: translateX(-50%);\r\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.scrolled .navLink::after,\r\n.scrolled .navButton::after {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.navLink:hover::after,\r\n.navButton:hover::after,\r\n.navButton.active::after {\r\n  width: 80%;\r\n}\r\n\r\n/* Dropdown Styles */\r\n.dropdownContainer {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.dropdownIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: var(--transition-smooth);\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.dropdownIcon.rotated {\r\n  transform: rotate(180deg);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.loadingIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.dropdown {\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: var(--cast-stone-white);\r\n  border: 1px solid rgba(74, 55, 40, 0.1);\r\n  border-radius: 8px;\r\n  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);\r\n  min-width: 280px;\r\n  z-index: 1001;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transform: translateX(-50%) translateY(-10px);\r\n  transition: var(--transition-smooth);\r\n  animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n@keyframes dropdownSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    transform: translateX(-50%) translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    visibility: visible;\r\n    transform: translateX(-50%) translateY(0);\r\n  }\r\n}\r\n\r\n.dropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.dropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.dropdownLink {\r\n  display: block;\r\n  padding: 0.75rem 1.25rem;\r\n  color: var(--cast-stone-brown);\r\n  text-decoration: none;\r\n  font-size: 0.9rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 3px solid transparent;\r\n}\r\n\r\n.dropdownLink:hover {\r\n  background: rgba(74, 55, 40, 0.04);\r\n  color: var(--cast-stone-light-brown);\r\n  border-left-color: var(--cast-stone-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Sub-dropdown Styles */\r\n.subDropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  background: rgba(74, 55, 40, 0.02);\r\n  border-top: 1px solid rgba(74, 55, 40, 0.08);\r\n}\r\n\r\n.subDropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.subDropdownLink {\r\n  display: block;\r\n  padding: 0.6rem 1.25rem 0.6rem 2rem;\r\n  color: var(--cast-stone-gray);\r\n  text-decoration: none;\r\n  font-size: 0.85rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 3px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.subDropdownLink::before {\r\n  content: '→  ';\r\n  position: absolute;\r\n  left: 0.75rem;\r\n  color: var(--cast-stone-gray);\r\n  font-size: 0.7rem;\r\n  transition: var(--transition-fast);\r\n}\r\n\r\n.subDropdownLink:hover {\r\n  background: rgba(74, 55, 40, 0.06);\r\n  color: var(--cast-stone-brown);\r\n  border-left-color: var(--cast-stone-light-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n.subDropdownLink:hover::before {\r\n  color: var(--cast-stone-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Sub-sub-dropdown Styles (Level 3) */\r\n.subSubDropdownList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  background: rgba(74, 55, 40, 0.04);\r\n  border-top: 1px solid rgba(74, 55, 40, 0.12);\r\n}\r\n\r\n.subSubDropdownItem {\r\n  position: relative;\r\n}\r\n\r\n.subSubDropdownLink {\r\n  display: block;\r\n  padding: 0.5rem 1.25rem 0.5rem 2.5rem;\r\n  color: var(--cast-stone-gray);\r\n  text-decoration: none;\r\n  font-size: 0.8rem;\r\n  font-weight: 400;\r\n  transition: var(--transition-fast);\r\n  border-left: 6px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.subSubDropdownLink::before {\r\n  content: '⤷  ';\r\n  position: absolute;\r\n  left: 1.5rem;\r\n  color: var(--cast-stone-gray);\r\n  font-size: 0.65rem;\r\n  transition: var(--transition-fast);\r\n}\r\n\r\n.subSubDropdownLink:hover {\r\n  background: rgba(74, 55, 40, 0.08);\r\n  color: var(--cast-stone-brown);\r\n  border-left-color: var(--cast-stone-light-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n.subSubDropdownLink:hover::before {\r\n  color: var(--cast-stone-brown);\r\n  transform: translateX(2px);\r\n}\r\n\r\n/* Cart Styles */\r\n.cartContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 1rem;\r\n}\r\n\r\n.cartLink {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 44px;\r\n  height: 44px;\r\n  color: var(--cast-stone-brown);\r\n  text-decoration: none;\r\n  border-radius: 8px;\r\n  transition: var(--transition-smooth);\r\n  position: relative;\r\n}\r\n\r\n.cartLink:hover {\r\n  background: rgba(74, 55, 40, 0.06);\r\n  color: var(--cast-stone-light-brown);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.cartIconWrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.cartBadge {\r\n  position: absolute;\r\n  top: -8px;\r\n  right: -8px;\r\n  background: #dc2626;\r\n  color: white;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  min-width: 20px;\r\n  height: 20px;\r\n  border-radius: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0 4px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n  animation: cartBadgeAppear 0.3s ease-out;\r\n}\r\n\r\n@keyframes cartBadgeAppear {\r\n  0% {\r\n    transform: scale(0);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    transform: scale(1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n\r\n  .navList {\r\n    gap: 0;\r\n  }\r\n\r\n  .navLink,\r\n  .navButton {\r\n    padding: 1.5rem 1rem;\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n    height: 70px;\r\n  }\r\n\r\n  .logoText {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .logoSubtext {\r\n    font-size: 0.7rem;\r\n  }\r\n\r\n  .navList {\r\n    gap: 0;\r\n  }\r\n\r\n  .navLink,\r\n  .navButton {\r\n    padding: 1.25rem 0.75rem;\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .dropdown {\r\n    min-width: 200px;\r\n  }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .nav {\r\n    display: none; /* Will implement mobile menu in future */\r\n  }\r\n\r\n  .container {\r\n    justify-content: space-between;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAaA;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;;;;;;AAWA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;AAMA;;;;;;AAOA;;;;;AAMA;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;;;AAQA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;AAeA;EACE;;;;EAIA;;;;EAIA;;;;;;AAOF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA"}}]}