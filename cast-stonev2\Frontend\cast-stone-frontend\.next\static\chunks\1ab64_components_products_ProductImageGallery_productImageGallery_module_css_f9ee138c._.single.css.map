{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductImageGallery/productImageGallery.module.css"], "sourcesContent": ["/* Product Image Gallery - Sharp Rectangular Design */\r\n.imageGallery {\r\n  width: 100%;\r\n}\r\n\r\n/* Main Image Container */\r\n.mainImageContainer {\r\n  position: relative;\r\n  background: #f8f8f8;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n  aspect-ratio: 1;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.mainImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: zoom-in;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.mainImage.zoomed {\r\n  cursor: zoom-out;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.navButton {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  border: none;\r\n  width: 50px;\r\n  height: 50px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.navButton:hover {\r\n  background: rgba(0, 0, 0, 0.9);\r\n}\r\n\r\n.prevButton {\r\n  left: 10px;\r\n}\r\n\r\n.nextButton {\r\n  right: 10px;\r\n}\r\n\r\n/* Image Counter */\r\n.imageCounter {\r\n  position: absolute;\r\n  bottom: 15px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Zoom Indicator */\r\n.zoomIndicator {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #4a3728;\r\n  padding: 0.5rem;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  border: 1px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Thumbnail Container */\r\n/* .thumbnailContainer {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\r\n  gap: 0.5rem;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.thumbnail {\r\n  background: none;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  aspect-ratio: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n} */\r\n\r\n.thumbnailContainer {\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  white-space: nowrap;\r\n  padding: 8px 0;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.thumbnail {\r\n  display: inline-block;\r\n  border: none;\r\n  background: none;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 60px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border: 2px solid transparent;\r\n  border-radius: 4px;\r\n  transition: border 0.3s;\r\n}\r\n\r\n.activeThumbnail .thumbnailImage {\r\n  border-color: #0070f3; /* Highlight active */\r\n}\r\n\r\n.thumbnail:hover {\r\n  border-color: #4a3728;\r\n}\r\n\r\n.thumbnail.activeThumbnail {\r\n  border-color: #4a3728;\r\n  border-width: 3px;\r\n}\r\n\r\n\r\n\r\n/* Zoom Overlay */\r\n.zoomOverlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: zoom-out;\r\n}\r\n\r\n.zoomedImageContainer {\r\n  position: relative;\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n}\r\n\r\n.zoomedImage {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.closeZoomButton {\r\n  position: absolute;\r\n  top: -50px;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #4a3728;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.closeZoomButton:hover {\r\n  background: white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .navButton {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n  \r\n  .prevButton {\r\n    left: 5px;\r\n  }\r\n  \r\n  .nextButton {\r\n    right: 5px;\r\n  }\r\n  \r\n  .imageCounter {\r\n    bottom: 10px;\r\n    font-size: 0.8rem;\r\n    padding: 0.25rem 0.75rem;\r\n  }\r\n  \r\n  .zoomIndicator {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 0.25rem;\r\n    font-size: 0.7rem;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .zoomedImageContainer {\r\n    max-width: 95vw;\r\n    max-height: 95vh;\r\n  }\r\n  \r\n  .closeZoomButton {\r\n    top: -40px;\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .mainImageContainer {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n  \r\n  .navButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;AA6CA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;AAQA;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;;EAOA;;;;;EAKA;;;;;EAKA;;;;;;;AAOF;EACE;;;;EAIA;;;;;EAKA"}}]}