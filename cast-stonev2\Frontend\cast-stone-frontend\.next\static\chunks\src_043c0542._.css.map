{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartItem/cartItem.module.css"], "sourcesContent": ["/* Cart Item Styles - Magazine/Editorial Theme */\r\n.cartItem {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr auto auto auto;\r\n  gap: 1.5rem;\r\n  padding: 1.5rem;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  align-items: start;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.cartItem:hover {\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Product Image */\r\n.imageContainer {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n/* Product Details */\r\n.productDetails {\r\n  min-width: 0; /* Allow text truncation */\r\n}\r\n\r\n.productName {\r\n  color: #4a3728;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDescription {\r\n  color: #6b5b4d;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.productMeta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.unitPrice {\r\n  color: #4a3728;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.collection {\r\n  color: #8b7355;\r\n  font-size: 0.85rem;\r\n  font-style: italic;\r\n}\r\n\r\n.stockStatus {\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.inStock {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc2626;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Quantity Section */\r\n.quantitySection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  min-width: 120px;\r\n}\r\n\r\n.quantityLabel {\r\n  color: #4a3728;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.quantityControls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  padding: 0.25rem;\r\n}\r\n\r\n.quantityBtn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: 1px solid #d1d5db;\r\n  background: white;\r\n  color: #4a3728;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #8b7355;\r\n  color: white;\r\n  border-color: #8b7355;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantity {\r\n  min-width: 40px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #4a3728;\r\n  font-size: 1rem;\r\n}\r\n\r\n.updating {\r\n  color: #8b7355;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Price Section */\r\n.priceSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 0.25rem;\r\n  min-width: 120px;\r\n}\r\n\r\n.itemTotal {\r\n  color: #4a3728;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.priceBreakdown {\r\n  color: #6b5b4d;\r\n  font-size: 0.85rem;\r\n}\r\n\r\n/* Remove Section */\r\n.removeSection {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.removeBtn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #dc2626;\r\n  border-radius: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.removeBtn:hover:not(:disabled) {\r\n  background: #fee2e2;\r\n  color: #b91c1c;\r\n}\r\n\r\n.removeBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.removeBtn svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.removing {\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .cartItem {\r\n    grid-template-columns: 80px 1fr;\r\n    grid-template-areas: \r\n      \"image details\"\r\n      \"image quantity\"\r\n      \"image price\"\r\n      \"remove remove\";\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n\r\n  .imageContainer {\r\n    grid-area: image;\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .productDetails {\r\n    grid-area: details;\r\n  }\r\n\r\n  .quantitySection {\r\n    grid-area: quantity;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n    min-width: auto;\r\n  }\r\n\r\n  .quantityLabel {\r\n    margin-right: 0.5rem;\r\n  }\r\n\r\n  .priceSection {\r\n    grid-area: price;\r\n    align-items: flex-start;\r\n    min-width: auto;\r\n  }\r\n\r\n  .removeSection {\r\n    grid-area: remove;\r\n    justify-content: center;\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .itemTotal {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .cartItem {\r\n    grid-template-columns: 1fr;\r\n    grid-template-areas: \r\n      \"image\"\r\n      \"details\"\r\n      \"quantity\"\r\n      \"price\"\r\n      \"remove\";\r\n    text-align: center;\r\n  }\r\n\r\n  .imageContainer {\r\n    width: 120px;\r\n    height: 120px;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .quantitySection {\r\n    justify-content: center;\r\n  }\r\n\r\n  .priceSection {\r\n    align-items: center;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartSummary/cartSummary.module.css"], "sourcesContent": ["/* Cart Summary Styles - Magazine/Editorial Theme */\r\n.cartSummary {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  position: sticky;\r\n  top: 2rem;\r\n  height: fit-content;\r\n}\r\n\r\n.title {\r\n  color: #4a3728;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1.5rem 0;\r\n  text-align: center;\r\n  border-bottom: 2px solid #f3f4f6;\r\n  padding-bottom: 1rem;\r\n}\r\n\r\n/* Summary Details */\r\n.summaryDetails {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.summaryRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.label {\r\n  color: #6b5b4d;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.value {\r\n  color: #4a3728;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.freeShipping {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.divider {\r\n  height: 1px;\r\n  background: #e5e7eb;\r\n  margin: 1rem 0;\r\n}\r\n\r\n.totalRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem 0;\r\n  border-top: 2px solid #4a3728;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.totalLabel {\r\n  color: #4a3728;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.totalValue {\r\n  color: #4a3728;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n/* Shipping Notice */\r\n.shippingNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background: #f0f9ff;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 6px;\r\n  padding: 0.75rem;\r\n  margin-bottom: 1.5rem;\r\n  color: #0369a1;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.infoIcon {\r\n  width: 16px;\r\n  height: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Action Buttons */\r\n.actionButtons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.checkoutBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 1.5rem;\r\n  background: #8b7355;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.checkoutBtn:hover {\r\n  background: #6d5a47;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\r\n}\r\n\r\n.checkoutIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.clearBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 0.75rem 1rem;\r\n  background: transparent;\r\n  color: #dc2626;\r\n  border: 2px solid #dc2626;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clearBtn:hover:not(:disabled) {\r\n  background: #dc2626;\r\n  color: white;\r\n}\r\n\r\n.clearBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.clearIcon {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Security Notice */\r\n.securityNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  color: #059669;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.securityIcon {\r\n  width: 16px;\r\n  height: 16px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .cartSummary {\r\n    position: static;\r\n    margin-top: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .cartSummary {\r\n    padding: 1.5rem;\r\n    margin-top: 1.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.25rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .totalLabel {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .totalValue {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .checkoutBtn {\r\n    padding: 0.875rem 1.25rem;\r\n    font-size: 0.95rem;\r\n  }\r\n\r\n  .actionButtons {\r\n    gap: 0.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .cartSummary {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .summaryRow {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n\r\n  .label,\r\n  .value {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .totalRow {\r\n    padding: 0.75rem 0;\r\n  }\r\n\r\n  .checkoutBtn {\r\n    padding: 0.75rem 1rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .clearBtn {\r\n    padding: 0.625rem 0.875rem;\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAOA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/cart/cart.module.css"], "sourcesContent": ["/* Cart Page Styles - Magazine/Editorial Theme */\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 2rem 1rem;\r\n  min-height: 80vh;\r\n}\r\n\r\n/* Header */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 2rem;\r\n  border-bottom: 2px solid #f3f4f6;\r\n}\r\n\r\n.title {\r\n  color: #4a3728;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.subtitle {\r\n  color: #6b5b4d;\r\n  font-size: 1.1rem;\r\n  margin: 0;\r\n}\r\n\r\n/* Loading State */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  color: #6b5b4d;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f4f6;\r\n  border-top: 3px solid #8b7355;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Empty Cart */\r\n.emptyCart {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 400px;\r\n  padding: 2rem;\r\n}\r\n\r\n.emptyIcon {\r\n  width: 120px;\r\n  height: 120px;\r\n  color: #d1d5db;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.emptyIcon svg {\r\n  width: 100%;\r\n  height: 100%;\r\n  stroke-width: 1.5;\r\n}\r\n\r\n.emptyTitle {\r\n  color: #4a3728;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.emptyMessage {\r\n  color: #6b5b4d;\r\n  font-size: 1.1rem;\r\n  line-height: 1.6;\r\n  margin: 0 0 2rem 0;\r\n  max-width: 500px;\r\n}\r\n\r\n.shopNowBtn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 2rem;\r\n  background: #8b7355;\r\n  color: white;\r\n  border-radius: 6px;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.shopNowBtn:hover {\r\n  background: #6d5a47;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.3);\r\n}\r\n\r\n.shopIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Error Message */\r\n.errorMessage {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  background: #fef2f2;\r\n  border: 1px solid #fecaca;\r\n  border-radius: 6px;\r\n  padding: 1rem;\r\n  margin-bottom: 2rem;\r\n  color: #dc2626;\r\n}\r\n\r\n.errorIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  flex-shrink: 0;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Cart Content */\r\n.cartContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 350px;\r\n  gap: 3rem;\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n/* Cart Items */\r\n.cartItems {\r\n  min-width: 0; /* Allow content to shrink */\r\n}\r\n\r\n.itemsHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1.5rem;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.itemsHeader h2 {\r\n  color: #4a3728;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0;\r\n}\r\n\r\n.continueShoppingLink {\r\n  color: #8b7355;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.continueShoppingLink:hover {\r\n  color: #6d5a47;\r\n  text-decoration: underline;\r\n}\r\n\r\n.itemsList {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n}\r\n\r\n/* Cart Summary Container */\r\n.cartSummaryContainer {\r\n  /* Styles handled by CartSummary component */\r\n}\r\n\r\n/* Additional Actions */\r\n.additionalActions {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 2rem;\r\n  padding-top: 2rem;\r\n  border-top: 2px solid #f3f4f6;\r\n}\r\n\r\n.helpSection,\r\n.shippingInfo {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n}\r\n\r\n.helpSection h3,\r\n.shippingInfo h3 {\r\n  color: #4a3728;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1rem 0;\r\n}\r\n\r\n.helpSection p {\r\n  color: #6b5b4d;\r\n  line-height: 1.6;\r\n  margin: 0;\r\n}\r\n\r\n.contactLink {\r\n  color: #8b7355;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n}\r\n\r\n.contactLink:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.shippingInfo ul {\r\n  list-style: none;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.shippingInfo li {\r\n  color: #6b5b4d;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.shippingInfo li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.shippingInfo li::before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #059669;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .cartContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .header {\r\n    margin-bottom: 2rem;\r\n    padding-bottom: 1.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .cartContent {\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .additionalActions {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .helpSection,\r\n  .shippingInfo {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .emptyIcon {\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .emptyTitle {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .emptyMessage {\r\n    font-size: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .itemsHeader {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .shopNowBtn {\r\n    padding: 0.875rem 1.5rem;\r\n    font-size: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAYA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;EACE;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA", "debugId": null}}]}