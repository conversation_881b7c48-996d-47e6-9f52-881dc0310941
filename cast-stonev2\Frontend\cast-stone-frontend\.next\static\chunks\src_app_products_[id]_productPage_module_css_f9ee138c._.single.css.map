{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/[id]/productPage.module.css"], "sourcesContent": ["/* Product Page Styles - Sharp Rectangular Design */\r\n.productPage {\r\n  padding-top: 6rem;\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 60vh;\r\n  color: #1e40af;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #1e40af;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #1e40af;\r\n}\r\n\r\n.errorContainer h1 {\r\n  font-size: 2rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Main Product Layout */\r\n.productMain {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 4rem;\r\n  margin-bottom: 4rem;\r\n}\r\n\r\n.imageSection {\r\n  position: relative;\r\n}\r\n\r\n.detailsSection {\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* Product Title */\r\n.productTitle {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #1a1a1a;\r\n  margin-bottom: 1rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* Product Code */\r\n.productCode {\r\n  background: #f5f5f5;\r\n  border: 1px solid #ddd;\r\n  padding: 0.4rem 1rem;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 0.9rem;\r\n  color:rgb(0, 0, 0);\r\n  margin-bottom: 2rem;\r\n  border-radius: 0; /* Sharp corners */\r\n   display: inline-block;   \r\n}\r\n\r\n/* Product Info Grid */\r\n.productInfo {\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.infoRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  line-height: 1.4;\r\n}\r\n\r\n.infoLabel {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.infoValue {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  flex: 1;\r\n}\r\n/* Price Section */\r\n.priceSection {\r\n  margin: 2rem 0;\r\n  padding: 1.5rem;\r\n  background: white;\r\n  /* border: 2px solid black; */\r\n  border-radius: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n  align-items: flex-start;\r\n}\r\n\r\n.priceRow {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: center;\r\n  gap: 2rem;\r\n  width: 100%;\r\n}\r\n\r\n\r\n.purchaseSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n  margin-top: 0; /* Remove extra space */\r\n}\r\n\r\n.price {\r\n  background:#f5f5f5;\r\n  padding: 6px;\r\n  font-size: 2rem;\r\n  font-weight: 520;\r\n  color:rgb(0, 0, 0);\r\n}\r\n\r\n/* Purchase Section */\r\n.purchaseSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n  margin-top: 2rem;\r\n}\r\n\r\n.quantitySelector {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 2rem;\r\n}\r\n\r\n.quantitySelector label {\r\n  font-weight: 600;\r\n  color:rgb(0, 0, 0);\r\n}\r\n\r\n.quantityControls {\r\n  display: flex;\r\n  align-items: center;\r\n  border: 2px solidrgb(0, 0, 0);\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n}\r\n\r\n.quantityBtn {\r\n  background: #f5f5f5;\r\n  color: black;\r\n  border: black;\r\n  padding: 0.75rem 1rem;\r\n  cursor: pointer;\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #1e40af;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  background: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantityInput {\r\n  border: none;\r\n  padding: 0.75rem 1rem;\r\n  text-align: center;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  width: 80px;\r\n  background: white;\r\n  outline: none;\r\n  color: black;\r\n}\r\n\r\n.addToCartRow {\r\n  display: grid;\r\n  grid-template-columns: 150px 1fr;\r\n  width: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.addToCartLabel {\r\nwidth: 400%;\r\n}\r\n\r\n.addToCartWrapper {\r\n  display: flex;\r\n  justify-content: center; \r\n  width: 100%;\r\n}\r\n\r\n.addToCartBtn {\r\n  background: #f5f5f5;\r\n  color: black;\r\n  border: 2px solidrgb(0, 0, 0);\r\n  padding: 1rem 2rem;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  width: 100%;   \r\n  max-width: 300px; \r\n}\r\n\r\n.addToCartBtn:hover:not(:disabled) {\r\n  background:rgb(91, 88, 88);\r\n  color:black;\r\n}\r\n\r\n.addToCartBtn:disabled {\r\n  background: #ccc;\r\n  border-color: #ccc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .productMain {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n  \r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productTitle {\r\n    font-size: 2rem;\r\n  }\r\n  .rightSection {\r\n    gap: 2rem;\r\n  }\r\n\r\n.keySpecsTable {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  margin-top: 20px;\r\n  font-size: 16px;\r\n  font-family: 'Arial', sans-serif;\r\n  color: #111;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.specRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: baseline;\r\n  padding: 0.75rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  font-size: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  min-width: 160px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.value {\r\n  color: #1f2937;\r\n  flex: 1;\r\n  text-align: left;\r\n}\r\n\r\n  /* .specRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  line-height: 1.4;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.value {\r\n  color: #1f2937;\r\n  flex: 1;\r\n} */\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .infoRow {\r\n    display: flex;\r\n    align-items: baseline;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .infoLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n  \r\n  .purchaseSection {\r\n    position: sticky;\r\n    bottom: 0;\r\n    background: white;\r\n    padding: 1rem;\r\n    border-top: 2px solid #1e40af;\r\n    margin: 2rem -1rem 0;\r\n  }\r\n  \r\n  .quantitySelector {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .quantityControls {\r\n    justify-content: center;\r\n  }\r\n\r\n.rightSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.keySpecsTable {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  margin-top: 20px;\r\n  font-size: 16px;\r\n  font-family: 'Arial', sans-serif;\r\n  color: #111;\r\n}\r\n\r\n  .infoLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .infoValue {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n  }\r\n  .productTitle {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .productCode {\r\n    font-size: 0.8rem;\r\n    padding: 0.5rem;\r\n  }\r\n  .specRow {\r\n    display: flex;\r\n    align-items: baseline;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n.label {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .value {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n    word-break: break-word;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;AAKA;;;;;;;;AASA;;;;;;;;;;;;AAaA;;;;AAIA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;;AAYA;;;;;;;;AAgBA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;EAGA;;;;EAIF;;;;;;;;;;EAUA;;;;;;;;;;EAUA;;;;;;;EAOA;;;;;;;AA2BA;EACE;;;;;;;;EAQA;;;;;;;EAOA;;;;;;;;;EASA;;;;;;EAMA;;;;EAIF;;;;;;EAMA;;;;;;;;;EASE;;;;;;;;EAQA;;;;;;EAKA;;;;EAQA;;;;;EAIA;;;;;;;;EAQF;;;;;;;;EAQE"}}]}