/* [project]/src/app/collections/collections.module.css [app-client] (css) */
.collections-module__IO8dua__container {
  color: #fff;
  background: #1a1a2e;
  min-height: 100vh;
  padding: 2rem 1rem;
}

.collections-module__IO8dua__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
}

.collections-module__IO8dua__title {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 3rem;
  font-weight: 700;
}

.collections-module__IO8dua__subtitle {
  color: #b0b0b0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.collections-module__IO8dua__loading, .collections-module__IO8dua__error {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
}

.collections-module__IO8dua__error {
  color: #ff6b6b;
}

.collections-module__IO8dua__collectionsGrid {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.collections-module__IO8dua__collectionCard {
  color: inherit;
  background: #2a2a3e;
  border: 2px solid #0000;
  border-radius: 12px;
  text-decoration: none;
  transition: all .3s;
  overflow: hidden;
}

.collections-module__IO8dua__collectionCard:hover {
  border-color: #4a90e2;
  transform: translateY(-5px);
  box-shadow: 0 10px 30px #4a90e233;
}

.collections-module__IO8dua__imageContainer {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.collections-module__IO8dua__collectionImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.collections-module__IO8dua__collectionCard:hover .collections-module__IO8dua__collectionImage {
  transform: scale(1.05);
}

.collections-module__IO8dua__placeholderImage {
  color: #888;
  background: #3a3a4e;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  font-size: 1rem;
  display: flex;
}

.collections-module__IO8dua__cardContent {
  padding: 1.5rem;
}

.collections-module__IO8dua__collectionName {
  color: #fff;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.collections-module__IO8dua__collectionDescription {
  color: #b0b0b0;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 1rem;
  line-height: 1.5;
  display: -webkit-box;
  overflow: hidden;
}

.collections-module__IO8dua__cardFooter {
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  display: flex;
}

.collections-module__IO8dua__viewCollection {
  color: #4a90e2;
  font-size: .9rem;
  font-weight: 500;
}

.collections-module__IO8dua__emptyState {
  text-align: center;
  color: #888;
  padding: 4rem 2rem;
}

.collections-module__IO8dua__emptyState h3 {
  color: #fff;
  margin-bottom: .5rem;
  font-size: 1.5rem;
}

.collections-module__IO8dua__emptyState p {
  color: #b0b0b0;
  font-size: 1rem;
}

@media (width <= 768px) {
  .collections-module__IO8dua__title {
    font-size: 2rem;
  }

  .collections-module__IO8dua__collectionsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .collections-module__IO8dua__container {
    padding: 1rem;
  }
}


/*# sourceMappingURL=src_app_collections_collections_module_8ddd73c9.css.map*/