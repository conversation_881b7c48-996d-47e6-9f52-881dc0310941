{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"activeThumbnail\": \"productImageGallery-module__AcqG6a__activeThumbnail\",\n  \"closeZoomButton\": \"productImageGallery-module__AcqG6a__closeZoomButton\",\n  \"imageCounter\": \"productImageGallery-module__AcqG6a__imageCounter\",\n  \"imageGallery\": \"productImageGallery-module__AcqG6a__imageGallery\",\n  \"mainImage\": \"productImageGallery-module__AcqG6a__mainImage\",\n  \"mainImageContainer\": \"productImageGallery-module__AcqG6a__mainImageContainer\",\n  \"navButton\": \"productImageGallery-module__AcqG6a__navButton\",\n  \"nextButton\": \"productImageGallery-module__AcqG6a__nextButton\",\n  \"prevButton\": \"productImageGallery-module__AcqG6a__prevButton\",\n  \"thumbnail\": \"productImageGallery-module__AcqG6a__thumbnail\",\n  \"thumbnailContainer\": \"productImageGallery-module__AcqG6a__thumbnailContainer\",\n  \"thumbnailGrid\": \"productImageGallery-module__AcqG6a__thumbnailGrid\",\n  \"thumbnailImage\": \"productImageGallery-module__AcqG6a__thumbnailImage\",\n  \"zoomIndicator\": \"productImageGallery-module__AcqG6a__zoomIndicator\",\n  \"zoomOverlay\": \"productImageGallery-module__AcqG6a__zoomOverlay\",\n  \"zoomed\": \"productImageGallery-module__AcqG6a__zoomed\",\n  \"zoomedImage\": \"productImageGallery-module__AcqG6a__zoomedImage\",\n  \"zoomedImageContainer\": \"productImageGallery-module__AcqG6a__zoomedImageContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductImageGallery/ProductImageGallery.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport styles from './productImageGallery.module.css';\r\n\r\ninterface ProductImageGalleryProps {\r\n  images: string[];\r\n  productName: string;\r\n}\r\n\r\nconst ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ \r\n  images, \r\n  productName \r\n}) => {\r\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\r\n  const [isZoomed, setIsZoomed] = useState(false);\r\n\r\n  // Use placeholder if no images provided\r\n  const galleryImages = images.length > 0 \r\n    ? images \r\n    : ['/images/placeholder-product.jpg'];\r\n\r\n  const currentImage = galleryImages[selectedImageIndex];\r\n\r\n  const handleThumbnailClick = (index: number) => {\r\n    setSelectedImageIndex(index);\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  const handleMainImageClick = () => {\r\n    setIsZoomed(!isZoomed);\r\n  };\r\n\r\n  const handlePrevImage = () => {\r\n    setSelectedImageIndex((prev) => \r\n      prev === 0 ? galleryImages.length - 1 : prev - 1\r\n    );\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  const handleNextImage = () => {\r\n    setSelectedImageIndex((prev) => \r\n      prev === galleryImages.length - 1 ? 0 : prev + 1\r\n    );\r\n    setIsZoomed(false);\r\n  };\r\n\r\n  return (\r\n    <div className={styles.imageGallery}>\r\n      {/* Main Image Display */}\r\n      <div className={styles.mainImageContainer}>\r\n        <img\r\n          src={currentImage}\r\n          alt={`${productName} - Image ${selectedImageIndex + 1}`}\r\n          className={`${styles.mainImage} ${isZoomed ? styles.zoomed : ''}`}\r\n          onClick={handleMainImageClick}\r\n        />\r\n        \r\n        {/* Navigation Arrows */}\r\n        {galleryImages.length > 1 && (\r\n          <>\r\n            <button\r\n              className={`${styles.navButton} ${styles.prevButton}`}\r\n              onClick={handlePrevImage}\r\n              aria-label=\"Previous image\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M15 18L9 12L15 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            \r\n            <button\r\n              className={`${styles.navButton} ${styles.nextButton}`}\r\n              onClick={handleNextImage}\r\n              aria-label=\"Next image\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M9 18L15 12L9 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </>\r\n        )}\r\n\r\n        {/* Image Counter */}\r\n        {galleryImages.length > 1 && (\r\n          <div className={styles.imageCounter}>\r\n            {selectedImageIndex + 1} / {galleryImages.length}\r\n          </div>\r\n        )}\r\n\r\n        {/* Zoom Indicator */}\r\n        <div className={styles.zoomIndicator}>\r\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n            <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\r\n            <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n            <line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n            <line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n          </svg>\r\n          Click to zoom\r\n        </div>\r\n      </div>\r\n\r\n      {/* Thumbnail Gallery */}\r\n      {galleryImages.length > 1 && (\r\n        <div className={styles.thumbnailContainer}>\r\n          <div className={styles.thumbnailGrid}>\r\n            {galleryImages.map((image, index) => (\r\n              <button\r\n                key={index}\r\n                className={`${styles.thumbnail} ${\r\n                  index === selectedImageIndex ? styles.activeThumbnail : ''\r\n                }`}\r\n                onClick={() => handleThumbnailClick(index)}\r\n                aria-label={`View image ${index + 1}`}\r\n              >\r\n                <img\r\n                  src={image}\r\n                  alt={`${productName} thumbnail ${index + 1}`}\r\n                  className={styles.thumbnailImage}\r\n                />\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Zoom Overlay */}\r\n      {isZoomed && (\r\n        <div className={styles.zoomOverlay} onClick={() => setIsZoomed(false)}>\r\n          <div className={styles.zoomedImageContainer}>\r\n            <img\r\n              src={currentImage}\r\n              alt={`${productName} - Zoomed view`}\r\n              className={styles.zoomedImage}\r\n            />\r\n            <button\r\n              className={styles.closeZoomButton}\r\n              onClick={() => setIsZoomed(false)}\r\n              aria-label=\"Close zoom view\"\r\n            >\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductImageGallery;\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AACA;;;AAHA;;;AAUA,MAAM,sBAA0D,CAAC,EAC/D,MAAM,EACN,WAAW,EACZ;;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wCAAwC;IACxC,MAAM,gBAAgB,OAAO,MAAM,GAAG,IAClC,SACA;QAAC;KAAkC;IAEvC,MAAM,eAAe,aAAa,CAAC,mBAAmB;IAEtD,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,uBAAuB;QAC3B,YAAY,CAAC;IACf;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,IAAI,cAAc,MAAM,GAAG,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,cAAc,MAAM,GAAG,IAAI,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,qBACE,6LAAC;QAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,YAAY;;0BAEjC,6LAAC;gBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACvC,6LAAC;wBACC,KAAK;wBACL,KAAK,GAAG,YAAY,SAAS,EAAE,qBAAqB,GAAG;wBACvD,WAAW,GAAG,0LAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,0LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;wBACjE,SAAS;;;;;;oBAIV,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC;gCACC,WAAW,GAAG,0LAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,0LAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCACC,WAAW,GAAG,0LAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,0LAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;oBAQxB,cAAc,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,YAAY;;4BAChC,qBAAqB;4BAAE;4BAAI,cAAc,MAAM;;;;;;;kCAKpD,6LAAC;wBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,6LAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAI,QAAO;wCAAe,aAAY;;;;;;kDAChE,6LAAC;wCAAK,GAAE;wCAAqB,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACjF,6LAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACzF,6LAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;4BACrF;;;;;;;;;;;;;YAMT,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,kBAAkB;0BACvC,cAAA,6LAAC;oBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,aAAa;8BACjC,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;4BAEC,WAAW,GAAG,0LAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAC9B,UAAU,qBAAqB,0LAAA,CAAA,UAAM,CAAC,eAAe,GAAG,IACxD;4BACF,SAAS,IAAM,qBAAqB;4BACpC,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;sCAErC,cAAA,6LAAC;gCACC,KAAK;gCACL,KAAK,GAAG,YAAY,WAAW,EAAE,QAAQ,GAAG;gCAC5C,WAAW,0LAAA,CAAA,UAAM,CAAC,cAAc;;;;;;2BAV7B;;;;;;;;;;;;;;;YAmBd,0BACC,6LAAC;gBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,WAAW;gBAAE,SAAS,IAAM,YAAY;0BAC7D,cAAA,6LAAC;oBAAI,WAAW,0LAAA,CAAA,UAAM,CAAC,oBAAoB;;sCACzC,6LAAC;4BACC,KAAK;4BACL,KAAK,GAAG,YAAY,cAAc,CAAC;4BACnC,WAAW,0LAAA,CAAA,UAAM,CAAC,WAAW;;;;;;sCAE/B,6LAAC;4BACC,WAAW,0LAAA,CAAA,UAAM,CAAC,eAAe;4BACjC,SAAS,IAAM,YAAY;4BAC3B,cAAW;sCAEX,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,6LAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACxF,6LAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG;GAxJM;KAAA;uCA0JS", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"productSpecifications-module__J4NrIa__active\",\n  \"careContent\": \"productSpecifications-module__J4NrIa__careContent\",\n  \"careSection\": \"productSpecifications-module__J4NrIa__careSection\",\n  \"description\": \"productSpecifications-module__J4NrIa__description\",\n  \"detailsContent\": \"productSpecifications-module__J4NrIa__detailsContent\",\n  \"downloadLink\": \"productSpecifications-module__J4NrIa__downloadLink\",\n  \"downloadLinks\": \"productSpecifications-module__J4NrIa__downloadLinks\",\n  \"downloadSection\": \"productSpecifications-module__J4NrIa__downloadSection\",\n  \"featureList\": \"productSpecifications-module__J4NrIa__featureList\",\n  \"inStock\": \"productSpecifications-module__J4NrIa__inStock\",\n  \"outOfStock\": \"productSpecifications-module__J4NrIa__outOfStock\",\n  \"section\": \"productSpecifications-module__J4NrIa__section\",\n  \"sectionContent\": \"productSpecifications-module__J4NrIa__sectionContent\",\n  \"sectionHeader\": \"productSpecifications-module__J4NrIa__sectionHeader\",\n  \"shareButton\": \"productSpecifications-module__J4NrIa__shareButton\",\n  \"shareButtons\": \"productSpecifications-module__J4NrIa__shareButtons\",\n  \"shareLabel\": \"productSpecifications-module__J4NrIa__shareLabel\",\n  \"shareSection\": \"productSpecifications-module__J4NrIa__shareSection\",\n  \"specGrid\": \"productSpecifications-module__J4NrIa__specGrid\",\n  \"specLabel\": \"productSpecifications-module__J4NrIa__specLabel\",\n  \"specRow\": \"productSpecifications-module__J4NrIa__specRow\",\n  \"specValue\": \"productSpecifications-module__J4NrIa__specValue\",\n  \"specificationsContainer\": \"productSpecifications-module__J4NrIa__specificationsContainer\",\n  \"tag\": \"productSpecifications-module__J4NrIa__tag\",\n  \"tagContainer\": \"productSpecifications-module__J4NrIa__tagContainer\",\n  \"toggleIcon\": \"productSpecifications-module__J4NrIa__toggleIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductSpecifications/ProductSpecifications.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Product } from '@/services/types/entities';\r\nimport styles from './productSpecifications.module.css';\r\n\r\ninterface ProductSpecificationsProps {\r\n  product: Product;\r\n}\r\n\r\nconst ProductSpecifications: React.FC<ProductSpecificationsProps> = ({ product }) => {\r\n  const [activeSection, setActiveSection] = useState<string | null>('specifications');\r\n\r\n  const toggleSection = (section: string) => {\r\n    setActiveSection(activeSection === section ? null : section);\r\n  };\r\n\r\nconst hasSpecifications = product.productSpecifications &&\r\n  Object.values(product.productSpecifications).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDetails = product.productDetails &&\r\n  Object.values(product.productDetails).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDownloadableContent = product.downloadableContent &&\r\n  Object.values(product.downloadableContent).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\n\r\n  return (\r\n    <div className={styles.specificationsContainer}>\r\n      {/* Product Specifications Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'specifications' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('specifications')}\r\n        >\r\n          <span>Product Specifications</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'specifications' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'specifications' && hasSpecifications && (\r\n        \r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.specGrid}>\r\n              {product.productSpecifications?.material && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Material:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.material}</span>\r\n                </div>\r\n              )}\r\n \r\n              {product.productSpecifications?.dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.dimensions}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.totalWeight && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Total Weight:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.totalWeight}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.weightWithWater && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Weight With Water:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.weightWithWater}</span>\r\n                </div>\r\n              )}\r\n{/* ---- -------------------------------------------------------------------------------------------------- */}\r\n\r\n              {/* {product.productSpecifications?.base_Dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Base Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.base_Dimensions}</span>\r\n                </div>\r\n              )} */}\r\n\r\n              {/* {product.productSpecifications?.photographed_In && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Photographed In:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.photographed_In}</span>\r\n                </div>\r\n              )} */}\r\n\r\n              {/* {product.productSpecifications?.pieces && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Pieces:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.pieces}</span>\r\n                </div>\r\n              )} */}\r\n              {product.productSpecifications?.waterVolume && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Water Volume:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.waterVolume}</span>\r\n                </div>\r\n              )}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Collection:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.collection?.name || 'Not specified'}\r\n                </span>\r\n              </div> */}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Product Code:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.productCode || `P-${product.id.toString().padStart(3, '0')}-AS`}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Stock Status:</span>\r\n                <span className={`${styles.specValue} ${\r\n                  product.stock > 0 ? styles.inStock : styles.outOfStock\r\n                }`}>\r\n                  {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {product.tags && product.tags.length > 0 && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Tags:</span>\r\n                  <span className={styles.specValue}>\r\n                    <div className={styles.tagContainer}>\r\n                      {product.tags.map((tag, index) => (\r\n                        <span key={index} className={styles.tag}>\r\n                          {tag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Details Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'details' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('details')}\r\n        >\r\n          <span>Product Details</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'details' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'details' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.detailsContent}>\r\n              {product.description && (\r\n                <p className={styles.description}>{product.description}</p>\r\n              )}\r\n\r\n              {hasDetails && (\r\n                <div className={styles.specGrid}>\r\n                  {product.productDetails?.upc && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>UPC:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.upc}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.indoorUseOnly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Indoor Use Only:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.indoorUseOnly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assemblyRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assembly Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assemblyRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.easeOfAssembly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ease of Assembly:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.easeOfAssembly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assistanceRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assistance Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assistanceRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.splashLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Splash Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.splashLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundType && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Type:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundType}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.replacementPumpKit && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Replacement Pump Kit:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.replacementPumpKit}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.electricalCordLength && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Electrical Cord Length:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.electricalCordLength}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.pumpSize && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Pump Size:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.pumpSize}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.shipMethod && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ship Method:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.shipMethod}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.drainage_Info && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Drainage Info:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.drainage_Info}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Top && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Top:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Top}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Bottom && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Bottom:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Bottom}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Height && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Height:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Height}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.factory_Code && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Factory Code:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.factory_Code}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.catalogPage && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Catalog Page:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.catalogPage}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Care Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'care' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('care')}\r\n        >\r\n          <span>Product Care and Downloadable Content</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'care' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'care' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.careContent}>\r\n              {hasDownloadableContent && (\r\n                <div className={styles.downloadSection}>\r\n                  <h4>Downloadable Content:</h4>\r\n                  <div className={styles.downloadLinks}>\r\n                    {product.downloadableContent?.care && (\r\n                      <a\r\n                        href={product.downloadableContent.care}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📄 Care Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.productInstructions && (\r\n                      <a\r\n                        href={product.downloadableContent.productInstructions}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📋 Product Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.cad && (\r\n                      <a\r\n                        href={product.downloadableContent.cad}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📐 CAD Files\r\n                      </a>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}              \r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Share Section */}\r\n      <div className={styles.shareSection}>\r\n        <span className={styles.shareLabel}>Share</span>\r\n        <div className={styles.shareButtons}>\r\n          <button className={styles.shareButton} aria-label=\"Share on Facebook\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n            </svg>\r\n          </button>\r\n          \r\n          <button className={styles.shareButton} aria-label=\"Share on Pinterest\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-12.014C24.007 5.36 18.641.001 12.017.001z\"/>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductSpecifications;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAUA,MAAM,wBAA8D,CAAC,EAAE,OAAO,EAAE;;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,gBAAgB,CAAC;QACrB,iBAAiB,kBAAkB,UAAU,OAAO;IACtD;IAEF,MAAM,oBAAoB,QAAQ,qBAAqB,IACrD,OAAO,MAAM,CAAC,QAAQ,qBAAqB,EAAE,IAAI,CAC/C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,aAAa,QAAQ,cAAc,IACvC,OAAO,MAAM,CAAC,QAAQ,cAAc,EAAE,IAAI,CACxC,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,yBAAyB,QAAQ,mBAAmB,IACxD,OAAO,MAAM,CAAC,QAAQ,mBAAmB,EAAE,IAAI,CAC7C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAIzF,qBACE,6LAAC;QAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,uBAAuB;;0BAE5C,6LAAC;gBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,6LAAC;wBACC,WAAW,GAAG,8LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,mBAAmB,8LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IACrD;wBACF,SAAS,IAAM,cAAc;;0CAE7B,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,mBAAmB,MAAM;;;;;;;;;;;;oBAI/C,kBAAkB,oBAAoB,mCAErC,6LAAC;wBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,6LAAC;4BAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,QAAQ;;gCAC5B,QAAQ,qBAAqB,EAAE,0BAC9B,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,QAAQ;;;;;;;;;;;;gCAI7E,QAAQ,qBAAqB,EAAE,4BAC9B,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,UAAU;;;;;;;;;;;;gCAI/E,QAAQ,qBAAqB,EAAE,6BAC9B,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCAIhF,QAAQ,qBAAqB,EAAE,iCAC9B,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gCAyBpF,QAAQ,qBAAqB,EAAE,6BAC9B,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCA2BhF,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,6LAAC;4CAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;sDAC/B,cAAA,6LAAC;gDAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;0DAChC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;wDAAiB,WAAW,8LAAA,CAAA,UAAM,CAAC,GAAG;kEACpC;uDADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc7B,6LAAC;gBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,6LAAC;wBACC,WAAW,GAAG,8LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,YAAY,8LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,YAAY,MAAM;;;;;;;;;;;;oBAIxC,kBAAkB,2BACjB,6LAAC;wBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,6LAAC;4BAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,cAAc;;gCAClC,QAAQ,WAAW,kBAClB,6LAAC;oCAAE,WAAW,8LAAA,CAAA,UAAM,CAAC,WAAW;8CAAG,QAAQ,WAAW;;;;;;gCAGvD,4BACC,6LAAC;oCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,QAAQ;;wCAC5B,QAAQ,cAAc,EAAE,qBACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,GAAG;;;;;;;;;;;;wCAIjE,QAAQ,cAAc,EAAE,+BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,kCACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,gBAAgB;;;;;;;;;;;;wCAI9E,QAAQ,cAAc,EAAE,gCACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,cAAc;;;;;;;;;;;;wCAI5E,QAAQ,cAAc,EAAE,oCACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,6BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;wCAIzE,QAAQ,cAAc,EAAE,4BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,2BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,SAAS;;;;;;;;;;;;wCAIvE,QAAQ,cAAc,EAAE,oCACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,sCACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,oBAAoB;;;;;;;;;;;;wCAIlF,QAAQ,cAAc,EAAE,0BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,QAAQ;;;;;;;;;;;;wCAItE,QAAQ,cAAc,EAAE,4BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAGxE,QAAQ,cAAc,EAAE,+BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,4BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,+BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,+BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,8BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,YAAY;;;;;;;;;;;;wCAG1E,QAAQ,cAAc,EAAE,6BACvB,6LAAC;4CAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,6LAAC;oDAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtF,6LAAC;gBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,6LAAC;wBACC,WAAW,GAAG,8LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,SAAS,8LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC3C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,SAAS,MAAM;;;;;;;;;;;;oBAIrC,kBAAkB,wBACjB,6LAAC;wBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,6LAAC;4BAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,WAAW;sCAC/B,wCACC,6LAAC;gCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,eAAe;;kDACpC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;wCAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,aAAa;;4CACjC,QAAQ,mBAAmB,EAAE,sBAC5B,6LAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,IAAI;gDACtC,QAAO;gDACP,KAAI;gDACJ,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qCAC5B,6LAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,mBAAmB;gDACrD,QAAO;gDACP,KAAI;gDACJ,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qBAC5B,6LAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,GAAG;gDACrC,QAAO;gDACP,KAAI;gDACJ,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,6LAAC;gBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,6LAAC;wBAAK,WAAW,8LAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCACpC,6LAAC;wBAAI,WAAW,8LAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,6LAAC;gCAAO,WAAW,8LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAIZ,6LAAC;gCAAO,WAAW,8LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GAtXM;KAAA;uCAwXS", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/PatinaSelector/patinaSelector.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"patinaColor\": \"patinaSelector-module__M14LiG__patinaColor\",\n  \"patinaGrid\": \"patinaSelector-module__M14LiG__patinaGrid\",\n  \"patinaName\": \"patinaSelector-module__M14LiG__patinaName\",\n  \"patinaNote\": \"patinaSelector-module__M14LiG__patinaNote\",\n  \"patinaOption\": \"patinaSelector-module__M14LiG__patinaOption\",\n  \"patinaSelector\": \"patinaSelector-module__M14LiG__patinaSelector\",\n  \"selected\": \"patinaSelector-module__M14LiG__selected\",\n  \"selectedPatina\": \"patinaSelector-module__M14LiG__selectedPatina\",\n  \"selectorHeader\": \"patinaSelector-module__M14LiG__selectorHeader\",\n  \"selectorTitle\": \"patinaSelector-module__M14LiG__selectorTitle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/PatinaSelector/PatinaSelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styles from './patinaSelector.module.css';\r\n\r\ninterface PatinaOption {\r\n  name: string;\r\n  color: string;\r\n  description?: string;\r\n}\r\n\r\ninterface PatinaSelectorProps {\r\n  selectedPatina: string;\r\n  onPatinaChange: (patina: string) => void;\r\n}\r\n\r\nconst PatinaSelector: React.FC<PatinaSelectorProps> = ({ \r\n  selectedPatina, \r\n  onPatinaChange \r\n}) => {\r\n  // Patina options based on the reference design\r\n  const patinaOptions: PatinaOption[] = [\r\n    { name: 'Alpine Stone', color: '#D4C4A8', description: 'Light cream stone finish' },\r\n    { name: 'Aged Stone', color: '#B8A082', description: 'Weathered natural stone' },\r\n    { name: 'Charcoal', color: '#5A5A5A', description: 'Dark charcoal finish' },\r\n    { name: 'Limestone', color: '#E6DCC6', description: 'Classic limestone color' },\r\n    { name: 'Sandstone', color: '#C9B299', description: 'Warm sandstone tone' },\r\n    { name: 'Slate Gray', color: '#708090', description: 'Cool slate gray' },\r\n    { name: 'Terra Cotta', color: '#B87333', description: 'Earthy terra cotta' },\r\n    { name: 'Antique White', color: '#F5F5DC', description: 'Soft antique white' },\r\n    { name: 'Weathered Bronze', color: '#8B7355', description: 'Bronze patina finish' },\r\n    { name: 'Natural Stone', color: '#A0A0A0', description: 'Natural stone gray' },\r\n    { name: 'Moss Green', color: '#8FBC8F', description: 'Subtle moss green' },\r\n    { name: 'Rust', color: '#B7410E', description: 'Oxidized rust finish' }\r\n  ];\r\n\r\n  return (\r\n    <div className={styles.patinaSelector}>\r\n      <div className={styles.selectorHeader}>\r\n        <h3 className={styles.selectorTitle}>Select Patina</h3>\r\n        <span className={styles.selectedPatina}>{selectedPatina}</span>\r\n      </div>\r\n      \r\n      <div className={styles.patinaGrid}>\r\n        {patinaOptions.map((option) => (\r\n          <button\r\n            key={option.name}\r\n            className={`${styles.patinaOption} ${\r\n              selectedPatina === option.name ? styles.selected : ''\r\n            }`}\r\n            onClick={() => onPatinaChange(option.name)}\r\n            title={`${option.name} - ${option.description}`}\r\n            aria-label={`Select ${option.name} patina`}\r\n          >\r\n            <div \r\n              className={styles.patinaColor}\r\n              style={{ backgroundColor: option.color }}\r\n            />\r\n            <span className={styles.patinaName}>{option.name}</span>\r\n          </button>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className={styles.patinaNote}>\r\n        <p>\r\n          <strong>Note:</strong> Patina colors are representative. Actual finish may vary \r\n          due to the handcrafted nature of cast stone. Contact us for physical samples.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PatinaSelector;\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAgBA,MAAM,iBAAgD,CAAC,EACrD,cAAc,EACd,cAAc,EACf;IACC,+CAA+C;IAC/C,MAAM,gBAAgC;QACpC;YAAE,MAAM;YAAgB,OAAO;YAAW,aAAa;QAA2B;QAClF;YAAE,MAAM;YAAc,OAAO;YAAW,aAAa;QAA0B;QAC/E;YAAE,MAAM;YAAY,OAAO;YAAW,aAAa;QAAuB;QAC1E;YAAE,MAAM;YAAa,OAAO;YAAW,aAAa;QAA0B;QAC9E;YAAE,MAAM;YAAa,OAAO;YAAW,aAAa;QAAsB;QAC1E;YAAE,MAAM;YAAc,OAAO;YAAW,aAAa;QAAkB;QACvE;YAAE,MAAM;YAAe,OAAO;YAAW,aAAa;QAAqB;QAC3E;YAAE,MAAM;YAAiB,OAAO;YAAW,aAAa;QAAqB;QAC7E;YAAE,MAAM;YAAoB,OAAO;YAAW,aAAa;QAAuB;QAClF;YAAE,MAAM;YAAiB,OAAO;YAAW,aAAa;QAAqB;QAC7E;YAAE,MAAM;YAAc,OAAO;YAAW,aAAa;QAAoB;QACzE;YAAE,MAAM;YAAQ,OAAO;YAAW,aAAa;QAAuB;KACvE;IAED,qBACE,6LAAC;QAAI,WAAW,gLAAA,CAAA,UAAM,CAAC,cAAc;;0BACnC,6LAAC;gBAAI,WAAW,gLAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAG,WAAW,gLAAA,CAAA,UAAM,CAAC,aAAa;kCAAE;;;;;;kCACrC,6LAAC;wBAAK,WAAW,gLAAA,CAAA,UAAM,CAAC,cAAc;kCAAG;;;;;;;;;;;;0BAG3C,6LAAC;gBAAI,WAAW,gLAAA,CAAA,UAAM,CAAC,UAAU;0BAC9B,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;wBAEC,WAAW,GAAG,gLAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EACjC,mBAAmB,OAAO,IAAI,GAAG,gLAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IACnD;wBACF,SAAS,IAAM,eAAe,OAAO,IAAI;wBACzC,OAAO,GAAG,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,WAAW,EAAE;wBAC/C,cAAY,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC;;0CAE1C,6LAAC;gCACC,WAAW,gLAAA,CAAA,UAAM,CAAC,WAAW;gCAC7B,OAAO;oCAAE,iBAAiB,OAAO,KAAK;gCAAC;;;;;;0CAEzC,6LAAC;gCAAK,WAAW,gLAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,OAAO,IAAI;;;;;;;uBAZ3C,OAAO,IAAI;;;;;;;;;;0BAiBtB,6LAAC;gBAAI,WAAW,gLAAA,CAAA,UAAM,CAAC,UAAU;0BAC/B,cAAA,6LAAC;;sCACC,6LAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAMhC;KAvDM;uCAyDS", "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"availability\": \"relatedProducts-module__zj5_XW__availability\",\n  \"disabled\": \"relatedProducts-module__zj5_XW__disabled\",\n  \"imageContainer\": \"relatedProducts-module__zj5_XW__imageContainer\",\n  \"inStock\": \"relatedProducts-module__zj5_XW__inStock\",\n  \"outOfStock\": \"relatedProducts-module__zj5_XW__outOfStock\",\n  \"outOfStockOverlay\": \"relatedProducts-module__zj5_XW__outOfStockOverlay\",\n  \"price\": \"relatedProducts-module__zj5_XW__price\",\n  \"priceContainer\": \"relatedProducts-module__zj5_XW__priceContainer\",\n  \"productCard\": \"relatedProducts-module__zj5_XW__productCard\",\n  \"productCode\": \"relatedProducts-module__zj5_XW__productCode\",\n  \"productDetails\": \"relatedProducts-module__zj5_XW__productDetails\",\n  \"productImage\": \"relatedProducts-module__zj5_XW__productImage\",\n  \"productInfo\": \"relatedProducts-module__zj5_XW__productInfo\",\n  \"productLink\": \"relatedProducts-module__zj5_XW__productLink\",\n  \"productName\": \"relatedProducts-module__zj5_XW__productName\",\n  \"productsContainer\": \"relatedProducts-module__zj5_XW__productsContainer\",\n  \"productsGrid\": \"relatedProducts-module__zj5_XW__productsGrid\",\n  \"relatedProducts\": \"relatedProducts-module__zj5_XW__relatedProducts\",\n  \"scrollButton\": \"relatedProducts-module__zj5_XW__scrollButton\",\n  \"scrollControls\": \"relatedProducts-module__zj5_XW__scrollControls\",\n  \"sectionHeader\": \"relatedProducts-module__zj5_XW__sectionHeader\",\n  \"sectionTitle\": \"relatedProducts-module__zj5_XW__sectionTitle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/RelatedProducts/RelatedProducts.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { Product } from '@/services/types/entities';\r\nimport styles from './relatedProducts.module.css';\r\n\r\ninterface RelatedProductsProps {\r\n  products: Product[];\r\n}\r\n\r\nconst RelatedProducts: React.FC<RelatedProductsProps> = ({ products }) => {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\r\n  const [canScrollRight, setCanScrollRight] = useState(true);\r\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(price);\r\n  };\r\n\r\n  const updateScrollButtons = () => {\r\n    if (scrollContainerRef.current) {\r\n      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;\r\n      setCanScrollLeft(scrollLeft > 0);\r\n      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);\r\n    }\r\n  };\r\n\r\n  const scrollLeft = () => {\r\n    if (scrollContainerRef.current) {\r\n      const cardWidth = 320; // Card width + gap\r\n      scrollContainerRef.current.scrollBy({ left: -cardWidth, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (scrollContainerRef.current) {\r\n      const cardWidth = 320; // Card width + gap\r\n      scrollContainerRef.current.scrollBy({ left: cardWidth, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const scrollContainer = scrollContainerRef.current;\r\n    if (scrollContainer) {\r\n      updateScrollButtons();\r\n      scrollContainer.addEventListener('scroll', updateScrollButtons);\r\n      return () => scrollContainer.removeEventListener('scroll', updateScrollButtons);\r\n    }\r\n  }, [products]);\r\n\r\n  if (!products || products.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={styles.relatedProducts}>\r\n      <div className={styles.sectionHeader}>\r\n        <h2 className={styles.sectionTitle}>You May Also Like</h2>\r\n        \r\n        {products.length > 3 && (\r\n          <div className={styles.scrollControls}>\r\n            <button\r\n              className={`${styles.scrollButton} ${!canScrollLeft ? styles.disabled : ''}`}\r\n              onClick={scrollLeft}\r\n              disabled={!canScrollLeft}\r\n              aria-label=\"Scroll left\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M15 18L9 12L15 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n            \r\n            <button\r\n              className={`${styles.scrollButton} ${!canScrollRight ? styles.disabled : ''}`}\r\n              onClick={scrollRight}\r\n              disabled={!canScrollRight}\r\n              aria-label=\"Scroll right\"\r\n            >\r\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                <path \r\n                  d=\"M9 18L15 12L9 6\" \r\n                  stroke=\"currentColor\" \r\n                  strokeWidth=\"2\" \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div \r\n        className={styles.productsContainer}\r\n        ref={scrollContainerRef}\r\n      >\r\n        <div className={styles.productsGrid}>\r\n          {products.map((product) => {\r\n            const mainImage = product.images && product.images.length > 0 \r\n              ? product.images[0] \r\n              : '/images/placeholder-product.jpg';\r\n            \r\n            const isInStock = product.stock > 0;\r\n\r\n            return (\r\n              <div key={product.id} className={styles.productCard}>\r\n                <Link href={`/products/${product.id}`} className={styles.productLink}>\r\n                  <div className={styles.imageContainer}>\r\n                    <img\r\n                      src={mainImage}\r\n                      alt={product.name}\r\n                      className={styles.productImage}\r\n                    />\r\n                    {!isInStock && (\r\n                      <div className={styles.outOfStockOverlay}>\r\n                        <span>Out of Stock</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  \r\n                  <div className={styles.productInfo}>\r\n                    <h3 className={styles.productName}>{product.name}</h3>\r\n                    \r\n                    <div className={styles.productDetails}>\r\n                      <span className={styles.productCode}>\r\n                        P-{product.id.toString().padStart(3, '0')}-AS\r\n                      </span>\r\n                      \r\n                      <div className={styles.availability}>\r\n                        {isInStock ? (\r\n                          <span className={styles.inStock}>\r\n                            Available in 14 Colors And 3 sizes\r\n                          </span>\r\n                        ) : (\r\n                          <span className={styles.outOfStock}>\r\n                            Currently Out of Stock\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className={styles.priceContainer}>\r\n                      <span className={styles.price}>{formatPrice(product.price)}</span>\r\n                    </div>\r\n                  </div>\r\n                </Link>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RelatedProducts;\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,4CAA4C;;;;AAG5C;AACA;AAEA;;;AALA;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,sBAAsB;QAC1B,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,mBAAmB,OAAO;YAC3E,iBAAiB,aAAa;YAC9B,kBAAkB,aAAa,cAAc,cAAc;QAC7D;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM,CAAC;gBAAW,UAAU;YAAS;QAC7E;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAW,UAAU;YAAS;QAC5E;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,kBAAkB,mBAAmB,OAAO;YAClD,IAAI,iBAAiB;gBACnB;gBACA,gBAAgB,gBAAgB,CAAC,UAAU;gBAC3C;iDAAO,IAAM,gBAAgB,mBAAmB,CAAC,UAAU;;YAC7D;QACF;oCAAG;QAAC;KAAS;IAEb,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,eAAe;;0BACpC,6LAAC;gBAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,aAAa;;kCAClC,6LAAC;wBAAG,WAAW,kLAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;oBAEnC,SAAS,MAAM,GAAG,mBACjB,6LAAC;wBAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,6LAAC;gCACC,WAAW,GAAG,kLAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,gBAAgB,kLAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC5E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCACC,WAAW,GAAG,kLAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,iBAAiB,kLAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC7E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,6LAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,6LAAC;gBACC,WAAW,kLAAA,CAAA,UAAM,CAAC,iBAAiB;gBACnC,KAAK;0BAEL,cAAA,6LAAC;oBAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,YAAY;8BAChC,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;wBAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;wBAElC,qBACE,6LAAC;4BAAqB,WAAW,kLAAA,CAAA,UAAM,CAAC,WAAW;sCACjD,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,kLAAA,CAAA,UAAM,CAAC,WAAW;;kDAClE,6LAAC;wCAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,6LAAC;gDACC,KAAK;gDACL,KAAK,QAAQ,IAAI;gDACjB,WAAW,kLAAA,CAAA,UAAM,CAAC,YAAY;;;;;;4CAE/B,CAAC,2BACA,6LAAC;gDAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,iBAAiB;0DACtC,cAAA,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,6LAAC;gDAAG,WAAW,kLAAA,CAAA,UAAM,CAAC,WAAW;0DAAG,QAAQ,IAAI;;;;;;0DAEhD,6LAAC;gDAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,cAAc;;kEACnC,6LAAC;wDAAK,WAAW,kLAAA,CAAA,UAAM,CAAC,WAAW;;4DAAE;4DAChC,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG;4DAAK;;;;;;;kEAG5C,6LAAC;wDAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,YAAY;kEAChC,0BACC,6LAAC;4DAAK,WAAW,kLAAA,CAAA,UAAM,CAAC,OAAO;sEAAE;;;;;iFAIjC,6LAAC;4DAAK,WAAW,kLAAA,CAAA,UAAM,CAAC,UAAU;sEAAE;;;;;;;;;;;;;;;;;0DAO1C,6LAAC;gDAAI,WAAW,kLAAA,CAAA,UAAM,CAAC,cAAc;0DACnC,cAAA,6LAAC;oDAAK,WAAW,kLAAA,CAAA,UAAM,CAAC,KAAK;8DAAG,YAAY,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;2BArCvD,QAAQ,EAAE;;;;;oBA2CxB;;;;;;;;;;;;;;;;;AAKV;GAzJM;KAAA;uCA2JS", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/[id]/productPage.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"addToCartBtn\": \"productPage-module__Rcy5XW__addToCartBtn\",\n  \"addToCartLabel\": \"productPage-module__Rcy5XW__addToCartLabel\",\n  \"addToCartRow\": \"productPage-module__Rcy5XW__addToCartRow\",\n  \"addToCartWrapper\": \"productPage-module__Rcy5XW__addToCartWrapper\",\n  \"container\": \"productPage-module__Rcy5XW__container\",\n  \"detailsSection\": \"productPage-module__Rcy5XW__detailsSection\",\n  \"errorContainer\": \"productPage-module__Rcy5XW__errorContainer\",\n  \"imageSection\": \"productPage-module__Rcy5XW__imageSection\",\n  \"infoLabel\": \"productPage-module__Rcy5XW__infoLabel\",\n  \"infoRow\": \"productPage-module__Rcy5XW__infoRow\",\n  \"infoValue\": \"productPage-module__Rcy5XW__infoValue\",\n  \"keySpecsTable\": \"productPage-module__Rcy5XW__keySpecsTable\",\n  \"label\": \"productPage-module__Rcy5XW__label\",\n  \"loadingContainer\": \"productPage-module__Rcy5XW__loadingContainer\",\n  \"loadingSpinner\": \"productPage-module__Rcy5XW__loadingSpinner\",\n  \"price\": \"productPage-module__Rcy5XW__price\",\n  \"priceRow\": \"productPage-module__Rcy5XW__priceRow\",\n  \"priceSection\": \"productPage-module__Rcy5XW__priceSection\",\n  \"productCode\": \"productPage-module__Rcy5XW__productCode\",\n  \"productInfo\": \"productPage-module__Rcy5XW__productInfo\",\n  \"productMain\": \"productPage-module__Rcy5XW__productMain\",\n  \"productPage\": \"productPage-module__Rcy5XW__productPage\",\n  \"productTitle\": \"productPage-module__Rcy5XW__productTitle\",\n  \"purchaseSection\": \"productPage-module__Rcy5XW__purchaseSection\",\n  \"quantityBtn\": \"productPage-module__Rcy5XW__quantityBtn\",\n  \"quantityControls\": \"productPage-module__Rcy5XW__quantityControls\",\n  \"quantityInput\": \"productPage-module__Rcy5XW__quantityInput\",\n  \"quantitySelector\": \"productPage-module__Rcy5XW__quantitySelector\",\n  \"rightSection\": \"productPage-module__Rcy5XW__rightSection\",\n  \"specRow\": \"productPage-module__Rcy5XW__specRow\",\n  \"spin\": \"productPage-module__Rcy5XW__spin\",\n  \"value\": \"productPage-module__Rcy5XW__value\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/products/%5Bid%5D/page.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'next/navigation';\r\nimport { Product } from '@/services/types/entities';\r\nimport { productService } from '@/services';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport ProductImageGallery from '@/components/products/ProductImageGallery/ProductImageGallery';\r\nimport ProductSpecifications from '@/components/products/ProductSpecifications/ProductSpecifications';\r\nimport PatinaSelector from '@/components/products/PatinaSelector/PatinaSelector';\r\nimport RelatedProducts from '@/components/products/RelatedProducts/RelatedProducts';\r\nimport styles from './productPage.module.css';\r\n\r\nexport default function ProductPage() {\r\n  const params = useParams();\r\n  const productId = parseInt(params.id as string);\r\n  const { addToCart } = useCart();\r\n\r\n  const [product, setProduct] = useState<Product | null>(null);\r\n  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [quantity, setQuantity] = useState(1);\r\n  const [selectedPatina, setSelectedPatina] = useState<string>('Alpine Stone');\r\n  const [isAddingToCart, setIsAddingToCart] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (productId) {\r\n      fetchProductData();\r\n    }\r\n  }, [productId]);\r\n\r\n  const fetchProductData = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      const [productData, relatedData] = await Promise.all([\r\n        productService.get.getById(productId),\r\n        productService.get.getRecommendations(productId, 6)\r\n      ]);\r\n\r\n      setProduct(productData);\r\n      setRelatedProducts(relatedData);\r\n    } catch (err) {\r\n      console.error('Error fetching product:', err);\r\n      setError('Failed to load product details');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAddToCart = async () => {\r\n    if (!product) return;\r\n\r\n    try {\r\n      setIsAddingToCart(true);\r\n      await addToCart(product.id, quantity);\r\n      // You could add a success notification here\r\n    } catch (err) {\r\n      console.error('Error adding to cart:', err);\r\n      // You could add an error notification here\r\n    } finally {\r\n      setIsAddingToCart(false);\r\n    }\r\n  };\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD'\r\n    }).format(price);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={styles.loadingContainer}>\r\n        <div className={styles.loadingSpinner}></div>\r\n        <p>Loading product details...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !product) {\r\n    return (\r\n      <div className={styles.errorContainer}>\r\n        <h1>Product Not Found</h1>\r\n        <p>{error || 'The requested product could not be found.'}</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const isInStock = product.stock > 0;\r\n\r\n  return (\r\n    <div className={styles.productPage}>\r\n      <div className={styles.container}>\r\n        {/* Main Product Section */}\r\n        <div className={styles.productMain}>\r\n          {/* Product Image Gallery */}\r\n          <div className={styles.imageSection}>\r\n            <ProductImageGallery \r\n              images={product.images} \r\n              productName={product.name}\r\n            />\r\n          </div>\r\n\r\n          {/* Product Details */}\r\n          <div className={styles.detailsSection}>\r\n            <h1 className={styles.productTitle}>{product.name}</h1>\r\n            \r\n            {/* Product Code */}\r\n            <div className={styles.productCode}>\r\n              Product Code: {product.productCode || `P-${product.id.toString().padStart(3, '0')}-AS`}\r\n            </div>\r\n\r\n            {/* Key Specifications - Styled Table Look */}\r\n            {product.productSpecifications && (\r\n              <>{console.log(\"Full productSpecifications object:\", product.productSpecifications)}\r\n              <div className={styles.keySpecsTable}>\r\n                {product.productSpecifications.pieces && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Pieces:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.pieces}</span>\r\n                  </div>\r\n                )}\r\n                {product.productSpecifications.material && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Material:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.material}</span>\r\n                  </div>\r\n                )}\r\n                {product.productSpecifications.dimensions && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Dimensions:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.dimensions}</span>\r\n                  </div>\r\n                )}\r\n                {product.productSpecifications.totalWeight && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Total Weight:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.totalWeight}</span>\r\n                  </div>\r\n                )}\r\n                {product.productSpecifications.photographed_In && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Photographed In:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.photographed_In}</span>\r\n                  </div>\r\n                )}\r\n                {product.productSpecifications.base_Dimensions && (\r\n                  <div className={styles.specRow}>\r\n                    <span className={styles.label}>Base Dimensions:</span>\r\n                    <span className={styles.value}>{product.productSpecifications.base_Dimensions}</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              </>\r\n            )}\r\n\r\n            {/* Product Info Grid */}\r\n             <div className={styles.productInfo}>\r\n              <div className={styles.infoRow}>\r\n                <span className={styles.infoLabel}>Availability:</span>\r\n                <span className={styles.infoValue}>\r\n                  {isInStock ? \r\n                    `In Stock` : \r\n                    'Out of Stock'\r\n                  }\r\n                </span>\r\n              </div> \r\n              </div>\r\n               \r\n                         \r\n\r\n\r\n            <div className={styles.priceSection}>\r\n                <div className={styles.priceRow}>\r\n    \r\n              <span className={styles.price}>\r\n                {formatPrice(product.price)}</span>\r\n            \r\n\r\n            {/* Quantity and Add to Cart */}\r\n              {/* <div className={styles.purchaseSection}> */}\r\n              <span><span></span></span>\r\n                <div className={styles.quantitySelector}>\r\n                  <label htmlFor=\"quantity\">Quantity:</label>\r\n                  <div className={styles.quantityControls}>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setQuantity(Math.max(1, quantity - 1))}\r\n                      disabled={quantity <= 1}\r\n                      className={styles.quantityBtn}\r\n                    >\r\n                      -\r\n                    </button>\r\n                    <input\r\n                      id=\"quantity\"\r\n                      type=\"number\"\r\n                      value={quantity}\r\n                      onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}\r\n                      min=\"1\"\r\n                      max={product.stock}\r\n                      className={styles.quantityInput}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}\r\n                      disabled={quantity >= product.stock}\r\n                      className={styles.quantityBtn}\r\n                    >\r\n                      +\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                \r\n              <div className={styles.addToCartRow}>\r\n                <div className={styles.addToCartLabel}> \r\n                \r\n                <button\r\n                  onClick={handleAddToCart}\r\n                  disabled={isAddingToCart}\r\n                  className={styles.addToCartBtn}\r\n                >\r\n                  {isAddingToCart ? 'Adding...' : 'Add to Cart'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n            </div>     \r\n            </div>\r\n\r\n              <PatinaSelector \r\n              selectedPatina={selectedPatina}\r\n              onPatinaChange={setSelectedPatina}\r\n            />\r\n\r\n\r\n        {/* Product Specifications */}\r\n        <ProductSpecifications product={product} />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Related Products */}\r\n        <RelatedProducts products={relatedProducts} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAG9C;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,SAAS,OAAO,EAAE;IACpC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB;QACvB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,8JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,OAAO,CAAC;gBAC3B,8JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW;aAClD;YAED,WAAW;YACX,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,kBAAkB;YAClB,MAAM,UAAU,QAAQ,EAAE,EAAE;QAC5B,4CAA4C;QAC9C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,2CAA2C;QAC7C,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,gBAAgB;;8BACrC,6LAAC;oBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,cAAc;;;;;;8BACrC,6LAAC;8BAAE;;;;;;;;;;;;IAGT;IAEA,IAAI,SAAS,CAAC,SAAS;QACrB,qBACE,6LAAC;YAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,6LAAC;8BAAG;;;;;;8BACJ,6LAAC;8BAAG,SAAS;;;;;;;;;;;;IAGnB;IAEA,MAAM,YAAY,QAAQ,KAAK,GAAG;IAElC,qBACE,6LAAC;QAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;kBAChC,cAAA,6LAAC;YAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,SAAS;;8BAE9B,6LAAC;oBAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;;sCAEhC,6LAAC;4BAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;sCACjC,cAAA,6LAAC,+KAAA,CAAA,UAAmB;gCAClB,QAAQ,QAAQ,MAAM;gCACtB,aAAa,QAAQ,IAAI;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,cAAc;;8CACnC,6LAAC;oCAAG,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;8CAAG,QAAQ,IAAI;;;;;;8CAGjD,6LAAC;oCAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;;wCAAE;wCACnB,QAAQ,WAAW,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;;;;;;;gCAIvF,QAAQ,qBAAqB,kBAC5B;;wCAAG,QAAQ,GAAG,CAAC,sCAAsC,QAAQ,qBAAqB;sDAClF,6LAAC;4CAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,aAAa;;gDACjC,QAAQ,qBAAqB,CAAC,MAAM,kBACnC,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,MAAM;;;;;;;;;;;;gDAGvE,QAAQ,qBAAqB,CAAC,QAAQ,kBACrC,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,QAAQ;;;;;;;;;;;;gDAGzE,QAAQ,qBAAqB,CAAC,UAAU,kBACvC,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,UAAU;;;;;;;;;;;;gDAG3E,QAAQ,qBAAqB,CAAC,WAAW,kBACxC,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gDAG5E,QAAQ,qBAAqB,CAAC,eAAe,kBAC5C,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gDAGhF,QAAQ,qBAAqB,CAAC,eAAe,kBAC5C,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;sEAC5B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAE;;;;;;sEAC/B,6LAAC;4DAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;sEAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;8CAQpF,6LAAC;oCAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;8CACjC,cAAA,6LAAC;wCAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,6LAAC;gDAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,SAAS;0DAAE;;;;;;0DACnC,6LAAC;gDAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,SAAS;0DAC9B,YACC,CAAC,QAAQ,CAAC,GACV;;;;;;;;;;;;;;;;;8CASR,6LAAC;oCAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;8CAC/B,cAAA,6LAAC;wCAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,QAAQ;;0DAEjC,6LAAC;gDAAK,WAAW,8JAAA,CAAA,UAAM,CAAC,KAAK;0DAC1B,YAAY,QAAQ,KAAK;;;;;;0DAK5B,6LAAC;0DAAK,cAAA,6LAAC;;;;;;;;;;0DACL,6LAAC;gDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,gBAAgB;;kEACrC,6LAAC;wDAAM,SAAQ;kEAAW;;;;;;kEAC1B,6LAAC;wDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,gBAAgB;;0EACrC,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;gEAClD,UAAU,YAAY;gEACtB,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;0EAC9B;;;;;;0EAGD,6LAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACrE,KAAI;gEACJ,KAAK,QAAQ,KAAK;gEAClB,WAAW,8JAAA,CAAA,UAAM,CAAC,aAAa;;;;;;0EAEjC,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,WAAW;gEAC9D,UAAU,YAAY,QAAQ,KAAK;gEACnC,WAAW,8JAAA,CAAA,UAAM,CAAC,WAAW;0EAC9B;;;;;;;;;;;;;;;;;;0DAMP,6LAAC;gDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;0DACjC,cAAA,6LAAC;oDAAI,WAAW,8JAAA,CAAA,UAAM,CAAC,cAAc;8DAErC,cAAA,6LAAC;wDACC,SAAS;wDACT,UAAU;wDACV,WAAW,8JAAA,CAAA,UAAM,CAAC,YAAY;kEAE7B,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOpC,6LAAC,qKAAA,CAAA,UAAc;oCACf,gBAAgB;oCAChB,gBAAgB;;;;;;8CAKtB,6LAAC,mLAAA,CAAA,UAAqB;oCAAC,SAAS;;;;;;;;;;;;;;;;;;8BAKhC,6LAAC,uKAAA,CAAA,UAAe;oBAAC,UAAU;;;;;;;;;;;;;;;;;AAInC;GA3OwB;;QACP,qIAAA,CAAA,YAAS;QAEF,kIAAA,CAAA,UAAO;;;KAHP", "debugId": null}}]}