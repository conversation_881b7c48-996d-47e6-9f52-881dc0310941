{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/retail-locator/retailLocator.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"comingSoon\": \"retailLocator-module__o1q4Zq__comingSoon\",\n  \"contactDetails\": \"retailLocator-module__o1q4Zq__contactDetails\",\n  \"contactInfo\": \"retailLocator-module__o1q4Zq__contactInfo\",\n  \"contactItem\": \"retailLocator-module__o1q4Zq__contactItem\",\n  \"container\": \"retailLocator-module__o1q4Zq__container\",\n  \"content\": \"retailLocator-module__o1q4Zq__content\",\n  \"feature\": \"retailLocator-module__o1q4Zq__feature\",\n  \"features\": \"retailLocator-module__o1q4Zq__features\",\n  \"header\": \"retailLocator-module__o1q4Zq__header\",\n  \"icon\": \"retailLocator-module__o1q4Zq__icon\",\n  \"subtitle\": \"retailLocator-module__o1q4Zq__subtitle\",\n  \"title\": \"retailLocator-module__o1q4Zq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/app/retail-locator/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport styles from './retailLocator.module.css';\r\n\r\nexport default function RetailLocatorPage() {\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.header}>\r\n        <h1 className={styles.title}>Retail Locator</h1>\r\n        <p className={styles.subtitle}>\r\n          Find authorized Cast Stone retailers and showrooms near you\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.content}>\r\n        <div className={styles.comingSoon}>\r\n          <div className={styles.icon}>\r\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n            </svg>\r\n          </div>\r\n          <h2>Store Locator Coming Soon</h2>\r\n          <p>\r\n            We&apos;re building an interactive map to help you find authorized Cast Stone retailers,\r\n            showrooms, and installation partners in your area. This will include detailed\r\n            information about each location, contact details, and available services.\r\n          </p>\r\n          <div className={styles.features}>\r\n            <div className={styles.feature}>\r\n              <h4>Interactive Map</h4>\r\n              <p>Easy-to-use map interface with location search</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Retailer Details</h4>\r\n              <p>Contact information, hours, and available products</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Installation Partners</h4>\r\n              <p>Find certified installation professionals near you</p>\r\n            </div>\r\n          </div>\r\n          <div className={styles.contactInfo}>\r\n            <h3>Need Help Now?</h3>\r\n            <p>Contact us directly for retailer recommendations in your area:</p>\r\n            <div className={styles.contactDetails}>\r\n              <div className={styles.contactItem}>\r\n                <strong>Phone:</strong> 1-800-CAST-STONE\r\n              </div>\r\n              <div className={styles.contactItem}>\r\n                <strong>Email:</strong> <EMAIL>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,6LAAC;gBAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAG,WAAW,+JAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,6LAAC;wBAAE,WAAW,+JAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,OAAO;0BAC5B,cAAA,6LAAC;oBAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,6LAAC;4BAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,IAAI;sCACzB,cAAA,6LAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;gCAAO,QAAO;;kDACjE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;kDACrE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;sCAGzE,6LAAC;sCAAG;;;;;;sCACJ,6LAAC;sCAAE;;;;;;sCAKH,6LAAC;4BAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,QAAQ;;8CAC7B,6LAAC;oCAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;8CAEL,6LAAC;oCAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;8CAEL,6LAAC;oCAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGP,6LAAC;4BAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,WAAW;;8CAChC,6LAAC;8CAAG;;;;;;8CACJ,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,cAAc;;sDACnC,6LAAC;4CAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,WAAW;;8DAChC,6LAAC;8DAAO;;;;;;gDAAe;;;;;;;sDAEzB,6LAAC;4CAAI,WAAW,+JAAA,CAAA,UAAM,CAAC,WAAW;;8DAChC,6LAAC;8DAAO;;;;;;gDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;KAtDwB", "debugId": null}}]}