{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/collections/[id]/collectionPage.module.css"], "sourcesContent": ["/* Collection Page - Sharp Rectangular Design */\r\n.collectionPage {\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 6rem 2rem 2rem;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 60vh;\r\n  color: #1e40af;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #1e40af;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #1e40af;\r\n}\r\n\r\n.errorContainer h1 {\r\n  font-size: 2rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Collection Header */\r\n.collectionHeader {\r\n  margin-bottom: 3rem;\r\n  padding: 2rem;\r\n  background: #f8f8f8;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.headerContent {\r\n  text-align: center;\r\n}\r\n\r\n.collectionTitle {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.collectionDescription {\r\n  font-size: 1.2rem;\r\n  color: #666;\r\n  margin-bottom: 1.5rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.collectionMeta {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.productCount {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  background: white;\r\n  padding: 0.5rem 1rem;\r\n  border: 2px solid #1e40af;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.collectionLevel {\r\n  font-weight: 600;\r\n  color: #666;\r\n  background: white;\r\n  padding: 0.5rem 1rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Filter Section */\r\n.filterSection {\r\n  margin-bottom: 2rem;\r\n  background: white;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n}\r\n\r\n.searchBar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.5rem;\r\n  background: #fafafa;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.searchInput {\r\n  flex: 1;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.searchIcon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  color: #666;\r\n  z-index: 1;\r\n}\r\n\r\n.searchField {\r\n  width: 100%;\r\n  padding: 1rem 1rem 1rem 3rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-size: 1rem;\r\n  background: white;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.searchField:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.filterToggle {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: 2px solid #1e40af;\r\n  padding: 1rem 1.5rem;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.filterToggle:hover {\r\n  background: white;\r\n  color: #1e40af;\r\n}\r\n\r\n.filterToggle.active {\r\n  background: #6b4e3d;\r\n}\r\n\r\n/* Advanced Filters */\r\n.advancedFilters {\r\n  padding: 2rem;\r\n  background: white;\r\n  border-top: 1px solid #ddd;\r\n}\r\n\r\n.filterGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 2rem;\r\n  align-items: end;\r\n}\r\n\r\n.filterGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.filterLabel {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.priceRange {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.priceInput {\r\n  flex: 1;\r\n  padding: 0.75rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.priceInput:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.checkboxLabel {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  cursor: pointer;\r\n}\r\n\r\n.checkbox {\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #1e40af;\r\n}\r\n\r\n.sortControls {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.sortSelect {\r\n  flex: 1;\r\n  padding: 0.75rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  background: white;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.sortSelect:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.sortDirection {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: 2px solid #1e40af;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.sortDirection:hover {\r\n  background: white;\r\n  color: #1e40af;\r\n}\r\n\r\n.sortDirection.desc {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.clearFilters {\r\n  background: #dc3545;\r\n  color: white;\r\n  border: 2px solid #dc3545;\r\n  padding: 0.75rem 1rem;\r\n  cursor: pointer;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  width: 100%;\r\n}\r\n\r\n.clearFilters:hover {\r\n  background: white;\r\n  color: #dc3545;\r\n}\r\n\r\n/* Products Section */\r\n.productsSection {\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .collectionTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .filterGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .collectionHeader {\r\n    padding: 1.5rem;\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .collectionTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .collectionDescription {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .collectionMeta {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .searchBar {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .searchInput {\r\n    width: 100%;\r\n  }\r\n  \r\n  .filterToggle {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filterGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .priceRange {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .sortControls {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .collectionTitle {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .advancedFilters {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .searchField {\r\n    padding: 0.75rem 0.75rem 0.75rem 2.5rem;\r\n  }\r\n  \r\n  .filterToggle {\r\n    padding: 0.75rem 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;AAKA;EACE;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA"}}]}