{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/HeroSection/heroSection.module.css"], "sourcesContent": ["/* Hero Section Styles */\r\n.hero {\r\n  position: relative;\r\n  height: 100vh;\r\n  min-height: 700px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  overflow: hidden;\r\n  background-color: #1a1a1a; /* Fallback color */\r\n}\r\n\r\n/* Image Background Carousel */\r\n.imageContainer {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 1;\r\n}\r\n\r\n.imageSlide {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  opacity: 0;\r\n  transition: opacity 1.5s ease-in-out;\r\n  transform: scale(1);\r\n  will-change: transform, opacity;\r\n}\r\n\r\n.imageSlide.active {\r\n  opacity: 1;\r\n  animation: smoothZoom 5s ease-out forwards;\r\n}\r\n\r\n.imageSlide:not(.active) {\r\n  animation: none;\r\n  transform: scale(1.06); /* Keep zoomed state when inactive */\r\n}\r\n\r\n/* Smooth zoom animation without reset */\r\n@keyframes smoothZoom {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  100% {\r\n    transform: scale(1.06);\r\n  }\r\n}\r\n\r\n.backgroundImage {\r\n  object-fit: cover;\r\n  object-position: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(\r\n    135deg,\r\n    rgba(0, 0, 0, 0.3) 0%,\r\n    rgba(0, 0, 0, 0.2) 50%,\r\n    rgba(0, 0, 0, 0.4) 100%\r\n  );\r\n  z-index: 2;\r\n}\r\n\r\n/* Content Styles */\r\n.container {\r\n  position: relative;\r\n  z-index: 3;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n  text-align: center;\r\n}\r\n\r\n.content {\r\n  max-width: 900px;\r\n  margin: 0 auto;\r\n  padding-top: 2rem;\r\n}\r\n\r\n.title {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 4.5rem;\r\n  font-weight: 300;\r\n  line-height: 1.1;\r\n  margin-bottom: 2rem;\r\n  color: #ffffff;\r\n  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);\r\n  letter-spacing: 0.02em;\r\n}\r\n\r\n.titleLine1,\r\n.titleLine2 {\r\n  display: block;\r\n  animation: fadeInUp 1s ease-out forwards;\r\n}\r\n\r\n.titleLine1 {\r\n  animation-delay: 0.3s;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.titleLine2 {\r\n  animation-delay: 0.6s;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 300;\r\n  line-height: 1.7;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  margin-bottom: 3rem;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);\r\n  animation: fadeInUp 1s ease-out 0.9s forwards;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n/* Button Styles */\r\n.actions {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n  animation: fadeInUp 1s ease-out 1.2s forwards;\r\n  opacity: 0;\r\n  transform: translateY(30px);\r\n}\r\n\r\n.primaryButton,\r\n.secondaryButton {\r\n  position: relative;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 1.2rem 3rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  letter-spacing: 0.15em;\r\n  text-transform: uppercase;\r\n  text-decoration: none;\r\n  border-radius: 0;\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  border: 2px solid transparent;\r\n  min-width: 220px;\r\n}\r\n\r\n.primaryButton {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: rgba(0, 0, 0, 0.8);\r\n  border-color: rgba(255, 255, 255, 0.9);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.primaryButton:hover {\r\n  background: rgba(255, 255, 255, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.secondaryButton {\r\n  background: transparent;\r\n  color: #ffffff;\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.secondaryButton:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 1);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.buttonText {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.buttonRipple {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);\r\n  transform: scale(0);\r\n  transition: transform 0.6s ease-out;\r\n  z-index: 1;\r\n}\r\n\r\n.primaryButton:active .buttonRipple,\r\n.secondaryButton:active .buttonRipple {\r\n  transform: scale(1);\r\n}\r\n\r\n/* Scroll Indicator */\r\n.scrollIndicator {\r\n  position: absolute;\r\n  bottom: 2rem;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  z-index: 3;\r\n  animation: fadeInUp 1s ease-out 1.5s forwards;\r\n  opacity: 0;\r\n}\r\n\r\n.scrollArrow {\r\n  color: #ffffff;\r\n  animation: bounce 2s infinite;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.scrollArrow:hover {\r\n  color: #d4af8c;\r\n}\r\n\r\n/* Animations */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-10px);\r\n  }\r\n  60% {\r\n    transform: translateY(-5px);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .title {\r\n    font-size: 3.5rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 1rem;\r\n    margin-bottom: 2rem;\r\n  }\r\n\r\n  .actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .primaryButton,\r\n  .secondaryButton {\r\n    width: 100%;\r\n    max-width: 280px;\r\n    padding: 0.875rem 2rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero {\r\n    min-height: 500px;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .subtitle {\r\n    font-size: 0.9rem;\r\n    margin-bottom: 1.5rem;\r\n  }\r\n\r\n  .primaryButton,\r\n  .secondaryButton {\r\n    font-size: 0.8rem;\r\n    padding: 0.75rem 1.5rem;\r\n  }\r\n}\r\n\r\n/* Navigation Arrows */\r\n.navArrow {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  /* background: rgba(255, 255, 255, 0.1); */\r\n  /* backdrop-filter: blur(10px);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 0%; */\r\n  width: 60px;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  z-index: 4;\r\n  opacity: 0.7;\r\n}\r\n\r\n.navArrow:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  color: rgba(255, 255, 255, 1);\r\n  opacity: 1;\r\n  transform: translateY(-50%) scale(1.1);\r\n}\r\n\r\n.navArrowLeft {\r\n  left: 2rem;\r\n}\r\n\r\n.navArrowRight {\r\n  right: 2rem;\r\n}\r\n\r\n/* Indicator Dots */\r\n.indicators {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 0.75rem;\r\n  margin-top: 3rem;\r\n  padding: 1rem 0;\r\n}\r\n\r\n.indicator {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n  background: transparent;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.indicator:hover {\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  transform: scale(1.2);\r\n}\r\n\r\n.indicatorActive {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-color: rgba(255, 255, 255, 0.9);\r\n  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n/* Mobile responsive adjustments */\r\n@media (max-width: 768px) {\r\n  .hero {\r\n    min-height: 500px;\r\n  }\r\n\r\n  .navArrow {\r\n    width: 50px;\r\n    height: 50px;\r\n    opacity: 0.6;\r\n  }\r\n\r\n  .navArrowLeft {\r\n    left: 1rem;\r\n  }\r\n\r\n  .navArrowRight {\r\n    right: 1rem;\r\n  }\r\n\r\n  .indicators {\r\n    margin-top: 2rem;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .indicator {\r\n    width: 10px;\r\n    height: 10px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAMA;;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAgBA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;AAMA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;AAKA;;;;;;;;;;;;AAWA;;;;;;;;;;;;;;AAaA;EACE;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAQF;;;;;;;;;;;;;;;;AAoBA;;;;;;;;AAQA;;;;AAIA;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAOA;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}