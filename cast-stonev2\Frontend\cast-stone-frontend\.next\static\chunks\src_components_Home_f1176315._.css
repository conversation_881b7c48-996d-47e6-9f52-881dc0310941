/* [project]/src/components/Home/HeroSection/heroSection.module.css [app-client] (css) */
.heroSection-module__CcYIFG__hero {
  background-color: #1a1a1a;
  justify-content: center;
  align-items: center;
  height: 100vh;
  min-height: 700px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.heroSection-module__CcYIFG__imageContainer {
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.heroSection-module__CcYIFG__imageSlide {
  opacity: 0;
  will-change: transform, opacity;
  width: 100%;
  height: 100%;
  transition: opacity 1.5s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
}

.heroSection-module__CcYIFG__imageSlide.heroSection-module__CcYIFG__active {
  opacity: 1;
  animation: 5s ease-out forwards heroSection-module__CcYIFG__smoothZoom;
}

.heroSection-module__CcYIFG__imageSlide:not(.heroSection-module__CcYIFG__active) {
  animation: none;
  transform: scale(1.06);
}

@keyframes heroSection-module__CcYIFG__smoothZoom {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.06);
  }
}

.heroSection-module__CcYIFG__backgroundImage {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

.heroSection-module__CcYIFG__imageOverlay {
  z-index: 2;
  background: linear-gradient(135deg, #0000004d 0%, #0003 50%, #0006 100%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.heroSection-module__CcYIFG__container {
  z-index: 3;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.heroSection-module__CcYIFG__content {
  max-width: 900px;
  margin: 0 auto;
  padding-top: 2rem;
}

.heroSection-module__CcYIFG__title {
  color: #fff;
  text-shadow: 2px 2px 8px #0000004d;
  letter-spacing: .02em;
  margin-bottom: 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.1;
}

.heroSection-module__CcYIFG__titleLine1, .heroSection-module__CcYIFG__titleLine2 {
  animation: 1s ease-out forwards heroSection-module__CcYIFG__fadeInUp;
  display: block;
}

.heroSection-module__CcYIFG__titleLine1 {
  opacity: 0;
  animation-delay: .3s;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__titleLine2 {
  opacity: 0;
  animation-delay: .6s;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__subtitle {
  color: #ffffffe6;
  text-shadow: 1px 1px 3px #0006;
  opacity: 0;
  max-width: 700px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.3rem;
  font-weight: 300;
  line-height: 1.7;
  animation: 1s ease-out .9s forwards heroSection-module__CcYIFG__fadeInUp;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__actions {
  opacity: 0;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  animation: 1s ease-out 1.2s forwards heroSection-module__CcYIFG__fadeInUp;
  display: flex;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
  letter-spacing: .15em;
  text-transform: uppercase;
  cursor: pointer;
  border: 2px solid #0000;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  min-width: 220px;
  padding: 1.2rem 3rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.heroSection-module__CcYIFG__primaryButton {
  color: #000c;
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border-color: #ffffffe6;
}

.heroSection-module__CcYIFG__primaryButton:hover {
  color: #000;
  background: #fff;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px #0003;
}

.heroSection-module__CcYIFG__secondaryButton {
  color: #fff;
  backdrop-filter: blur(10px);
  background: none;
  border-color: #fffc;
}

.heroSection-module__CcYIFG__secondaryButton:hover {
  background: #ffffff1a;
  border-color: #fff;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px #ffffff1a;
}

.heroSection-module__CcYIFG__buttonText {
  z-index: 2;
  position: relative;
}

.heroSection-module__CcYIFG__buttonRipple {
  z-index: 1;
  background: radial-gradient(circle, #ffffff4d 0%, #0000 70%);
  width: 100%;
  height: 100%;
  transition: transform .6s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0);
}

.heroSection-module__CcYIFG__primaryButton:active .heroSection-module__CcYIFG__buttonRipple, .heroSection-module__CcYIFG__secondaryButton:active .heroSection-module__CcYIFG__buttonRipple {
  transform: scale(1);
}

.heroSection-module__CcYIFG__scrollIndicator {
  z-index: 3;
  opacity: 0;
  animation: 1s ease-out 1.5s forwards heroSection-module__CcYIFG__fadeInUp;
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.heroSection-module__CcYIFG__scrollArrow {
  color: #fff;
  cursor: pointer;
  transition: color .3s;
  animation: 2s infinite heroSection-module__CcYIFG__bounce;
}

.heroSection-module__CcYIFG__scrollArrow:hover {
  color: #d4af8c;
}

@keyframes heroSection-module__CcYIFG__fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heroSection-module__CcYIFG__bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

@media (width <= 1024px) {
  .heroSection-module__CcYIFG__title {
    font-size: 3.5rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    font-size: 1.1rem;
  }

  .heroSection-module__CcYIFG__container {
    padding: 0 1.5rem;
  }
}

@media (width <= 768px) {
  .heroSection-module__CcYIFG__title {
    font-size: 2.5rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    margin-bottom: 2rem;
    font-size: 1rem;
  }

  .heroSection-module__CcYIFG__actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
    width: 100%;
    max-width: 280px;
    padding: .875rem 2rem;
  }

  .heroSection-module__CcYIFG__container {
    padding: 0 1rem;
  }
}

@media (width <= 480px) {
  .heroSection-module__CcYIFG__hero {
    min-height: 500px;
  }

  .heroSection-module__CcYIFG__title {
    margin-bottom: 1rem;
    font-size: 2rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    margin-bottom: 1.5rem;
    font-size: .9rem;
  }

  .heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
    padding: .75rem 1.5rem;
    font-size: .8rem;
  }
}

.heroSection-module__CcYIFG__navArrow {
  color: #fffc;
  cursor: pointer;
  z-index: 4;
  opacity: .7;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.heroSection-module__CcYIFG__navArrow:hover {
  color: #fff;
  opacity: 1;
  background: #fff3;
  border-color: #fff9;
  transform: translateY(-50%)scale(1.1);
}

.heroSection-module__CcYIFG__navArrowLeft {
  left: 2rem;
}

.heroSection-module__CcYIFG__navArrowRight {
  right: 2rem;
}

.heroSection-module__CcYIFG__indicators {
  justify-content: center;
  gap: .75rem;
  margin-top: 3rem;
  padding: 1rem 0;
  display: flex;
}

.heroSection-module__CcYIFG__indicator {
  cursor: pointer;
  background: none;
  border: 2px solid #ffffff80;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
  position: relative;
}

.heroSection-module__CcYIFG__indicator:hover {
  border-color: #fffc;
  transform: scale(1.2);
}

.heroSection-module__CcYIFG__indicatorActive {
  background: #ffffffe6;
  border-color: #ffffffe6;
  box-shadow: 0 0 10px #ffffff80;
}

@media (width <= 768px) {
  .heroSection-module__CcYIFG__hero {
    min-height: 500px;
  }

  .heroSection-module__CcYIFG__navArrow {
    opacity: .6;
    width: 50px;
    height: 50px;
  }

  .heroSection-module__CcYIFG__navArrowLeft {
    left: 1rem;
  }

  .heroSection-module__CcYIFG__navArrowRight {
    right: 1rem;
  }

  .heroSection-module__CcYIFG__indicators {
    gap: .5rem;
    margin-top: 2rem;
  }

  .heroSection-module__CcYIFG__indicator {
    width: 10px;
    height: 10px;
  }
}


/* [project]/src/components/Home/CategoriesSection/categoriesSection.module.css [app-client] (css) */
.categoriesSection-module__fGUJra__categoriesSection {
  background: linear-gradient(135deg, #faf9f7 0%, #fff 100%);
  padding: 6rem 0;
  position: relative;
}

.categoriesSection-module__fGUJra__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.categoriesSection-module__fGUJra__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.categoriesSection-module__fGUJra__sectionTitle {
  color: #1e3a8a;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3rem;
  font-weight: 700;
}

.categoriesSection-module__fGUJra__sectionSubtitle {
  color: #364c89;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.categoriesSection-module__fGUJra__grid, .categoriesSection-module__fGUJra__loadingGrid {
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.categoriesSection-module__fGUJra__loadingCard {
  background: #fff;
  border-radius: 16px;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite categoriesSection-module__fGUJra__pulse;
  overflow: hidden;
  box-shadow: 0 4px 20px #1e3a8a14;
}

.categoriesSection-module__fGUJra__loadingImage {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  width: 100%;
  height: 300px;
  animation: 2s infinite categoriesSection-module__fGUJra__shimmer;
}

.categoriesSection-module__fGUJra__loadingContent {
  padding: 2rem;
}

.categoriesSection-module__fGUJra__loadingText {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  border-radius: 4px;
  height: 1rem;
  margin-bottom: .75rem;
  animation: 2s infinite categoriesSection-module__fGUJra__shimmer;
}

.categoriesSection-module__fGUJra__loadingText:first-child {
  width: 60%;
}

.categoriesSection-module__fGUJra__loadingText:nth-child(2) {
  width: 80%;
}

.categoriesSection-module__fGUJra__loadingText:nth-child(3) {
  width: 40%;
}

@keyframes categoriesSection-module__fGUJra__shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

@keyframes categoriesSection-module__fGUJra__pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .8;
  }
}

.categoriesSection-module__fGUJra__errorMessage {
  text-align: center;
  color: #6b7280;
  padding: 4rem 2rem;
  font-size: 1.125rem;
}

.categoriesSection-module__fGUJra__categoryCard {
  background: #fff;
  border-radius: 16px;
  height: 400px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #2563eb1a;
}

.categoriesSection-module__fGUJra__categoryCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px #2563eb26;
}

.categoriesSection-module__fGUJra__cardLink {
  width: 100%;
  height: 100%;
  color: inherit;
  text-decoration: none;
  display: block;
  position: relative;
}

.categoriesSection-module__fGUJra__imageContainer {
  height: 60%;
  position: relative;
  overflow: hidden;
}

.categoriesSection-module__fGUJra__categoryImage {
  object-fit: cover;
  transition: transform .6s cubic-bezier(.4, 0, .2, 1);
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__categoryImage {
  transform: scale(1.05);
}

.categoriesSection-module__fGUJra__imageOverlay {
  z-index: 1;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__noImagePlaceholder {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.categoriesSection-module__fGUJra__noImageText {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: 1rem;
  font-weight: 500;
}

.categoriesSection-module__fGUJra__cardContent {
  z-index: 2;
  flex-direction: column;
  justify-content: space-between;
  height: 40%;
  padding: 1.5rem;
  display: flex;
  position: relative;
}

.categoriesSection-module__fGUJra__cardHeader {
  margin-bottom: 1rem;
}

.categoriesSection-module__fGUJra__stats {
  align-items: baseline;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.categoriesSection-module__fGUJra__statsNumber {
  color: #1e3a8a;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.categoriesSection-module__fGUJra__statsLabel {
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 500;
}

.categoriesSection-module__fGUJra__categoryTitle {
  color: #1e3a8a;
  margin: .25rem 0;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.categoriesSection-module__fGUJra__categorySubtitle {
  color: #30457e;
  text-transform: uppercase;
  letter-spacing: .15em;
  margin: 0;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
}

.categoriesSection-module__fGUJra__categoryDescription {
  color: #1e3a8a;
  flex-grow: 1;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  line-height: 1.5;
}

.categoriesSection-module__fGUJra__cardActions {
  align-items: center;
  gap: .75rem;
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
  text-transform: uppercase;
  letter-spacing: .05em;
  cursor: pointer;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 600;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
}

.categoriesSection-module__fGUJra__actionButton {
  color: #fff;
  background: #1e3a8a;
  padding: .5rem 1rem;
}

.categoriesSection-module__fGUJra__actionButton:hover {
  background: #1e3a8a;
  transform: translateY(-1px);
}

.categoriesSection-module__fGUJra__secondaryButton {
  color: #334986;
  background: none;
  border: 1px solid #6978a14d;
  padding: .5rem .75rem;
}

.categoriesSection-module__fGUJra__secondaryButton:hover {
  color: #1e3a8a;
  background: #4542991a;
}

.categoriesSection-module__fGUJra__hoverEffect {
  opacity: 0;
  z-index: 1;
  background: linear-gradient(135deg, #2563eb0d 0%, #0000 50%, #1d4ed80d 100%);
  transition: opacity .3s;
  position: absolute;
  inset: 0;
}

.categoriesSection-module__fGUJra__categoryCard:hover .categoriesSection-module__fGUJra__hoverEffect {
  opacity: 1;
}

@media (width <= 1024px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 4rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1.5rem;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2.5rem;
  }

  .categoriesSection-module__fGUJra__grid {
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 350px;
  }
}

@media (width <= 768px) {
  .categoriesSection-module__fGUJra__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 300px;
  }

  .categoriesSection-module__fGUJra__sectionTitle {
    font-size: 2rem;
  }

  .categoriesSection-module__fGUJra__sectionSubtitle {
    font-size: 1rem;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1.25rem;
  }

  .categoriesSection-module__fGUJra__statsNumber {
    font-size: 1.75rem;
  }

  .categoriesSection-module__fGUJra__categoryTitle {
    font-size: 1.25rem;
  }
}

@media (width <= 480px) {
  .categoriesSection-module__fGUJra__categoriesSection {
    padding: 3rem 0;
  }

  .categoriesSection-module__fGUJra__container {
    padding: 0 1rem;
  }

  .categoriesSection-module__fGUJra__header {
    margin-bottom: 2.5rem;
  }

  .categoriesSection-module__fGUJra__categoryCard {
    height: 280px;
  }

  .categoriesSection-module__fGUJra__cardContent {
    padding: 1rem;
  }

  .categoriesSection-module__fGUJra__cardActions {
    flex-direction: column;
    gap: .5rem;
  }

  .categoriesSection-module__fGUJra__actionButton, .categoriesSection-module__fGUJra__secondaryButton {
    justify-content: center;
    width: 100%;
  }
}


/* [project]/src/components/Home/CatalogBanner/catalogBanner.module.css [app-client] (css) */
.catalogBanner-module__6ToAlG__catalogBanner {
  background: #1e3a8a;
  margin: 4rem 0;
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.catalogBanner-module__6ToAlG__backgroundContainer {
  z-index: 1;
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__backgroundImage {
  object-fit: cover;
  object-position: center;
}

.catalogBanner-module__6ToAlG__backgroundOverlay {
  z-index: 2;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__container {
  z-index: 3;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.catalogBanner-module__6ToAlG__content {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.catalogBanner-module__6ToAlG__textContent {
  color: #fff;
}

.catalogBanner-module__6ToAlG__subtitle {
  text-transform: uppercase;
  letter-spacing: .15em;
  color: #white;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.catalogBanner-module__6ToAlG__title {
  color: #fff;
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.catalogBanner-module__6ToAlG__description {
  color: #ffffffe6;
  margin-bottom: 2.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.catalogBanner-module__6ToAlG__features {
  gap: 2rem;
  margin-bottom: 2rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__feature {
  color: #ffffffe6;
  align-items: center;
  gap: .75rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  display: flex;
}

.catalogBanner-module__6ToAlG__featureIcon {
  width: 40px;
  height: 40px;
  color: #white;
  background: #d4af8c33;
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  display: flex;
}

.catalogBanner-module__6ToAlG__ctaContainer {
  flex-direction: column;
  align-items: flex-start;
  gap: 2rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__ctaButton {
  background: linear-gradient(135deg, #white, #f5f4f3);
  color: #white;
  text-transform: uppercase;
  letter-spacing: .05em;
  border-radius: 50px;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem 2.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #8e8b894d;
}

.catalogBanner-module__6ToAlG__ctaButton:hover {
  background: linear-gradient(135deg, #white, #white);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px #d4af8c66;
}

.catalogBanner-module__6ToAlG__ctaText {
  z-index: 2;
  position: relative;
}

.catalogBanner-module__6ToAlG__ctaIcon {
  z-index: 2;
  transition: transform .3s;
  position: relative;
}

.catalogBanner-module__6ToAlG__ctaButton:hover .catalogBanner-module__6ToAlG__ctaIcon {
  transform: translateX(4px);
}

.catalogBanner-module__6ToAlG__buttonRipple {
  z-index: 1;
  background: radial-gradient(circle, #ffffff4d 0%, #0000 70%);
  width: 100%;
  height: 100%;
  transition: transform .6s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0);
}

.catalogBanner-module__6ToAlG__ctaButton:active .catalogBanner-module__6ToAlG__buttonRipple {
  transform: scale(1);
}

.catalogBanner-module__6ToAlG__catalogStats {
  gap: 3rem;
  display: flex;
}

.catalogBanner-module__6ToAlG__stat {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.catalogBanner-module__6ToAlG__statNumber {
  color: #white;
  margin-bottom: .25rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}

.catalogBanner-module__6ToAlG__statLabel {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 500;
}

.catalogBanner-module__6ToAlG__decorativeElements {
  z-index: 2;
  pointer-events: none;
  position: absolute;
  inset: 0;
}

.catalogBanner-module__6ToAlG__decorativeCircle {
  border: 2px solid #d4af8c33;
  border-radius: 50%;
  width: 200px;
  height: 200px;
  animation: 6s ease-in-out infinite catalogBanner-module__6ToAlG__float;
  position: absolute;
  top: 20%;
  right: 10%;
}

.catalogBanner-module__6ToAlG__decorativeLine {
  background: linear-gradient(90deg, #0000, #d4af8c4d, #0000);
  width: 150px;
  height: 2px;
  animation: 8s ease-in-out infinite catalogBanner-module__6ToAlG__slide;
  position: absolute;
  bottom: 20%;
  left: 5%;
}

@keyframes catalogBanner-module__6ToAlG__float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes catalogBanner-module__6ToAlG__slide {
  0%, 100% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(50px);
  }
}

@media (width <= 1024px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    padding: 6rem 0;
  }

  .catalogBanner-module__6ToAlG__content {
    gap: 3rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 3rem;
  }

  .catalogBanner-module__6ToAlG__features {
    gap: 1.5rem;
  }
}

@media (width <= 768px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    margin: 3rem 0;
    padding: 4rem 0;
  }

  .catalogBanner-module__6ToAlG__container {
    padding: 0 1.5rem;
  }

  .catalogBanner-module__6ToAlG__content {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 2.5rem;
  }

  .catalogBanner-module__6ToAlG__description {
    font-size: 1rem;
  }

  .catalogBanner-module__6ToAlG__features {
    justify-content: center;
    gap: 1rem;
  }

  .catalogBanner-module__6ToAlG__ctaContainer {
    align-items: center;
  }

  .catalogBanner-module__6ToAlG__catalogStats {
    gap: 2rem;
  }
}

@media (width <= 480px) {
  .catalogBanner-module__6ToAlG__catalogBanner {
    padding: 3rem 0;
  }

  .catalogBanner-module__6ToAlG__container {
    padding: 0 1rem;
  }

  .catalogBanner-module__6ToAlG__title {
    font-size: 2rem;
  }

  .catalogBanner-module__6ToAlG__features {
    flex-direction: column;
    gap: 1rem;
  }

  .catalogBanner-module__6ToAlG__feature {
    justify-content: center;
  }

  .catalogBanner-module__6ToAlG__ctaButton {
    padding: 1rem 2rem;
    font-size: .9rem;
  }

  .catalogBanner-module__6ToAlG__catalogStats {
    gap: 1.5rem;
  }

  .catalogBanner-module__6ToAlG__statNumber {
    font-size: 2rem;
  }
}


/* [project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css [app-client] (css) */
.collectionsCarousel-module____MCqq__collectionsSection {
  background: linear-gradient(135deg, #fff 0%, #faf9f7 100%);
  padding: 6rem 0;
}

.collectionsCarousel-module____MCqq__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.collectionsCarousel-module____MCqq__header {
  justify-content: space-between;
  align-items: flex-end;
  gap: 2rem;
  margin-bottom: 3rem;
  display: flex;
}

.collectionsCarousel-module____MCqq__headerContent {
  flex: 1;
}

.collectionsCarousel-module____MCqq__title {
  color: #1e3a8a;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3rem;
  font-weight: 700;
}

.collectionsCarousel-module____MCqq__subtitle {
  color: #1e40af;
  max-width: 600px;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.collectionsCarousel-module____MCqq__navigation {
  gap: .5rem;
  display: flex;
}

.collectionsCarousel-module____MCqq__navButton {
  color: #1e3a8a;
  cursor: pointer;
  background: #fff;
  border: 2px solid #4a37281a;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
  box-shadow: 0 4px 12px #4a37281a;
}

.collectionsCarousel-module____MCqq__navButton:hover {
  color: #fff;
  background: #1e3a8a;
  border-color: #1e3a8a;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px #4a372833;
}

.collectionsCarousel-module____MCqq__carouselContainer {
  margin-bottom: 3rem;
  position: relative;
}

.collectionsCarousel-module____MCqq__carousel {
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  gap: 1.5rem;
  padding: 1rem 0;
  display: flex;
  overflow-x: auto;
}

.collectionsCarousel-module____MCqq__carousel::-webkit-scrollbar {
  display: none;
}

.collectionsCarousel-module____MCqq__collectionCard {
  background: #fff;
  border-radius: 16px;
  flex: 0 0 320px;
  height: 400px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #1e0bac1a;
}

.collectionsCarousel-module____MCqq__collectionCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px #4a372826;
}

.collectionsCarousel-module____MCqq__cardLink {
  width: 100%;
  height: 100%;
  color: inherit;
  text-decoration: none;
  display: block;
}

.collectionsCarousel-module____MCqq__imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.collectionsCarousel-module____MCqq__collectionImage {
  object-fit: cover;
  transition: transform .6s cubic-bezier(.4, 0, .2, 1);
}

.collectionsCarousel-module____MCqq__collectionCard:hover .collectionsCarousel-module____MCqq__collectionImage {
  transform: scale(1.05);
}

.collectionsCarousel-module____MCqq__imageOverlay {
  z-index: 1;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.collectionsCarousel-module____MCqq__noImagePlaceholder {
  background: linear-gradient(135deg, #1e0bac 0%, #4a3728 100%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.collectionsCarousel-module____MCqq__noImageText {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: 1rem;
  font-weight: 500;
}

.collectionsCarousel-module____MCqq__cardContent {
  color: #fff;
  z-index: 2;
  background: linear-gradient(to top, #19234166 0%, #141e3c59 50%, #1e284666 100% transparent 100%);
  padding: 2rem;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.collectionsCarousel-module____MCqq__productCount {
  text-transform: uppercase;
  letter-spacing: .1em;
  color: #1e3a8a;
  margin-bottom: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 500;
}

.collectionsCarousel-module____MCqq__collectionName {
  color: #fff;
  margin-bottom: .75rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.collectionsCarousel-module____MCqq__collectionDescription {
  color: #ffffffe6;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  line-height: 1.4;
  display: -webkit-box;
  overflow: hidden;
}

.collectionsCarousel-module____MCqq__cardAction {
  text-transform: uppercase;
  letter-spacing: .05em;
  color: #1e3a8a;
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.collectionsCarousel-module____MCqq__collectionCard:hover .collectionsCarousel-module____MCqq__cardAction {
  color: #fff;
  transform: translateX(4px);
}

.collectionsCarousel-module____MCqq__actionText {
  transition: transform .3s;
}

.collectionsCarousel-module____MCqq__viewAllContainer {
  text-align: center;
}

.collectionsCarousel-module____MCqq__viewAllButton {
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: .05em;
  background: none;
  border: 2px solid #1e3a8a;
  border-radius: 50px;
  align-items: center;
  gap: .75rem;
  padding: 1rem 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
}

.collectionsCarousel-module____MCqq__viewAllButton:hover {
  color: #fff;
  background: #1e3a8a;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #4a37284d;
}

.collectionsCarousel-module____MCqq__loadingContainer, .collectionsCarousel-module____MCqq__errorContainer {
  text-align: center;
  color: #8b7355;
  padding: 4rem 2rem;
}

.collectionsCarousel-module____MCqq__loadingSpinner {
  border: 3px solid #4a37281a;
  border-top-color: #1e3a8a;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto 1rem;
  animation: 1s linear infinite collectionsCarousel-module____MCqq__spin;
}

@keyframes collectionsCarousel-module____MCqq__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (width <= 1024px) {
  .collectionsCarousel-module____MCqq__collectionsSection {
    padding: 4rem 0;
  }

  .collectionsCarousel-module____MCqq__container {
    padding: 0 1.5rem;
  }

  .collectionsCarousel-module____MCqq__title {
    font-size: 2.5rem;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 280px;
    height: 350px;
  }
}

@media (width <= 768px) {
  .collectionsCarousel-module____MCqq__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .collectionsCarousel-module____MCqq__navigation {
    align-self: flex-end;
  }

  .collectionsCarousel-module____MCqq__title {
    font-size: 2rem;
  }

  .collectionsCarousel-module____MCqq__subtitle {
    font-size: 1rem;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 260px;
    height: 320px;
  }

  .collectionsCarousel-module____MCqq__cardContent {
    padding: 1.5rem;
  }
}

@media (width <= 480px) {
  .collectionsCarousel-module____MCqq__container {
    padding: 0 1rem;
  }

  .collectionsCarousel-module____MCqq__header {
    margin-bottom: 2rem;
  }

  .collectionsCarousel-module____MCqq__navigation {
    display: none;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 240px;
    height: 300px;
  }

  .collectionsCarousel-module____MCqq__cardContent {
    padding: 1.25rem;
  }

  .collectionsCarousel-module____MCqq__collectionName {
    font-size: 1.25rem;
  }
}


/* [project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css [app-client] (css) */
.testimonialsSection-module__2JZFAW__testimonialsSection {
  color: #fff;
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  padding: 8rem 0;
  position: relative;
  overflow: hidden;
}

.testimonialsSection-module__2JZFAW__testimonialsSection:before {
  content: "";
  opacity: .05;
  z-index: 1;
  background: url("/images/testimonials-pattern.svg");
  position: absolute;
  inset: 0;
}

.testimonialsSection-module__2JZFAW__container {
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.testimonialsSection-module__2JZFAW__header {
  text-align: center;
  max-width: 700px;
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
}

.testimonialsSection-module__2JZFAW__subtitle {
  text-transform: uppercase;
  letter-spacing: .15em;
  color: #white;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  display: block;
}

.testimonialsSection-module__2JZFAW__title {
  color: #fff;
  letter-spacing: -.02em;
  margin-bottom: 1.5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
}

.testimonialsSection-module__2JZFAW__description {
  color: #ffffffe6;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.testimonialsSection-module__2JZFAW__testimonialsContainer {
  grid-template-columns: 2fr 1fr;
  align-items: center;
  gap: 4rem;
  margin-bottom: 4rem;
  display: grid;
}

.testimonialsSection-module__2JZFAW__testimonialContent {
  position: relative;
}

.testimonialsSection-module__2JZFAW__quoteIcon {
  color: #white;
  opacity: .7;
  margin-bottom: 2rem;
}

.testimonialsSection-module__2JZFAW__testimonialText {
  position: relative;
}

.testimonialsSection-module__2JZFAW__testimonialContent {
  color: #fff;
  margin-bottom: 2rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-style: italic;
  font-weight: 400;
  line-height: 1.6;
}

.testimonialsSection-module__2JZFAW__rating {
  gap: .25rem;
  margin-bottom: 1.5rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__projectInfo {
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__projectLabel {
  color: #white;
  text-transform: uppercase;
  letter-spacing: .05em;
  font-weight: 600;
}

.testimonialsSection-module__2JZFAW__projectName {
  color: #ffffffe6;
  font-weight: 400;
}

.testimonialsSection-module__2JZFAW__testimonialMeta {
  flex-direction: column;
  gap: 2rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__authorInfo {
  align-items: center;
  gap: 1.5rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__authorImage {
  border: 3px solid #white;
  border-radius: 50%;
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  position: relative;
  overflow: hidden;
}

.testimonialsSection-module__2JZFAW__authorPhoto {
  object-fit: cover;
}

.testimonialsSection-module__2JZFAW__authorDetails {
  flex: 1;
}

.testimonialsSection-module__2JZFAW__authorName {
  color: #fff;
  margin-bottom: .25rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.2;
}

.testimonialsSection-module__2JZFAW__authorTitle {
  color: #white;
  margin-bottom: .125rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
}

.testimonialsSection-module__2JZFAW__authorCompany {
  color: #fffc;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 400;
}

.testimonialsSection-module__2JZFAW__navigation {
  justify-content: center;
  gap: .75rem;
  display: flex;
}

.testimonialsSection-module__2JZFAW__navDot {
  cursor: pointer;
  background: #ffffff4d;
  border: none;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.testimonialsSection-module__2JZFAW__navDot:hover {
  background: #d4af8cb3;
  transform: scale(1.2);
}

.testimonialsSection-module__2JZFAW__navDot.testimonialsSection-module__2JZFAW__active {
  background: #white;
  transform: scale(1.3);
}

.testimonialsSection-module__2JZFAW__stats {
  border-top: 1px solid #fff3;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding-top: 3rem;
  display: grid;
}

.testimonialsSection-module__2JZFAW__stat {
  text-align: center;
}

.testimonialsSection-module__2JZFAW__statNumber {
  color: #white;
  margin-bottom: .5rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  display: block;
}

.testimonialsSection-module__2JZFAW__statLabel {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 500;
}

@media (width <= 1024px) {
  .testimonialsSection-module__2JZFAW__testimonialsSection {
    padding: 6rem 0;
  }

  .testimonialsSection-module__2JZFAW__container {
    padding: 0 1.5rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 3rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialsContainer {
    gap: 3rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1.25rem;
  }
}

@media (width <= 768px) {
  .testimonialsSection-module__2JZFAW__testimonialsSection {
    padding: 4rem 0;
  }

  .testimonialsSection-module__2JZFAW__header {
    margin-bottom: 3rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 2.5rem;
  }

  .testimonialsSection-module__2JZFAW__description {
    font-size: 1rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialsContainer {
    text-align: center;
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1.125rem;
  }

  .testimonialsSection-module__2JZFAW__authorInfo {
    justify-content: center;
  }

  .testimonialsSection-module__2JZFAW__stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (width <= 480px) {
  .testimonialsSection-module__2JZFAW__container {
    padding: 0 1rem;
  }

  .testimonialsSection-module__2JZFAW__header {
    margin-bottom: 2rem;
  }

  .testimonialsSection-module__2JZFAW__title {
    font-size: 2rem;
  }

  .testimonialsSection-module__2JZFAW__testimonialContent {
    font-size: 1rem;
  }

  .testimonialsSection-module__2JZFAW__authorInfo {
    text-align: center;
    flex-direction: column;
    gap: 1rem;
  }

  .testimonialsSection-module__2JZFAW__authorImage {
    width: 60px;
    height: 60px;
  }

  .testimonialsSection-module__2JZFAW__stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .testimonialsSection-module__2JZFAW__statNumber {
    font-size: 2rem;
  }
}


/* [project]/src/components/Home/homeComponent.module.css [app-client] (css) */
.homeComponent-module__wtPNwW__homeComponent {
  scroll-behavior: smooth;
  background: #fff;
  min-height: 100vh;
  overflow-x: hidden;
}

.homeComponent-module__wtPNwW__homeComponent > * {
  z-index: 1;
  position: relative;
}

.homeComponent-module__wtPNwW__homeComponent section {
  opacity: 1;
  transition: all .6s cubic-bezier(.4, 0, .2, 1);
  transform: translateY(0);
}

@media (width <= 768px) {
  .homeComponent-module__wtPNwW__homeComponent {
    overflow-x: hidden;
  }
}

@media print {
  .homeComponent-module__wtPNwW__homeComponent {
    color: #000;
    background: #fff;
  }
}


/*# sourceMappingURL=src_components_Home_f1176315._.css.map*/