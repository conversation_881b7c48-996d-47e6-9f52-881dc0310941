{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/apiConfig.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n// API Configuration\r\n// export const BaseApiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7069/api';\r\nexport const BaseApiUrl = 'https://gracious-acceptance-production.up.railway.app/api';\r\n// API Response Types\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  message: string;\r\n  data?: T;\r\n  errors?: string[];\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  pageNumber: number;\r\n  pageSize: number;\r\n  totalRecords: number;\r\n  totalPages: number;\r\n  hasNextPage: boolean;\r\n  hasPreviousPage: boolean;\r\n}\r\n\r\n// HTTP Methods\r\nexport enum HttpMethod {\r\n  GET = 'GET',\r\n  POST = 'POST',\r\n  PUT = 'PUT',\r\n  PATCH = 'PATCH',\r\n  DELETE = 'DELETE'\r\n}\r\n\r\n// API Endpoints\r\nexport const ApiEndpoints = {\r\n  // Collections\r\n  Collections: {\r\n    Base: '/collections',\r\n    ById: (id: number) => `/collections/${id}`,\r\n    ByLevel: (level: number) => `/collections/level/${level}`,\r\n    Children: (id: number) => `/collections/${id}/children`,\r\n    Hierarchy: '/collections/hierarchy',\r\n    Published: '/collections/published',\r\n    Search: '/collections/search',\r\n    Filter: '/collections/filter',\r\n    RefreshRelationships: '/collections/refresh-relationships'\r\n  },\r\n  \r\n  // Products\r\n  Products: {\r\n    Base: '/products',\r\n    ById: (id: number) => `/products/${id}`,\r\n    ByCollection: (collectionId: number) => `/products/collection/${collectionId}`,\r\n    InStock: '/products/in-stock',\r\n    Featured: '/products/featured',\r\n    Latest: '/products/latest',\r\n    Search: '/products/search',\r\n    PriceRange: '/products/price-range',\r\n    UpdateStock: (id: number) => `/products/${id}/stock`,\r\n    Filter: '/products/filter'\r\n  },\r\n\r\n  // Product Specifications\r\n  ProductSpecifications: {\r\n    Base: '/productspecifications',\r\n    ById: (id: number) => `/productspecifications/${id}`,\r\n    ByProduct: (productId: number) => `/productspecifications/product/${productId}`\r\n  },\r\n\r\n  // Product Details\r\n  ProductDetails: {\r\n    Base: '/productdetails',\r\n    ById: (id: number) => `/productdetails/${id}`,\r\n    ByProduct: (productId: number) => `/productdetails/product/${productId}`\r\n  },\r\n\r\n  // Downloadable Content\r\n  DownloadableContent: {\r\n    Base: '/downloadablecontent',\r\n    ById: (id: number) => `/downloadablecontent/${id}`,\r\n    ByProduct: (productId: number) => `/downloadablecontent/product/${productId}`\r\n  },\r\n  \r\n  // Orders\r\n  Orders: {\r\n    Base: '/orders',\r\n    ById: (id: number) => `/orders/${id}`,\r\n    ByUser: (userId: number) => `/orders/user/${userId}`,\r\n    ByEmail: (email: string) => `/orders/email/${email}`,\r\n    ByStatus: (statusId: number) => `/orders/status/${statusId}`,\r\n    UpdateStatus: (id: number) => `/orders/${id}/status`,\r\n    Cancel: (id: number) => `/orders/${id}/cancel`,\r\n    Pending: '/orders/pending',\r\n    Recent: '/orders/recent',\r\n    Details: (id: number) => `/orders/${id}/details`,\r\n    Revenue: {\r\n      Total: '/orders/revenue/total',\r\n      Range: '/orders/revenue/range'\r\n    },\r\n    Filter: '/orders/filter'\r\n  },\r\n  \r\n  // Users\r\n  Users: {\r\n    Base: '/users',\r\n    ById: (id: number) => `/users/${id}`,\r\n    ByEmail: (email: string) => `/users/email/${email}`,\r\n    ByRole: (role: string) => `/users/role/${role}`,\r\n    Active: '/users/active',\r\n    Recent: '/users/recent',\r\n    Deactivate: (id: number) => `/users/${id}/deactivate`,\r\n    Activate: (id: number) => `/users/${id}/activate`,\r\n    WithOrders: (id: number) => `/users/${id}/orders`,\r\n    EmailExists: (email: string) => `/users/email-exists/${email}`,\r\n    Filter: '/users/filter'\r\n  },\r\n\r\n  // Cart\r\n  Cart: {\r\n    Base: '/cart',\r\n    ByUserId: (userId: number) => `/cart/user/${userId}`,\r\n    BySessionId: (sessionId: string) => `/cart/session/${sessionId}`,\r\n    SummaryByUserId: (userId: number) => `/cart/summary/user/${userId}`,\r\n    SummaryBySessionId: (sessionId: string) => `/cart/summary/session/${sessionId}`,\r\n    Add: '/cart/add',\r\n    UpdateItem: (cartId: number, productId: number) => `/cart/${cartId}/items/${productId}`,\r\n    RemoveItem: (cartId: number, productId: number) => `/cart/${cartId}/items/${productId}`,\r\n    RemoveCartItem: (cartItemId: number) => `/cart/items/${cartItemId}`,\r\n    Clear: (cartId: number) => `/cart/${cartId}/clear`,\r\n    ClearByUserId: (userId: number) => `/cart/user/${userId}/clear`,\r\n    ClearBySessionId: (sessionId: string) => `/cart/session/${sessionId}/clear`,\r\n    GetOrCreate: '/cart/get-or-create'\r\n  },\r\n\r\n  // Payments\r\n  Payments: '/payments',\r\n  \r\n  // Seeding\r\n  Seed: {\r\n    All: '/seed/all',\r\n    Statuses: '/seed/statuses',\r\n    AdminUser: '/seed/admin-user',\r\n    Collections: '/seed/collections',\r\n    Products: '/seed/products'\r\n  }\r\n} as const;\r\n\r\n// Request Configuration\r\nexport interface RequestConfig extends RequestInit {\r\n  params?: Record<string, string | number | boolean | undefined>;\r\n}\r\n\r\n// Error Types\r\nexport class ApiError extends Error {\r\n  constructor(\r\n    message: string,\r\n    public status?: number,\r\n    public errors?: string[]\r\n  ) {\r\n    super(message);\r\n    this.name = 'ApiError';\r\n  }\r\n}\r\n\r\n// Utility function to build query string\r\nexport const buildQueryString = (params: Record<string, any>): string => {\r\n  const searchParams = new URLSearchParams();\r\n  \r\n  Object.entries(params).forEach(([key, value]) => {\r\n    if (value !== undefined && value !== null && value !== '') {\r\n      if (Array.isArray(value)) {\r\n        value.forEach(item => searchParams.append(key, String(item)));\r\n      } else {\r\n        searchParams.append(key, String(value));\r\n      }\r\n    }\r\n  });\r\n  \r\n  const queryString = searchParams.toString();\r\n  return queryString ? `?${queryString}` : '';\r\n};\r\n\r\n// Default headers\r\nexport const defaultHeaders = {\r\n  'Content-Type': 'application/json',\r\n  'Accept': 'application/json'\r\n};\r\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD,oBAAoB;AACpB,6FAA6F;;;;;;;;;AACtF,MAAM,aAAa;AAoBnB,IAAA,AAAK,oCAAA;;;;;;WAAA;;AASL,MAAM,eAAe;IAC1B,cAAc;IACd,aAAa;QACX,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,aAAa,EAAE,IAAI;QAC1C,SAAS,CAAC,QAAkB,CAAC,mBAAmB,EAAE,OAAO;QACzD,UAAU,CAAC,KAAe,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC;QACvD,WAAW;QACX,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,sBAAsB;IACxB;IAEA,WAAW;IACX,UAAU;QACR,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,UAAU,EAAE,IAAI;QACvC,cAAc,CAAC,eAAyB,CAAC,qBAAqB,EAAE,cAAc;QAC9E,SAAS;QACT,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,YAAY;QACZ,aAAa,CAAC,KAAe,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC;QACpD,QAAQ;IACV;IAEA,yBAAyB;IACzB,uBAAuB;QACrB,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,uBAAuB,EAAE,IAAI;QACpD,WAAW,CAAC,YAAsB,CAAC,+BAA+B,EAAE,WAAW;IACjF;IAEA,kBAAkB;IAClB,gBAAgB;QACd,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,gBAAgB,EAAE,IAAI;QAC7C,WAAW,CAAC,YAAsB,CAAC,wBAAwB,EAAE,WAAW;IAC1E;IAEA,uBAAuB;IACvB,qBAAqB;QACnB,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,qBAAqB,EAAE,IAAI;QAClD,WAAW,CAAC,YAAsB,CAAC,6BAA6B,EAAE,WAAW;IAC/E;IAEA,SAAS;IACT,QAAQ;QACN,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,QAAQ,EAAE,IAAI;QACrC,QAAQ,CAAC,SAAmB,CAAC,aAAa,EAAE,QAAQ;QACpD,SAAS,CAAC,QAAkB,CAAC,cAAc,EAAE,OAAO;QACpD,UAAU,CAAC,WAAqB,CAAC,eAAe,EAAE,UAAU;QAC5D,cAAc,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;QACpD,QAAQ,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;QAC9C,SAAS;QACT,QAAQ;QACR,SAAS,CAAC,KAAe,CAAC,QAAQ,EAAE,GAAG,QAAQ,CAAC;QAChD,SAAS;YACP,OAAO;YACP,OAAO;QACT;QACA,QAAQ;IACV;IAEA,QAAQ;IACR,OAAO;QACL,MAAM;QACN,MAAM,CAAC,KAAe,CAAC,OAAO,EAAE,IAAI;QACpC,SAAS,CAAC,QAAkB,CAAC,aAAa,EAAE,OAAO;QACnD,QAAQ,CAAC,OAAiB,CAAC,YAAY,EAAE,MAAM;QAC/C,QAAQ;QACR,QAAQ;QACR,YAAY,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;QACrD,UAAU,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;QACjD,YAAY,CAAC,KAAe,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC;QACjD,aAAa,CAAC,QAAkB,CAAC,oBAAoB,EAAE,OAAO;QAC9D,QAAQ;IACV;IAEA,OAAO;IACP,MAAM;QACJ,MAAM;QACN,UAAU,CAAC,SAAmB,CAAC,WAAW,EAAE,QAAQ;QACpD,aAAa,CAAC,YAAsB,CAAC,cAAc,EAAE,WAAW;QAChE,iBAAiB,CAAC,SAAmB,CAAC,mBAAmB,EAAE,QAAQ;QACnE,oBAAoB,CAAC,YAAsB,CAAC,sBAAsB,EAAE,WAAW;QAC/E,KAAK;QACL,YAAY,CAAC,QAAgB,YAAsB,CAAC,MAAM,EAAE,OAAO,OAAO,EAAE,WAAW;QACvF,YAAY,CAAC,QAAgB,YAAsB,CAAC,MAAM,EAAE,OAAO,OAAO,EAAE,WAAW;QACvF,gBAAgB,CAAC,aAAuB,CAAC,YAAY,EAAE,YAAY;QACnE,OAAO,CAAC,SAAmB,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC;QAClD,eAAe,CAAC,SAAmB,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QAC/D,kBAAkB,CAAC,YAAsB,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC;QAC3E,aAAa;IACf;IAEA,WAAW;IACX,UAAU;IAEV,UAAU;IACV,MAAM;QACJ,KAAK;QACL,UAAU;QACV,WAAW;QACX,aAAa;QACb,UAAU;IACZ;AACF;AAQO,MAAM,iBAAiB;;;IAC5B,YACE,OAAe,EACf,AAAO,MAAe,EACtB,AAAO,MAAiB,CACxB;QACA,KAAK,CAAC,eAHC,SAAA,aACA,SAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,eAAe,IAAI;IAEzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAA,OAAQ,aAAa,MAAM,CAAC,KAAK,OAAO;YACxD,OAAO;gBACL,aAAa,MAAM,CAAC,KAAK,OAAO;YAClC;QACF;IACF;IAEA,MAAM,cAAc,aAAa,QAAQ;IACzC,OAAO,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;AAC3C;AAGO,MAAM,iBAAiB;IAC5B,gBAAgB;IAChB,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/httpClient.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { \r\n  BaseApiUrl, \r\n  ApiResponse, \r\n  HttpMethod, \r\n  RequestConfig, \r\n  ApiError, \r\n  defaultHeaders,\r\n  buildQueryString \r\n} from './apiConfig';\r\n\r\nclass HttpClient {\r\n  private baseUrl: string;\r\n\r\n  constructor(baseUrl: string = BaseApiUrl) {\r\n    this.baseUrl = baseUrl;\r\n  }\r\n\r\n  private async request<T>(\r\n    endpoint: string,\r\n    method: HttpMethod = HttpMethod.GET,\r\n    config: RequestConfig = {}\r\n  ): Promise<ApiResponse<T>> {\r\n    const { params, ...requestConfig } = config;\r\n    \r\n    // Build URL with query parameters\r\n    let url = `${this.baseUrl}${endpoint}`;\r\n    if (params) {\r\n      url += buildQueryString(params);\r\n    }\r\n\r\n    const requestOptions: RequestInit = {\r\n      method,\r\n      headers: {\r\n        ...defaultHeaders,\r\n        ...requestConfig.headers,\r\n      },\r\n      ...requestConfig,\r\n    };\r\n\r\n    // Add body for non-GET requests\r\n    if (method !== HttpMethod.GET && requestConfig.body) {\r\n      if (typeof requestConfig.body === 'object') {\r\n        requestOptions.body = JSON.stringify(requestConfig.body);\r\n      }\r\n    }\r\n\r\n    try {\r\n      const response = await fetch(url, requestOptions);\r\n      \r\n      // Handle different response types\r\n      let responseData: ApiResponse<T>;\r\n      \r\n      const contentType = response.headers.get('content-type');\r\n      if (contentType && contentType.includes('application/json')) {\r\n        responseData = await response.json();\r\n      } else {\r\n        // Handle non-JSON responses\r\n        const text = await response.text();\r\n        responseData = {\r\n          success: response.ok,\r\n          message: response.ok ? 'Success' : 'Error',\r\n          data: text as any,\r\n        };\r\n      }\r\n\r\n      if (!response.ok) {\r\n        throw new ApiError(\r\n          responseData.message || `HTTP error! status: ${response.status}`,\r\n          response.status,\r\n          responseData.errors\r\n        );\r\n      }\r\n\r\n      return responseData;\r\n    } catch (error) {\r\n      if (error instanceof ApiError) {\r\n        throw error;\r\n      }\r\n      \r\n      console.error('API request failed:', error);\r\n      throw new ApiError(\r\n        error instanceof Error ? error.message : 'Unknown error occurred'\r\n      );\r\n    }\r\n  }\r\n\r\n  // GET request\r\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, HttpMethod.GET, { params });\r\n  }\r\n\r\n  // POST request\r\n  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, HttpMethod.POST, {\r\n      ...config,\r\n      body: data,\r\n    });\r\n  }\r\n\r\n  // PUT request\r\n  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, HttpMethod.PUT, {\r\n      ...config,\r\n      body: data,\r\n    });\r\n  }\r\n\r\n  // PATCH request\r\n  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, HttpMethod.PATCH, {\r\n      ...config,\r\n      body: data,\r\n    });\r\n  }\r\n\r\n  // DELETE request\r\n  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {\r\n    return this.request<T>(endpoint, HttpMethod.DELETE, config);\r\n  }\r\n\r\n  // Upload file (multipart/form-data)\r\n  async upload<T>(endpoint: string, formData: FormData, config?: RequestConfig): Promise<ApiResponse<T>> {\r\n    const uploadConfig = {\r\n      ...config,\r\n      headers: {\r\n        // Don't set Content-Type for FormData, let browser set it with boundary\r\n        ...config?.headers,\r\n      },\r\n      body: formData,\r\n    };\r\n    \r\n    // Remove Content-Type header for file uploads\r\n    if (uploadConfig.headers && 'Content-Type' in uploadConfig.headers) {\r\n      delete uploadConfig.headers['Content-Type'];\r\n    }\r\n\r\n    return this.request<T>(endpoint, HttpMethod.POST, uploadConfig);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const httpClient = new HttpClient();\r\n\r\n// Export class for custom instances if needed\r\nexport { HttpClient };\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;;AAUA,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,sIAAA,CAAA,aAAU,CAAE;QACxC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QACZ,QAAgB,EAChB,SAAqB,sIAAA,CAAA,aAAU,CAAC,GAAG,EACnC,SAAwB,CAAC,CAAC,EACD;QACzB,MAAM,EAAE,MAAM,EAAE,GAAG,eAAe,GAAG;QAErC,kCAAkC;QAClC,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACtC,IAAI,QAAQ;YACV,OAAO,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1B;QAEA,MAAM,iBAA8B;YAClC;YACA,SAAS;gBACP,GAAG,sIAAA,CAAA,iBAAc;gBACjB,GAAG,cAAc,OAAO;YAC1B;YACA,GAAG,aAAa;QAClB;QAEA,gCAAgC;QAChC,IAAI,WAAW,sIAAA,CAAA,aAAU,CAAC,GAAG,IAAI,cAAc,IAAI,EAAE;YACnD,IAAI,OAAO,cAAc,IAAI,KAAK,UAAU;gBAC1C,eAAe,IAAI,GAAG,KAAK,SAAS,CAAC,cAAc,IAAI;YACzD;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,kCAAkC;YAClC,IAAI;YAEJ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;gBAC3D,eAAe,MAAM,SAAS,IAAI;YACpC,OAAO;gBACL,4BAA4B;gBAC5B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe;oBACb,SAAS,SAAS,EAAE;oBACpB,SAAS,SAAS,EAAE,GAAG,YAAY;oBACnC,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,sIAAA,CAAA,WAAQ,CAChB,aAAa,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,EAChE,SAAS,MAAM,EACf,aAAa,MAAM;YAEvB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,sIAAA,CAAA,WAAQ,EAAE;gBAC7B,MAAM;YACR;YAEA,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,sIAAA,CAAA,WAAQ,CAChB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF;IAEA,cAAc;IACd,MAAM,IAAO,QAAgB,EAAE,MAA4B,EAA2B;QACpF,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,GAAG,EAAE;YAAE;QAAO;IAC5D;IAEA,eAAe;IACf,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC3F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;YAChD,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,cAAc;IACd,MAAM,IAAO,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC1F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,GAAG,EAAE;YAC/C,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,MAAS,QAAgB,EAAE,IAAU,EAAE,MAAsB,EAA2B;QAC5F,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,KAAK,EAAE;YACjD,GAAG,MAAM;YACT,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,MAAM,OAAU,QAAgB,EAAE,MAAsB,EAA2B;QACjF,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,MAAM,EAAE;IACtD;IAEA,oCAAoC;IACpC,MAAM,OAAU,QAAgB,EAAE,QAAkB,EAAE,MAAsB,EAA2B;QACrG,MAAM,eAAe;YACnB,GAAG,MAAM;YACT,SAAS;gBACP,wEAAwE;gBACxE,GAAG,QAAQ,OAAO;YACpB;YACA,MAAM;QACR;QAEA,8CAA8C;QAC9C,IAAI,aAAa,OAAO,IAAI,kBAAkB,aAAa,OAAO,EAAE;YAClE,OAAO,aAAa,OAAO,CAAC,eAAe;QAC7C;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU,sIAAA,CAAA,aAAU,CAAC,IAAI,EAAE;IACpD;AACF;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/config/baseService.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { httpClient } from './httpClient';\r\nimport { ApiResponse, PaginatedResponse } from './apiConfig';\r\n\r\nexport abstract class BaseService {\r\n  protected client = httpClient;\r\n\r\n  /**\r\n   * Handle API response and extract data\r\n   */\r\n  protected async handleResponse<T>(\r\n    apiCall: Promise<ApiResponse<T>>\r\n  ): Promise<T> {\r\n    try {\r\n      const response = await apiCall;\r\n      if (response.success && response.data !== undefined) {\r\n        return response.data;\r\n      }\r\n      throw new Error(response.message || 'API call failed');\r\n    } catch (error) {\r\n      console.error('API Error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle paginated API response\r\n   */\r\n  protected async handlePaginatedResponse<T>(\r\n    apiCall: Promise<ApiResponse<PaginatedResponse<T>>>\r\n  ): Promise<PaginatedResponse<T>> {\r\n    try {\r\n      const response = await apiCall;\r\n      if (response.success && response.data !== undefined) {\r\n        return response.data;\r\n      }\r\n      throw new Error(response.message || 'API call failed');\r\n    } catch (error) {\r\n      console.error('API Error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle API response without data extraction (for operations like delete)\r\n   */\r\n  protected async handleVoidResponse(\r\n    apiCall: Promise<ApiResponse<any>>\r\n  ): Promise<boolean> {\r\n    try {\r\n      const response = await apiCall;\r\n      return response.success;\r\n    } catch (error) {\r\n      console.error('API Error:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Log API calls in development\r\n   */\r\n  protected logApiCall(method: string, endpoint: string, data?: any): void {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log(`🌐 API ${method}:`, endpoint, data ? { data } : '');\r\n    }\r\n  }\r\n}\r\n\r\n// Utility functions for common operations\r\nexport class ServiceUtils {\r\n  /**\r\n   * Format date for API calls\r\n   */\r\n  static formatDate(date: Date): string {\r\n    return date.toISOString();\r\n  }\r\n\r\n  /**\r\n   * Parse API date string to Date object\r\n   */\r\n  static parseDate(dateString: string): Date {\r\n    return new Date(dateString);\r\n  }\r\n\r\n  /**\r\n   * Validate email format\r\n   */\r\n  static isValidEmail(email: string): boolean {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  }\r\n\r\n  /**\r\n   * Format currency\r\n   */\r\n  static formatCurrency(amount: number, currency: string = 'USD'): string {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: currency,\r\n    }).format(amount);\r\n  }\r\n\r\n  /**\r\n   * Debounce function for search inputs\r\n   */\r\n  static debounce<T extends (...args: any[]) => any>(\r\n    func: T,\r\n    wait: number\r\n  ): (...args: Parameters<T>) => void {\r\n    let timeout: NodeJS.Timeout;\r\n    return (...args: Parameters<T>) => {\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(() => func(...args), wait);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean undefined values from objects (useful for API params)\r\n   */\r\n  static cleanObject<T extends Record<string, any>>(obj: T): Partial<T> {\r\n    const cleaned: Partial<T> = {};\r\n    Object.entries(obj).forEach(([key, value]) => {\r\n      if (value !== undefined && value !== null && value !== '') {\r\n        cleaned[key as keyof T] = value;\r\n      }\r\n    });\r\n    return cleaned;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;;AAGO,MAAe;IACV,SAAS,uIAAA,CAAA,aAAU,CAAC;IAE9B;;GAEC,GACD,MAAgB,eACd,OAAgC,EACpB;QACZ,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO,SAAS,IAAI;YACtB;YACA,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAgB,wBACd,OAAmD,EACpB;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO,SAAS,IAAI;YACtB;YACA,MAAM,IAAI,MAAM,SAAS,OAAO,IAAI;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAgB,mBACd,OAAkC,EAChB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM;YACvB,OAAO,SAAS,OAAO;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAU,WAAW,MAAc,EAAE,QAAgB,EAAE,IAAU,EAAQ;QACvE,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,UAAU,OAAO;gBAAE;YAAK,IAAI;QAC/D;IACF;AACF;AAGO,MAAM;IACX;;GAEC,GACD,OAAO,WAAW,IAAU,EAAU;QACpC,OAAO,KAAK,WAAW;IACzB;IAEA;;GAEC,GACD,OAAO,UAAU,UAAkB,EAAQ;QACzC,OAAO,IAAI,KAAK;IAClB;IAEA;;GAEC,GACD,OAAO,aAAa,KAAa,EAAW;QAC1C,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA;;GAEC,GACD,OAAO,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QACtE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA;;GAEC,GACD,OAAO,SACL,IAAO,EACP,IAAY,EACsB;QAClC,IAAI;QACJ,OAAO,CAAC,GAAG;YACT,aAAa;YACb,UAAU,WAAW,IAAM,QAAQ,OAAO;QAC5C;IACF;IAEA;;GAEC,GACD,OAAO,YAA2C,GAAM,EAAc;QACpE,MAAM,UAAsB,CAAC;QAC7B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACvC,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;gBACzD,OAAO,CAAC,IAAe,GAAG;YAC5B;QACF;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/types/entities.ts"], "sourcesContent": ["// Collection Types\r\nexport interface Collection {\r\n  id: number;\r\n  name: string;\r\n  description?: string;\r\n  level: number;\r\n  parentCollectionId?: number;\r\n  childCollectionIds?: number[];\r\n  tags: string[];\r\n  images: string[];\r\n  productIds?: number[];\r\n  published: boolean;\r\n  createdBy: string;\r\n  createdAt: string;\r\n  updatedBy?: string;\r\n  updatedAt?: string;\r\n  productCount: number;\r\n  parentCollection?: Collection;\r\n  childCollections: Collection[];\r\n  products: Product[];\r\n}\r\n\r\nexport interface CollectionHierarchy {\r\n  id: number;\r\n  name: string;\r\n  description?: string;\r\n  level: number;\r\n  tags: string[];\r\n  published: boolean;\r\n  children: CollectionHierarchy[];\r\n  productCount: number;\r\n  images?: string[]; \r\n}\r\n\r\nexport interface CreateCollectionRequest {\r\n  name: string;\r\n  description?: string;\r\n  level: number;\r\n  parentCollectionId?: number;\r\n  childCollectionIds?: number[];\r\n  tags: string[];\r\n  images: string[];\r\n  productIds?: number[];\r\n  published: boolean;\r\n  createdBy: string;\r\n}\r\n\r\nexport interface UpdateCollectionRequest {\r\n  name: string;\r\n  description?: string;\r\n  level: number;\r\n  parentCollectionId?: number;\r\n  childCollectionIds?: number[];\r\n  tags: string[];\r\n  images: string[];\r\n  productIds?: number[];\r\n  published: boolean;\r\n  updatedBy: string;\r\n}\r\n\r\nexport interface CollectionFilterRequest {\r\n  name?: string;\r\n  level?: number;\r\n  parentCollectionId?: number;\r\n  published?: boolean;\r\n  createdBy?: string;\r\n  createdAfter?: string;\r\n  createdBefore?: string;\r\n  updatedAfter?: string;\r\n  updatedBefore?: string;\r\n  tag?: string;\r\n  pageNumber?: number;\r\n  pageSize?: number;\r\n  sortBy?: string;\r\n  sortDirection?: 'asc' | 'desc';\r\n}\r\n\r\n// Product Specifications Types\r\nexport interface ProductSpecifications {\r\n  id: number;\r\n  material?: string;\r\n  dimensions?: string;\r\n  base_Dimensions?: string;\r\n  photographed_In?: string;\r\n  pieces?: string;\r\n  totalWeight?: string;\r\n  weightWithWater?: string;\r\n  waterVolume?: string;\r\n  productId: number;\r\n}\r\n\r\nexport interface CreateProductSpecificationsRequest {\r\n  material?: string;\r\n  dimensions?: string;\r\n  totalWeight?: string;\r\n  weightWithWater?: string;\r\n  waterVolume?: string;\r\n  productId: number;\r\n  base_Dimensions?: string;\r\n  photographed_In?: string;\r\n  pieces?: string;\r\n}\r\n\r\nexport interface UpdateProductSpecificationsRequest {\r\n  material?: string;\r\n  dimensions?: string;\r\n  totalWeight?: string;\r\n  weightWithWater?: string;\r\n  waterVolume?: string;\r\n  base_Dimensions?: string;\r\n  photographed_In?: string;\r\n  pieces?: string;\r\n\r\n}\r\n\r\n// Product Details Types\r\nexport interface ProductDetails {\r\n  id: number;\r\n  upc?: string;\r\n  indoorUseOnly?: string;\r\n  assemblyRequired?: string;\r\n  easeOfAssembly?: string;\r\n  assistanceRequired?: string;\r\n  splashLevel?: string;\r\n  soundLevel?: string;\r\n  soundType?: string;\r\n  replacementPumpKit?: string;\r\n  electricalCordLength?: string;\r\n  pumpSize?: string;\r\n  shipMethod?: string;\r\n  drainage_Info?: string;\r\n  inside_Top?: string;\r\n  inside_Bottom?: string;\r\n  inside_Height?: string;\r\n  catalogPage?: string;\r\n  factory_Code?: string;\r\n  productId: number;\r\n}\r\n\r\nexport interface CreateProductDetailsRequest {\r\n  upc?: string;\r\n  indoorUseOnly?: string;\r\n  assemblyRequired?: string;\r\n  easeOfAssembly?: string;\r\n  assistanceRequired?: string;\r\n  splashLevel?: string;\r\n  soundLevel?: string;\r\n  soundType?: string;\r\n  replacementPumpKit?: string;\r\n  electricalCordLength?: string;\r\n  pumpSize?: string;\r\n  shipMethod?: string;\r\n  drainage_Info?: string;\r\n  inside_Top?: string;\r\n  inside_Bottom?: string;\r\n  inside_Height?: string;\r\n  catalogPage?: string;\r\n  factory_Code?: string;\r\n  productId: number;\r\n}\r\n\r\nexport interface UpdateProductDetailsRequest {\r\n  upc?: string;\r\n  indoorUseOnly?: string;\r\n  assemblyRequired?: string;\r\n  easeOfAssembly?: string;\r\n  assistanceRequired?: string;\r\n  splashLevel?: string;\r\n  soundLevel?: string;\r\n  soundType?: string;\r\n  replacementPumpKit?: string;\r\n  electricalCordLength?: string;\r\n  pumpSize?: string;\r\n  shipMethod?: string;\r\n  catalogPage?: string;\r\n  Factory_Code?: string;\r\n  Drainage_Info?: string;\r\n  Inside_Top?: string;\r\n  Inside_Bottom?: string;\r\n  Inside_Height?: string;\r\n}\r\n\r\n// Downloadable Content Types\r\nexport interface DownloadableContent {\r\n  id: number;\r\n  care?: string;\r\n  productInstructions?: string;\r\n  cad?: string;\r\n  productId: number;\r\n}\r\n\r\nexport interface CreateDownloadableContentRequest {\r\n  care?: string;\r\n  productInstructions?: string;\r\n  cad?: string;\r\n  productId: number;\r\n}\r\n\r\nexport interface UpdateDownloadableContentRequest {\r\n  care?: string;\r\n  productInstructions?: string;\r\n  cad?: string;\r\n}\r\n\r\n// Product Types\r\nexport interface Product {\r\n  id: number;\r\n  name: string;\r\n  productCode?: string;\r\n  description?: string;\r\n  price: number;\r\n  stock: number;\r\n  collectionId: number;\r\n  images: string[];\r\n  tags: string[];\r\n  createdAt: string;\r\n  updatedAt?: string;\r\n  collection?: Collection;\r\n  productSpecifications?: ProductSpecifications;\r\n  productDetails?: ProductDetails;\r\n  downloadableContent?: DownloadableContent;\r\n}\r\n\r\nexport interface ProductSummary {\r\n  id: number;\r\n  name: string;\r\n  price: number;\r\n  stock: number;\r\n  mainImage?: string;\r\n  collectionName: string;\r\n  inStock: boolean;\r\n}\r\n\r\nexport interface CreateProductRequest {\r\n  name: string;\r\n  productCode?: string;\r\n  description?: string;\r\n  price: number;\r\n  stock: number;\r\n  collectionId: number;\r\n  images: string[];\r\n  tags: string[];\r\n  productSpecifications?: CreateProductSpecificationsRequest;\r\n  productDetails?: CreateProductDetailsRequest;\r\n  downloadableContent?: CreateDownloadableContentRequest;\r\n}\r\n\r\nexport interface UpdateProductRequest {\r\n  name: string;\r\n  productCode?: string;\r\n  description?: string;\r\n  price: number;\r\n  stock: number;\r\n  collectionId: number;\r\n  images: string[];\r\n  tags: string[];\r\n  productSpecifications?: UpdateProductSpecificationsRequest;\r\n  productDetails?: UpdateProductDetailsRequest;\r\n  downloadableContent?: UpdateDownloadableContentRequest;\r\n}\r\n\r\nexport interface ProductFilterRequest {\r\n  name?: string;\r\n  collectionId?: number;\r\n  minPrice?: number;\r\n  maxPrice?: number;\r\n  minStock?: number;\r\n  maxStock?: number;\r\n  inStock?: boolean;\r\n  createdAfter?: string;\r\n  createdBefore?: string;\r\n  updatedAfter?: string;\r\n  updatedBefore?: string;\r\n  tag?: string;\r\n  pageNumber?: number;\r\n  pageSize?: number;\r\n  sortBy?: string;\r\n  sortDirection?: 'asc' | 'desc';\r\n}\r\n\r\n// Order Types\r\nexport interface Order {\r\n  id: number;\r\n  userId?: number;\r\n  email: string;\r\n  phoneNumber?: string;\r\n  country?: string;\r\n  city?: string;\r\n  zipCode?: string;\r\n  totalAmount: number;\r\n  statusId: number;\r\n  paymentMethod?: string;\r\n  createdAt: string;\r\n  user?: User;\r\n  status: Status;\r\n  orderItems: OrderItem[];\r\n}\r\n\r\nexport interface OrderSummary {\r\n  statusId: number;\r\n  id: number;\r\n  email: string;\r\n  totalAmount: number;\r\n  statusName: string;\r\n  createdAt: string;\r\n  itemCount: number;\r\n}\r\n\r\nexport interface OrderItem {\r\n  id: number;\r\n  productId: number;\r\n  quantity: number;\r\n  priceAtPurchaseTime: number;\r\n  orderId: number;\r\n  product?: Product;\r\n}\r\n\r\nexport interface CreateOrderRequest {\r\n  userId?: number;\r\n  email: string;\r\n  phoneNumber?: string;\r\n  country?: string;\r\n  city?: string;\r\n  zipCode?: string;\r\n  paymentMethod?: string;\r\n  orderItems: CreateOrderItemRequest[];\r\n}\r\n\r\nexport interface CreateOrderItemRequest {\r\n  productId: number;\r\n  quantity: number;\r\n}\r\n\r\nexport interface UpdateOrderStatusRequest {\r\n  statusId: number;\r\n}\r\n\r\nexport interface OrderFilterRequest {\r\n  userId?: number;\r\n  email?: string;\r\n  statusId?: number;\r\n  minAmount?: number;\r\n  maxAmount?: number;\r\n  paymentMethod?: string;\r\n  country?: string;\r\n  city?: string;\r\n  createdAfter?: string;\r\n  createdBefore?: string;\r\n  pageNumber?: number;\r\n  pageSize?: number;\r\n  sortBy?: string;\r\n  sortDirection?: 'asc' | 'desc';\r\n}\r\n\r\n// User Types\r\nexport interface User {\r\n  id: number;\r\n  role: string;\r\n  email: string;\r\n  phoneNumber?: string;\r\n  name?: string;\r\n  country?: string;\r\n  city?: string;\r\n  zipCode?: string;\r\n  createdAt: string;\r\n  active: boolean;\r\n}\r\n\r\nexport interface CreateUserRequest {\r\n  role: string;\r\n  email: string;\r\n  phoneNumber?: string;\r\n  password: string;\r\n  name?: string;\r\n  country?: string;\r\n  city?: string;\r\n  zipCode?: string;\r\n  active: boolean;\r\n}\r\n\r\nexport interface UpdateUserRequest {\r\n  role: string;\r\n  phoneNumber?: string;\r\n  name?: string;\r\n  country?: string;\r\n  city?: string;\r\n  zipCode?: string;\r\n  active: boolean;\r\n}\r\n\r\nexport interface UserFilterRequest {\r\n  email?: string;\r\n  role?: string;\r\n  active?: boolean;\r\n  country?: string;\r\n  city?: string;\r\n  name?: string;\r\n  createdAfter?: string;\r\n  createdBefore?: string;\r\n  pageNumber?: number;\r\n  pageSize?: number;\r\n  sortBy?: string;\r\n  sortDirection?: 'asc' | 'desc';\r\n}\r\n\r\n// Status Types\r\nexport interface Status {\r\n  id: number;\r\n  statusName: string;\r\n}\r\n\r\n// Cart Types\r\nexport interface Cart {\r\n  id: number;\r\n  userId?: number;\r\n  sessionId?: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  cartItems: CartItem[];\r\n  totalAmount: number;\r\n  totalItems: number;\r\n}\r\n\r\nexport interface CartItem {\r\n  id: number;\r\n  cartId: number;\r\n  productId: number;\r\n  quantity: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  product?: Product;\r\n  itemTotal: number;\r\n}\r\n\r\nexport interface CartSummary {\r\n  id: number;\r\n  totalItems: number;\r\n  totalAmount: number;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface AddToCartRequest {\r\n  productId: number;\r\n  quantity: number;\r\n  userId?: number;\r\n  sessionId?: string;\r\n}\r\n\r\nexport interface UpdateCartItemRequest {\r\n  quantity: number;\r\n}\r\n\r\n// Contact Form Types\r\nexport enum InquiryType {\r\n  ProductInquiry = 1,\r\n  RequestDesignConsultation = 2,\r\n  CustomOrders = 3,\r\n  TradePartnerships = 4,\r\n  InstallationSupport = 5,\r\n  ShippingAndLeadTimes = 6,\r\n  RequestCatalogPriceList = 7,\r\n  MediaPressInquiry = 8,\r\n  GeneralQuestions = 9\r\n}\r\n\r\nexport interface ContactFormSubmission {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  company?: string;\r\n  state: string;\r\n  inquiry: InquiryType;\r\n  inquiryDisplayName: string;\r\n  message: string;\r\n  createdAt: string;\r\n}\r\n\r\nexport interface CreateContactFormSubmissionRequest {\r\n  name: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  company?: string;\r\n  state: string;\r\n  inquiry: InquiryType;\r\n  message: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;AAqcZ,IAAA,AAAK,qCAAA;;;;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints, PaginatedResponse } from '../../config/apiConfig';\r\nimport { \r\n  Collection, \r\n  CollectionHierarchy, \r\n  CollectionFilterRequest,\r\n   \r\n} from '../../types/entities';\r\n\r\nexport class CollectionGetService extends BaseService {\r\n  /**\r\n   * Get all collections\r\n   */\r\n  async getAll(): Promise<Collection[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Base);\r\n    return this.handleResponse(\r\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Base)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collection by ID\r\n   */\r\n  async getById(id: number): Promise<Collection> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.ById(id));\r\n    return this.handleResponse(\r\n      this.client.get<Collection>(ApiEndpoints.Collections.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collections by level\r\n   */\r\n  async getByLevel(level: number): Promise<Collection[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.ByLevel(level));\r\n    return this.handleResponse(\r\n      this.client.get<Collection[]>(ApiEndpoints.Collections.ByLevel(level))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get children of a collection\r\n   */\r\n  async getChildren(parentId: number): Promise<Collection[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Children(parentId));\r\n    return this.handleResponse(\r\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Children(parentId))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collection hierarchy\r\n   */\r\n  async getHierarchy(): Promise<CollectionHierarchy[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Hierarchy);\r\n    return this.handleResponse(\r\n      this.client.get<CollectionHierarchy[]>(ApiEndpoints.Collections.Hierarchy)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get published collections\r\n   */\r\n  async getPublished(): Promise<Collection[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Published);\r\n    return this.handleResponse(\r\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Published)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Search collections by name\r\n   */\r\n  async search(name: string): Promise<Collection[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Search, { name });\r\n    return this.handleResponse(\r\n      this.client.get<Collection[]>(ApiEndpoints.Collections.Search, { name })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collections with advanced filtering and pagination\r\n   */\r\n  async getFiltered(filters: CollectionFilterRequest): Promise<PaginatedResponse<Collection>> {\r\n    const cleanFilters = ServiceUtils.cleanObject(filters);\r\n    this.logApiCall('GET', ApiEndpoints.Collections.Filter, cleanFilters);\r\n    \r\n    return this.handlePaginatedResponse(\r\n      this.client.get<PaginatedResponse<Collection>>(\r\n        ApiEndpoints.Collections.Filter, \r\n        cleanFilters\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get collections with default pagination\r\n   */\r\n  async getPaginated(\r\n    pageNumber: number = 1, \r\n    pageSize: number = 10,\r\n    sortBy: string = 'createdAt',\r\n    sortDirection: 'asc' | 'desc' = 'desc'\r\n  ): Promise<PaginatedResponse<Collection>> {\r\n    const filters: CollectionFilterRequest = {\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy,\r\n      sortDirection\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get root collections (level 1)\r\n   */\r\n  async getRootCollections(): Promise<Collection[]> {\r\n    return this.getByLevel(1);\r\n  }\r\n\r\n  /**\r\n   * Get collections by tag\r\n   */\r\n  async getByTag(tag: string): Promise<Collection[]> {\r\n    const filters: CollectionFilterRequest = {\r\n      tag,\r\n      pageSize: 100 // Get all matching collections\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get collections created by specific user\r\n   */\r\n  async getByCreatedBy(createdBy: string): Promise<Collection[]> {\r\n    const filters: CollectionFilterRequest = {\r\n      createdBy,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get collections created within date range\r\n   */\r\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Collection[]> {\r\n    const filters: CollectionFilterRequest = {\r\n      createdAfter: ServiceUtils.formatDate(startDate),\r\n      createdBefore: ServiceUtils.formatDate(endDate),\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const collectionGetService = new CollectionGetService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,SAAgC;QACpC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI;IAE/D;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAuB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QACrD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAa,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;IAE9D;IAEA;;GAEC,GACD,MAAM,WAAW,KAAa,EAAyB;QACrD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,OAAO,CAAC;QACxD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,OAAO,CAAC;IAEnE;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAyB;QACzD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;IAEpE;IAEA;;GAEC,GACD,MAAM,eAA+C;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;IAE7E;IAEA;;GAEC,GACD,MAAM,eAAsC;QAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;QACzD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,SAAS;IAEpE;IAEA;;GAEC,GACD,MAAM,OAAO,IAAY,EAAyB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;YAAE;QAAK;QAC/D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAe,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;YAAE;QAAK;IAE1E;IAEA;;GAEC,GACD,MAAM,YAAY,OAAgC,EAA0C;QAC1F,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAAE;QAExD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM,EAC/B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACE;QACxC,MAAM,UAAmC;YACvC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,qBAA4C;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB;IAEA;;GAEC,GACD,MAAM,SAAS,GAAW,EAAyB;QACjD,MAAM,UAAmC;YACvC;YACA,UAAU,IAAI,+BAA+B;QAC/C;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAyB;QAC7D,MAAM,UAAmC;YACvC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAyB;QAC1E,MAAM,UAAmC;YACvC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Collection, CreateCollectionRequest } from '../../types/entities';\r\n\r\nexport class CollectionPostService extends BaseService {\r\n  /**\r\n   * Create a new collection\r\n   */\r\n  async create(data: CreateCollectionRequest): Promise<Collection> {\r\n    this.logApiCall('POST', ApiEndpoints.Collections.Base, data);\r\n    \r\n    // Validate required fields\r\n    this.validateCreateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<Collection>(ApiEndpoints.Collections.Base, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a root collection (Level 1)\r\n   */\r\n  async createRootCollection(\r\n    name: string,\r\n    description: string,\r\n    tags: string[] = [],\r\n    published: boolean = false,\r\n    createdBy: string\r\n  ): Promise<Collection> {\r\n    const data: CreateCollectionRequest = {\r\n      name,\r\n      description,\r\n      level: 1,\r\n      parentCollectionId: undefined,\r\n      childCollectionIds: undefined,\r\n      tags,\r\n      published,\r\n      createdBy,\r\n      images: []\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create a sub-collection (Level 2 or 3)\r\n   */\r\n  async createSubCollection(\r\n    name: string,\r\n    description: string,\r\n    level: 2 | 3,\r\n    parentCollectionId: number,\r\n    tags: string[] = [],\r\n    published: boolean = false,\r\n    createdBy: string\r\n  ): Promise<Collection> {\r\n    const data: CreateCollectionRequest = {\r\n      name,\r\n      description,\r\n      level,\r\n      parentCollectionId,\r\n      childCollectionIds: undefined,\r\n      tags,\r\n      published,\r\n      createdBy,\r\n      images: []\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create multiple collections in batch\r\n   */\r\n  async createBatch(collections: CreateCollectionRequest[]): Promise<Collection[]> {\r\n    this.logApiCall('POST', 'Batch Collections', { count: collections.length });\r\n    \r\n    const promises = collections.map(collection => this.create(collection));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Refresh parent-child relationships for all collections (maintenance operation)\r\n   */\r\n  async refreshAllRelationships(): Promise<{\r\n    success: boolean;\r\n    updatedCount: number;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Collections.RefreshRelationships);\r\n\r\n    try {\r\n      const response = await this.client.post<number>(ApiEndpoints.Collections.RefreshRelationships);\r\n\r\n      if (response.success && response.data !== undefined) {\r\n        return {\r\n          success: true,\r\n          updatedCount: response.data,\r\n          message: response.message || `Updated ${response.data} collection relationships`\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          updatedCount: 0,\r\n          message: response.message || 'Failed to refresh relationships'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error refreshing collection relationships:', error);\r\n      return {\r\n        success: false,\r\n        updatedCount: 0,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate create collection request\r\n   */\r\n  private validateCreateRequest(data: CreateCollectionRequest): void {\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Collection name is required');\r\n    }\r\n\r\n    if (data.name.length > 200) {\r\n      throw new Error('Collection name must be 200 characters or less');\r\n    }\r\n\r\n    if (!data.level || data.level < 1 || data.level > 3) {\r\n      throw new Error('Collection level must be 1, 2, or 3');\r\n    }\r\n\r\n    if (data.level === 1 && data.parentCollectionId) {\r\n      throw new Error('Root collections (level 1) cannot have a parent');\r\n    }\r\n\r\n    if (data.level > 1 && !data.parentCollectionId) {\r\n      throw new Error('Sub-collections (level 2-3) must have a parent');\r\n    }\r\n\r\n    if (!data.createdBy || data.createdBy.trim().length === 0) {\r\n      throw new Error('CreatedBy is required');\r\n    }\r\n\r\n    if (data.createdBy.length > 100) {\r\n      throw new Error('CreatedBy must be 100 characters or less');\r\n    }\r\n\r\n    if (data.description && data.description.length > 1000) {\r\n      throw new Error('Description must be 1000 characters or less');\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const collectionPostService = new CollectionPostService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,8BAA8B,wIAAA,CAAA,cAAW;IACpD;;GAEC,GACD,MAAM,OAAO,IAA6B,EAAuB;QAC/D,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,EAAE;QAEvD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAa,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,EAAE;IAEhE;IAEA;;GAEC,GACD,MAAM,qBACJ,IAAY,EACZ,WAAmB,EACnB,OAAiB,EAAE,EACnB,YAAqB,KAAK,EAC1B,SAAiB,EACI;QACrB,MAAM,OAAgC;YACpC;YACA;YACA,OAAO;YACP,oBAAoB;YACpB,oBAAoB;YACpB;YACA;YACA;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,oBACJ,IAAY,EACZ,WAAmB,EACnB,KAAY,EACZ,kBAA0B,EAC1B,OAAiB,EAAE,EACnB,YAAqB,KAAK,EAC1B,SAAiB,EACI;QACrB,MAAM,OAAgC;YACpC;YACA;YACA;YACA;YACA,oBAAoB;YACpB;YACA;YACA;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,WAAsC,EAAyB;QAC/E,IAAI,CAAC,UAAU,CAAC,QAAQ,qBAAqB;YAAE,OAAO,YAAY,MAAM;QAAC;QAEzE,MAAM,WAAW,YAAY,GAAG,CAAC,CAAA,aAAc,IAAI,CAAC,MAAM,CAAC;QAC3D,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,0BAIH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,oBAAoB;QAErE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAS,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,oBAAoB;YAE7F,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,WAAW;gBACnD,OAAO;oBACL,SAAS;oBACT,cAAc,SAAS,IAAI;oBAC3B,SAAS,SAAS,OAAO,IAAI,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,yBAAyB,CAAC;gBAClF;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,cAAc;oBACd,SAAS,SAAS,OAAO,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,OAAO;gBACL,SAAS;gBACT,cAAc;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA6B,EAAQ;QACjE,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,kBAAkB,EAAE;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,kBAAkB,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Collection, UpdateCollectionRequest } from '../../types/entities';\r\n\r\nexport class CollectionUpdateService extends BaseService {\r\n  /**\r\n   * Update an existing collection\r\n   */\r\n  async update(id: number, data: UpdateCollectionRequest): Promise<Collection> {\r\n    this.logApiCall('PUT', ApiEndpoints.Collections.ById(id), data);\r\n    \r\n    // Validate required fields\r\n    this.validateUpdateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.put<Collection>(ApiEndpoints.Collections.ById(id), data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update collection name and description\r\n   */\r\n  async updateBasicInfo(\r\n    id: number,\r\n    name: string,\r\n    description: string,\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    // First get the current collection to preserve other fields\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const data: UpdateCollectionRequest = {\r\n      name,\r\n      description,\r\n      level: currentCollection.data.level,\r\n      parentCollectionId: currentCollection.data.parentCollectionId,\r\n      childCollectionIds: currentCollection.data.childCollectionIds,\r\n      tags: currentCollection.data.tags,\r\n      published: currentCollection.data.published,\r\n      updatedBy,\r\n      images: []\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update collection tags\r\n   */\r\n  async updateTags(\r\n    id: number,\r\n    tags: string[],\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const data: UpdateCollectionRequest = {\r\n      name: currentCollection.data.name,\r\n      description: currentCollection.data.description,\r\n      level: currentCollection.data.level,\r\n      parentCollectionId: currentCollection.data.parentCollectionId,\r\n      childCollectionIds: currentCollection.data.childCollectionIds,\r\n      tags,\r\n      published: currentCollection.data.published,\r\n      updatedBy,\r\n      images: []\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Publish or unpublish a collection\r\n   */\r\n  async updatePublishStatus(\r\n    id: number,\r\n    published: boolean,\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const data: UpdateCollectionRequest = {\r\n      name: currentCollection.data.name,\r\n      description: currentCollection.data.description,\r\n      level: currentCollection.data.level,\r\n      parentCollectionId: currentCollection.data.parentCollectionId,\r\n      childCollectionIds: currentCollection.data.childCollectionIds,\r\n      tags: currentCollection.data.tags,\r\n      published,\r\n      updatedBy,\r\n      images: []\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Move collection to different parent (change hierarchy)\r\n   */\r\n  async moveToParent(\r\n    id: number,\r\n    newParentId: number | undefined,\r\n    newLevel: number,\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const data: UpdateCollectionRequest = {\r\n      name: currentCollection.data.name,\r\n      description: currentCollection.data.description,\r\n      level: newLevel,\r\n      parentCollectionId: newParentId,\r\n      childCollectionIds: currentCollection.data.childCollectionIds,\r\n      tags: currentCollection.data.tags,\r\n      published: currentCollection.data.published,\r\n      updatedBy,\r\n      images: []\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Add tags to existing collection\r\n   */\r\n  async addTags(\r\n    id: number,\r\n    newTags: string[],\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const existingTags = currentCollection.data.tags || [];\r\n    const uniqueTags = [...new Set([...existingTags, ...newTags])];\r\n\r\n    return this.updateTags(id, uniqueTags, updatedBy);\r\n  }\r\n\r\n  /**\r\n   * Remove tags from existing collection\r\n   */\r\n  async removeTags(\r\n    id: number,\r\n    tagsToRemove: string[],\r\n    updatedBy: string\r\n  ): Promise<Collection> {\r\n    const currentCollection = await this.client.get<Collection>(\r\n      ApiEndpoints.Collections.ById(id)\r\n    );\r\n\r\n    if (!currentCollection.success || !currentCollection.data) {\r\n      throw new Error('Collection not found');\r\n    }\r\n\r\n    const existingTags = currentCollection.data.tags || [];\r\n    const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));\r\n\r\n    return this.updateTags(id, filteredTags, updatedBy);\r\n  }\r\n\r\n  /**\r\n   * Validate update collection request\r\n   */\r\n  private validateUpdateRequest(data: UpdateCollectionRequest): void {\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Collection name is required');\r\n    }\r\n\r\n    if (data.name.length > 200) {\r\n      throw new Error('Collection name must be 200 characters or less');\r\n    }\r\n\r\n    if (!data.level || data.level < 1 || data.level > 3) {\r\n      throw new Error('Collection level must be 1, 2, or 3');\r\n    }\r\n\r\n    if (data.level === 1 && data.parentCollectionId) {\r\n      throw new Error('Root collections (level 1) cannot have a parent');\r\n    }\r\n\r\n    if (data.level > 1 && !data.parentCollectionId) {\r\n      throw new Error('Sub-collections (level 2-3) must have a parent');\r\n    }\r\n\r\n    if (!data.updatedBy || data.updatedBy.trim().length === 0) {\r\n      throw new Error('UpdatedBy is required');\r\n    }\r\n\r\n    if (data.updatedBy.length > 100) {\r\n      throw new Error('UpdatedBy must be 100 characters or less');\r\n    }\r\n\r\n    if (data.description && data.description.length > 1000) {\r\n      throw new Error('Description must be 1000 characters or less');\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const collectionUpdateService = new CollectionUpdateService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,gCAAgC,wIAAA,CAAA,cAAW;IACtD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAA6B,EAAuB;QAC3E,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK;QAE1D,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAa,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK;IAEnE;IAEA;;GAEC,GACD,MAAM,gBACJ,EAAU,EACV,IAAY,EACZ,WAAmB,EACnB,SAAiB,EACI;QACrB,4DAA4D;QAC5D,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC;YACA;YACA,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,WACJ,EAAU,EACV,IAAc,EACd,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D;YACA,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,oBACJ,EAAU,EACV,SAAkB,EAClB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO,kBAAkB,IAAI,CAAC,KAAK;YACnC,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC;YACA;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,aACJ,EAAU,EACV,WAA+B,EAC/B,QAAgB,EAChB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAgC;YACpC,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,aAAa,kBAAkB,IAAI,CAAC,WAAW;YAC/C,OAAO;YACP,oBAAoB;YACpB,oBAAoB,kBAAkB,IAAI,CAAC,kBAAkB;YAC7D,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACjC,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3C;YACA,QAAQ,EAAE;QACZ;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,QACJ,EAAU,EACV,OAAiB,EACjB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;QACtD,MAAM,aAAa;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAQ;SAAE;QAE9D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,YAAY;IACzC;IAEA;;GAEC,GACD,MAAM,WACJ,EAAU,EACV,YAAsB,EACtB,SAAiB,EACI;QACrB,MAAM,oBAAoB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC7C,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAGhC,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;QACtD,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,QAAQ,CAAC;QAEvE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,cAAc;IAC3C;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA6B,EAAQ;QACjE,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,kBAAkB,EAAE;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,KAAK,kBAAkB,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class CollectionDeleteService extends BaseService {\r\n  /**\r\n   * Delete a collection by ID\r\n   */\r\n  async delete(id: number): Promise<boolean> {\r\n    this.logApiCall('DELETE', ApiEndpoints.Collections.ById(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.delete(ApiEndpoints.Collections.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete multiple collections\r\n   */\r\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\r\n    this.logApiCall('DELETE', 'Batch Collections', { count: ids.length });\r\n    \r\n    const promises = ids.map(id => this.delete(id));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Soft delete - unpublish collection instead of deleting\r\n   */\r\n  async unpublish(id: number, updatedBy: string): Promise<boolean> {\r\n    this.logApiCall('PATCH', `Unpublish Collection ${id}`);\r\n    \r\n    try {\r\n      // Import here to avoid circular dependency\r\n      const { collectionUpdateService } = await import('./update');\r\n      await collectionUpdateService.updatePublishStatus(id, false, updatedBy);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Failed to unpublish collection:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if collection can be safely deleted\r\n   */\r\n  async canDelete(id: number): Promise<{\r\n    canDelete: boolean;\r\n    reason?: string;\r\n    hasChildren?: boolean;\r\n    hasProducts?: boolean;\r\n  }> {\r\n    try {\r\n      // Import here to avoid circular dependency\r\n      const { collectionGetService } = await import('./get');\r\n      \r\n      // Check if collection has children\r\n      const children = await collectionGetService.getChildren(id);\r\n      const hasChildren = children.length > 0;\r\n\r\n      // Get collection with products to check if it has products\r\n      const collection = await collectionGetService.getById(id);\r\n      const hasProducts = collection.products && collection.products.length > 0;\r\n\r\n      const canDelete = !hasChildren && !hasProducts;\r\n      \r\n      let reason: string | undefined;\r\n      if (!canDelete) {\r\n        if (hasChildren && hasProducts) {\r\n          reason = 'Collection has both child collections and products';\r\n        } else if (hasChildren) {\r\n          reason = 'Collection has child collections';\r\n        } else if (hasProducts) {\r\n          reason = 'Collection has products';\r\n        }\r\n      }\r\n\r\n      return {\r\n        canDelete,\r\n        reason,\r\n        hasChildren,\r\n        hasProducts\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking if collection can be deleted:', error);\r\n      return {\r\n        canDelete: false,\r\n        reason: 'Error checking collection dependencies'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Safe delete - checks dependencies before deleting\r\n   */\r\n  async safeDelete(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const deleteCheck = await this.canDelete(id);\r\n      \r\n      if (!deleteCheck.canDelete) {\r\n        return {\r\n          success: false,\r\n          message: deleteCheck.reason || 'Collection cannot be deleted'\r\n        };\r\n      }\r\n\r\n      const deleted = await this.delete(id);\r\n      \r\n      if (deleted) {\r\n        return {\r\n          success: true,\r\n          message: 'Collection deleted successfully'\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: 'Failed to delete collection'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during safe delete:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Force delete with cascade (delete children and move products)\r\n   * Note: This should be used with extreme caution\r\n   */\r\n  async forceDelete(\r\n    id: number, \r\n    moveProductsToCollectionId?: number\r\n  ): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCollections?: number[];\r\n    movedProducts?: number;\r\n  }> {\r\n    try {\r\n      const { collectionGetService } = await import('./get');\r\n      const deletedCollections: number[] = [];\r\n      let movedProducts = 0;\r\n\r\n      // Get collection details\r\n      const collection = await collectionGetService.getById(id);\r\n      \r\n      // Move products to another collection if specified\r\n      if (moveProductsToCollectionId && collection.products.length > 0) {\r\n        // This would require product service - placeholder for now\r\n        movedProducts = collection.products.length;\r\n        console.warn('Product moving not implemented - would move', movedProducts, 'products');\r\n      }\r\n\r\n      // Delete children recursively\r\n      const children = await collectionGetService.getChildren(id);\r\n      for (const child of children) {\r\n        const childResult = await this.forceDelete(child.id, moveProductsToCollectionId);\r\n        if (childResult.success && childResult.deletedCollections) {\r\n          deletedCollections.push(...childResult.deletedCollections);\r\n        }\r\n      }\r\n\r\n      // Delete the collection itself\r\n      const deleted = await this.delete(id);\r\n      if (deleted) {\r\n        deletedCollections.push(id);\r\n      }\r\n\r\n      return {\r\n        success: deleted,\r\n        message: deleted \r\n          ? `Successfully deleted collection and ${deletedCollections.length - 1} child collections`\r\n          : 'Failed to delete collection',\r\n        deletedCollections,\r\n        movedProducts\r\n      };\r\n    } catch (error) {\r\n      console.error('Error during force delete:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const collectionDeleteService = new CollectionDeleteService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,gCAAgC,wIAAA,CAAA,cAAW;IACtD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;QAExD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI,CAAC;IAErD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,qBAAqB;YAAE,OAAO,IAAI,MAAM;QAAC;QAEnE,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAAE,SAAiB,EAAoB;QAC/D,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,qBAAqB,EAAE,IAAI;QAErD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,uBAAuB,EAAE,GAAG;YACpC,MAAM,wBAAwB,mBAAmB,CAAC,IAAI,OAAO;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,mCAAmC;YACnC,MAAM,WAAW,MAAM,qBAAqB,WAAW,CAAC;YACxD,MAAM,cAAc,SAAS,MAAM,GAAG;YAEtC,2DAA2D;YAC3D,MAAM,aAAa,MAAM,qBAAqB,OAAO,CAAC;YACtD,MAAM,cAAc,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG;YAExE,MAAM,YAAY,CAAC,eAAe,CAAC;YAEnC,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,IAAI,eAAe,aAAa;oBAC9B,SAAS;gBACX,OAAO,IAAI,aAAa;oBACtB,SAAS;gBACX,OAAO,IAAI,aAAa;oBACtB,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;;GAGC,GACD,MAAM,YACJ,EAAU,EACV,0BAAmC,EAMlC;QACD,IAAI;YACF,MAAM,EAAE,oBAAoB,EAAE,GAAG;YACjC,MAAM,qBAA+B,EAAE;YACvC,IAAI,gBAAgB;YAEpB,yBAAyB;YACzB,MAAM,aAAa,MAAM,qBAAqB,OAAO,CAAC;YAEtD,mDAAmD;YACnD,IAAI,8BAA8B,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAChE,2DAA2D;gBAC3D,gBAAgB,WAAW,QAAQ,CAAC,MAAM;gBAC1C,QAAQ,IAAI,CAAC,+CAA+C,eAAe;YAC7E;YAEA,8BAA8B;YAC9B,MAAM,WAAW,MAAM,qBAAqB,WAAW,CAAC;YACxD,KAAK,MAAM,SAAS,SAAU;gBAC5B,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;gBACrD,IAAI,YAAY,OAAO,IAAI,YAAY,kBAAkB,EAAE;oBACzD,mBAAmB,IAAI,IAAI,YAAY,kBAAkB;gBAC3D;YACF;YAEA,+BAA+B;YAC/B,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAClC,IAAI,SAAS;gBACX,mBAAmB,IAAI,CAAC;YAC1B;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS,UACL,CAAC,oCAAoC,EAAE,mBAAmB,MAAM,GAAG,EAAE,kBAAkB,CAAC,GACxF;gBACJ;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,0BAA0B,IAAI", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/collections/index.ts"], "sourcesContent": ["// Collection Services\r\nexport { CollectionGetService, collectionGetService } from './get';\r\nexport { CollectionPostService, collectionPostService } from './post';\r\nexport { CollectionUpdateService, collectionUpdateService } from './update';\r\nexport { CollectionDeleteService, collectionDeleteService } from './delete';\r\n\r\n// Combined Collection Service\r\nimport { collectionGetService } from './get';\r\nimport { collectionPostService } from './post';\r\nimport { collectionUpdateService } from './update';\r\nimport { collectionDeleteService } from './delete';\r\n\r\nexport class CollectionService {\r\n  get = collectionGetService;\r\n  post = collectionPostService;\r\n  update = collectionUpdateService;\r\n  delete = collectionDeleteService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const collectionService = new CollectionService();\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AACtB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,4IAAA,CAAA,uBAAoB,CAAC;IAC3B,OAAO,6IAAA,CAAA,wBAAqB,CAAC;IAC7B,SAAS,+IAAA,CAAA,0BAAuB,CAAC;IACjC,SAAS,+IAAA,CAAA,0BAAuB,CAAC;AACnC;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints, PaginatedResponse } from '../../config/apiConfig';\r\nimport { \r\n  Product, \r\n  ProductSummary, \r\n  ProductFilterRequest,\r\n   \r\n} from '../../types/entities';\r\n\r\nexport class ProductGetService extends BaseService {\r\n  /**\r\n   * Get all products\r\n   */\r\n  async getAll(): Promise<Product[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.Base);\r\n    return this.handleResponse(\r\n      this.client.get<Product[]>(ApiEndpoints.Products.Base)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get product by ID\r\n   */\r\n  async getById(id: number): Promise<Product> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.ById(id));\r\n    return this.handleResponse(\r\n      this.client.get<Product>(ApiEndpoints.Products.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get products by collection ID\r\n   */\r\n  async getByCollection(collectionId: number): Promise<Product[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.ByCollection(collectionId));\r\n    return this.handleResponse(\r\n      this.client.get<Product[]>(ApiEndpoints.Products.ByCollection(collectionId))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get products in stock\r\n   */\r\n  async getInStock(): Promise<Product[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.InStock);\r\n    return this.handleResponse(\r\n      this.client.get<Product[]>(ApiEndpoints.Products.InStock)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get featured products\r\n   */\r\n  async getFeatured(count: number = 10): Promise<ProductSummary[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.Featured, { count });\r\n    return this.handleResponse(\r\n      this.client.get<ProductSummary[]>(ApiEndpoints.Products.Featured, { count })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get latest products\r\n   */\r\n  async getLatest(count: number = 10): Promise<ProductSummary[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.Latest, { count });\r\n    return this.handleResponse(\r\n      this.client.get<ProductSummary[]>(ApiEndpoints.Products.Latest, { count })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Search products by name\r\n   */\r\n  async search(name: string): Promise<Product[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.Search, { name });\r\n    return this.handleResponse(\r\n      this.client.get<Product[]>(ApiEndpoints.Products.Search, { name })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get products by price range\r\n   */\r\n  async getByPriceRange(minPrice: number, maxPrice: number): Promise<Product[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Products.PriceRange, { minPrice, maxPrice });\r\n    return this.handleResponse(\r\n      this.client.get<Product[]>(ApiEndpoints.Products.PriceRange, { minPrice, maxPrice })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get products with advanced filtering and pagination\r\n   */\r\n  async getFiltered(filters: ProductFilterRequest): Promise<PaginatedResponse<Product>> {\r\n    const cleanFilters = ServiceUtils.cleanObject(filters);\r\n    this.logApiCall('GET', ApiEndpoints.Products.Filter, cleanFilters);\r\n    \r\n    return this.handlePaginatedResponse(\r\n      this.client.get<PaginatedResponse<Product>>(\r\n        ApiEndpoints.Products.Filter, \r\n        cleanFilters\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get products with default pagination\r\n   */\r\n  async getPaginated(\r\n    pageNumber: number = 1, \r\n    pageSize: number = 10,\r\n    sortBy: string = 'createdAt',\r\n    sortDirection: 'asc' | 'desc' = 'desc'\r\n  ): Promise<PaginatedResponse<Product>> {\r\n    const filters: ProductFilterRequest = {\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy,\r\n      sortDirection\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get products by tag\r\n   */\r\n  async getByTag(tag: string): Promise<Product[]> {\r\n    const filters: ProductFilterRequest = {\r\n      tag,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get low stock products\r\n   */\r\n  async getLowStock(threshold: number = 10): Promise<Product[]> {\r\n    const filters: ProductFilterRequest = {\r\n      minStock: 1,\r\n      maxStock: threshold,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get out of stock products\r\n   */\r\n  async getOutOfStock(): Promise<Product[]> {\r\n    const filters: ProductFilterRequest = {\r\n      inStock: false,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get products in price range with pagination\r\n   */\r\n  async getByPriceRangePaginated(\r\n    minPrice: number, \r\n    maxPrice: number,\r\n    pageNumber: number = 1,\r\n    pageSize: number = 10\r\n  ): Promise<PaginatedResponse<Product>> {\r\n    const filters: ProductFilterRequest = {\r\n      minPrice,\r\n      maxPrice,\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy: 'price',\r\n      sortDirection: 'asc'\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get products created within date range\r\n   */\r\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Product[]> {\r\n    const filters: ProductFilterRequest = {\r\n      createdAfter: ServiceUtils.formatDate(startDate),\r\n      createdBefore: ServiceUtils.formatDate(endDate),\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get product recommendations based on collection\r\n   */\r\n  async getRecommendations(productId: number, count: number = 5): Promise<Product[]> {\r\n    try {\r\n      const product = await this.getById(productId);\r\n      const relatedProducts = await this.getByCollection(product.collectionId);\r\n      \r\n      // Filter out the current product and return limited results\r\n      return relatedProducts\r\n        .filter(p => p.id !== productId)\r\n        .slice(0, count);\r\n    } catch (error) {\r\n      console.error('Error getting product recommendations:', error);\r\n      return [];\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const productGetService = new ProductGetService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,SAA6B;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI;QACjD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI;IAEzD;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAoB;QAC1C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;IAExD;IAEA;;GAEC,GACD,MAAM,gBAAgB,YAAoB,EAAsB;QAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;QAC1D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;IAElE;IAEA;;GAEC,GACD,MAAM,aAAiC;QACrC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;IAE5D;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAE,EAA6B;QAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAAE;QAAM;QAC/D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAmB,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE;YAAE;QAAM;IAE9E;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAA6B;QAC7D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAM;QAC7D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAmB,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAM;IAE5E;IAEA;;GAEC,GACD,MAAM,OAAO,IAAY,EAAsB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAK;QAC5D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;YAAE;QAAK;IAEpE;IAEA;;GAEC,GACD,MAAM,gBAAgB,QAAgB,EAAE,QAAgB,EAAsB;QAC5E,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,UAAU,EAAE;YAAE;YAAU;QAAS;QAC9E,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAY,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,UAAU,EAAE;YAAE;YAAU;QAAS;IAEtF;IAEA;;GAEC,GACD,MAAM,YAAY,OAA6B,EAAuC;QACpF,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAAE;QAErD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,MAAM,EAC5B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACD;QACrC,MAAM,UAAgC;YACpC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,SAAS,GAAW,EAAsB;QAC9C,MAAM,UAAgC;YACpC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,YAAY,YAAoB,EAAE,EAAsB;QAC5D,MAAM,UAAgC;YACpC,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,gBAAoC;QACxC,MAAM,UAAgC;YACpC,SAAS;YACT,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,yBACJ,QAAgB,EAChB,QAAgB,EAChB,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACgB;QACrC,MAAM,UAAgC;YACpC;YACA;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAsB;QACvE,MAAM,UAAgC;YACpC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,mBAAmB,SAAiB,EAAE,QAAgB,CAAC,EAAsB;QACjF,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,OAAO,CAAC;YACnC,MAAM,kBAAkB,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,YAAY;YAEvE,4DAA4D;YAC5D,OAAO,gBACJ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WACrB,KAAK,CAAC,GAAG;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Product, CreateProductRequest } from '../../types/entities';\r\n\r\nexport class ProductPostService extends BaseService {\r\n  /**\r\n   * Create a new product\r\n   */\r\n  async create(data: CreateProductRequest): Promise<Product> {\r\n    this.logApiCall('POST', ApiEndpoints.Products.Base, data);\r\n    \r\n    // Validate required fields\r\n    this.validateCreateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<Product>(ApiEndpoints.Products.Base, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a simple product with minimal data\r\n   */\r\n  async createSimple(\r\n    name: string,\r\n    description: string,\r\n    price: number,\r\n    stock: number,\r\n    collectionId: number,\r\n    images: string[] = [],\r\n    tags: string[] = []\r\n  ): Promise<Product> {\r\n    const data: CreateProductRequest = {\r\n      name,\r\n      description,\r\n      price,\r\n      stock,\r\n      collectionId,\r\n      images,\r\n      tags\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create multiple products in batch\r\n   */\r\n  async createBatch(products: CreateProductRequest[]): Promise<Product[]> {\r\n    this.logApiCall('POST', 'Batch Products', { count: products.length });\r\n    \r\n    const promises = products.map(product => this.create(product));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Create product with image upload\r\n   */\r\n  async createWithImages(\r\n    productData: Omit<CreateProductRequest, 'images'>,\r\n    imageFiles: File[]\r\n  ): Promise<Product> {\r\n    try {\r\n      // First upload images (this would need an image upload service)\r\n      const imageUrls = await this.uploadImages(imageFiles);\r\n      \r\n      // Then create product with image URLs\r\n      const data: CreateProductRequest = {\r\n        ...productData,\r\n        images: imageUrls\r\n      };\r\n\r\n      return this.create(data);\r\n    } catch (error) {\r\n      console.error('Error creating product with images:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Duplicate an existing product\r\n   */\r\n  async duplicate(\r\n    originalProductId: number,\r\n    newName: string,\r\n    modifications: Partial<CreateProductRequest> = {}\r\n  ): Promise<Product> {\r\n    try {\r\n      // Get the original product\r\n      const originalResponse = await this.client.get<Product>(\r\n        ApiEndpoints.Products.ById(originalProductId)\r\n      );\r\n\r\n      if (!originalResponse.success || !originalResponse.data) {\r\n        throw new Error('Original product not found');\r\n      }\r\n\r\n      const original = originalResponse.data;\r\n\r\n      // Create new product data based on original\r\n      const data: CreateProductRequest = {\r\n        name: newName,\r\n        description: original.description,\r\n        price: original.price,\r\n        stock: 0, // Start with 0 stock for duplicated products\r\n        collectionId: original.collectionId,\r\n        images: [...original.images], // Copy images array\r\n        tags: [...original.tags], // Copy tags array\r\n        ...modifications // Apply any modifications\r\n      };\r\n\r\n      return this.create(data);\r\n    } catch (error) {\r\n      console.error('Error duplicating product:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create product variant (similar product with different attributes)\r\n   */\r\n  async createVariant(\r\n    baseProductId: number,\r\n    variantName: string,\r\n    priceAdjustment: number = 0,\r\n    modifications: Partial<CreateProductRequest> = {}\r\n  ): Promise<Product> {\r\n    try {\r\n      const baseResponse = await this.client.get<Product>(\r\n        ApiEndpoints.Products.ById(baseProductId)\r\n      );\r\n\r\n      if (!baseResponse.success || !baseResponse.data) {\r\n        throw new Error('Base product not found');\r\n      }\r\n\r\n      const base = baseResponse.data;\r\n\r\n      const data: CreateProductRequest = {\r\n        name: `${base.name} - ${variantName}`,\r\n        description: base.description,\r\n        price: base.price + priceAdjustment,\r\n        stock: 0,\r\n        collectionId: base.collectionId,\r\n        images: [...base.images],\r\n        tags: [...base.tags, 'variant'],\r\n        ...modifications\r\n      };\r\n\r\n      return this.create(data);\r\n    } catch (error) {\r\n      console.error('Error creating product variant:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Upload product images (placeholder - would need actual image upload service)\r\n   */\r\n  private async uploadImages(files: File[]): Promise<string[]> {\r\n    // This is a placeholder implementation\r\n    // In a real application, you would upload to a cloud storage service\r\n    const imageUrls: string[] = [];\r\n    \r\n    for (const file of files) {\r\n      // Simulate upload process\r\n      const formData = new FormData();\r\n      formData.append('image', file);\r\n      \r\n      try {\r\n        // This would be your actual image upload endpoint\r\n        // const response = await this.client.upload('/upload/image', formData);\r\n        // imageUrls.push(response.data.url);\r\n        \r\n        // For now, just create a placeholder URL\r\n        imageUrls.push(`/images/products/${Date.now()}-${file.name}`);\r\n      } catch (error) {\r\n        console.error('Error uploading image:', error);\r\n        throw new Error(`Failed to upload image: ${file.name}`);\r\n      }\r\n    }\r\n    \r\n    return imageUrls;\r\n  }\r\n\r\n  /**\r\n   * Validate create product request\r\n   */\r\n  private validateCreateRequest(data: CreateProductRequest): void {\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Product name is required');\r\n    }\r\n\r\n    if (data.name.length > 200) {\r\n      throw new Error('Product name must be 200 characters or less');\r\n    }\r\n\r\n    if (data.price <= 0) {\r\n      throw new Error('Product price must be greater than 0');\r\n    }\r\n\r\n    if (data.stock < 0) {\r\n      throw new Error('Product stock cannot be negative');\r\n    }\r\n\r\n    if (!data.collectionId) {\r\n      throw new Error('Collection ID is required');\r\n    }\r\n\r\n    if (data.description && data.description.length > 1000) {\r\n      throw new Error('Description must be 1000 characters or less');\r\n    }\r\n\r\n    // Validate image URLs\r\n    if (data.images && data.images.length > 0) {\r\n      for (const image of data.images) {\r\n        if (!this.isValidImageUrl(image)) {\r\n          throw new Error(`Invalid image URL: ${image}`);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate image URL format\r\n   */\r\n  private isValidImageUrl(url: string): boolean {\r\n    try {\r\n      new URL(url);\r\n      return true;\r\n    } catch {\r\n      // If not a valid URL, check if it's a relative path\r\n      return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const productPostService = new ProductPostService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,OAAO,IAA0B,EAAoB;QACzD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,EAAE;QAEpD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,EAAE;IAE1D;IAEA;;GAEC,GACD,MAAM,aACJ,IAAY,EACZ,WAAmB,EACnB,KAAa,EACb,KAAa,EACb,YAAoB,EACpB,SAAmB,EAAE,EACrB,OAAiB,EAAE,EACD;QAClB,MAAM,OAA6B;YACjC;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgC,EAAsB;QACtE,IAAI,CAAC,UAAU,CAAC,QAAQ,kBAAkB;YAAE,OAAO,SAAS,MAAM;QAAC;QAEnE,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,UAAW,IAAI,CAAC,MAAM,CAAC;QACrD,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,WAAiD,EACjD,UAAkB,EACA;QAClB,IAAI;YACF,gEAAgE;YAChE,MAAM,YAAY,MAAM,IAAI,CAAC,YAAY,CAAC;YAE1C,sCAAsC;YACtC,MAAM,OAA6B;gBACjC,GAAG,WAAW;gBACd,QAAQ;YACV;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UACJ,iBAAyB,EACzB,OAAe,EACf,gBAA+C,CAAC,CAAC,EAC/B;QAClB,IAAI;YACF,2BAA2B;YAC3B,MAAM,mBAAmB,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAC5C,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAG7B,IAAI,CAAC,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,IAAI,EAAE;gBACvD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,iBAAiB,IAAI;YAEtC,4CAA4C;YAC5C,MAAM,OAA6B;gBACjC,MAAM;gBACN,aAAa,SAAS,WAAW;gBACjC,OAAO,SAAS,KAAK;gBACrB,OAAO;gBACP,cAAc,SAAS,YAAY;gBACnC,QAAQ;uBAAI,SAAS,MAAM;iBAAC;gBAC5B,MAAM;uBAAI,SAAS,IAAI;iBAAC;gBACxB,GAAG,cAAc,0BAA0B;YAC7C;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,cACJ,aAAqB,EACrB,WAAmB,EACnB,kBAA0B,CAAC,EAC3B,gBAA+C,CAAC,CAAC,EAC/B;QAClB,IAAI;YACF,MAAM,eAAe,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CACxC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;YAG7B,IAAI,CAAC,aAAa,OAAO,IAAI,CAAC,aAAa,IAAI,EAAE;gBAC/C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,aAAa,IAAI;YAE9B,MAAM,OAA6B;gBACjC,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,aAAa;gBACrC,aAAa,KAAK,WAAW;gBAC7B,OAAO,KAAK,KAAK,GAAG;gBACpB,OAAO;gBACP,cAAc,KAAK,YAAY;gBAC/B,QAAQ;uBAAI,KAAK,MAAM;iBAAC;gBACxB,MAAM;uBAAI,KAAK,IAAI;oBAAE;iBAAU;gBAC/B,GAAG,aAAa;YAClB;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAc,aAAa,KAAa,EAAqB;QAC3D,uCAAuC;QACvC,qEAAqE;QACrE,MAAM,YAAsB,EAAE;QAE9B,KAAK,MAAM,QAAQ,MAAO;YACxB,0BAA0B;YAC1B,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,IAAI;gBACF,kDAAkD;gBAClD,wEAAwE;gBACxE,qCAAqC;gBAErC,yCAAyC;gBACzC,UAAU,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACxD;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA0B,EAAQ;QAC9D,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,IAAI,GAAG;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,GAAG;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,sBAAsB;QACtB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;YACzC,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ;oBAChC,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,OAAO;gBAC/C;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,GAAW,EAAW;QAC5C,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,oDAAoD;YACpD,OAAO,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC;QACvE;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Product, UpdateProductRequest } from '../../types/entities';\r\n\r\nexport class ProductUpdateService extends BaseService {\r\n  /**\r\n   * Update an existing product\r\n   */\r\n  async update(id: number, data: UpdateProductRequest): Promise<Product> {\r\n    this.logApiCall('PUT', ApiEndpoints.Products.ById(id), data);\r\n    \r\n    // Validate required fields\r\n    this.validateUpdateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.put<Product>(ApiEndpoints.Products.ById(id), data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update product stock\r\n   */\r\n  async updateStock(id: number, newStock: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', ApiEndpoints.Products.UpdateStock(id), { stock: newStock });\r\n    \r\n    if (newStock < 0) {\r\n      throw new Error('Stock cannot be negative');\r\n    }\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.patch(ApiEndpoints.Products.UpdateStock(id), newStock)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update product price\r\n   */\r\n  async updatePrice(id: number, newPrice: number): Promise<Product> {\r\n    if (newPrice <= 0) {\r\n      throw new Error('Price must be greater than 0');\r\n    }\r\n\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    \r\n    const data: UpdateProductRequest = {\r\n      ...this.mapProductToUpdateRequest(currentProduct),\r\n      price: newPrice\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update product basic information\r\n   */\r\n  async updateBasicInfo(\r\n    id: number,\r\n    name: string,\r\n    description: string\r\n  ): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    \r\n    const data: UpdateProductRequest = {\r\n      ...this.mapProductToUpdateRequest(currentProduct),\r\n      name,\r\n      description\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update product images\r\n   */\r\n  async updateImages(id: number, images: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    \r\n    const data: UpdateProductRequest = {\r\n      ...this.mapProductToUpdateRequest(currentProduct),\r\n      images\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Add images to product\r\n   */\r\n  async addImages(id: number, newImages: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const existingImages = currentProduct.images || [];\r\n    const allImages = [...existingImages, ...newImages];\r\n\r\n    return this.updateImages(id, allImages);\r\n  }\r\n\r\n  /**\r\n   * Remove images from product\r\n   */\r\n  async removeImages(id: number, imagesToRemove: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const existingImages = currentProduct.images || [];\r\n    const filteredImages = existingImages.filter(img => !imagesToRemove.includes(img));\r\n\r\n    return this.updateImages(id, filteredImages);\r\n  }\r\n\r\n  /**\r\n   * Update product tags\r\n   */\r\n  async updateTags(id: number, tags: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    \r\n    const data: UpdateProductRequest = {\r\n      ...this.mapProductToUpdateRequest(currentProduct),\r\n      tags\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Add tags to product\r\n   */\r\n  async addTags(id: number, newTags: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const existingTags = currentProduct.tags || [];\r\n    const uniqueTags = [...new Set([...existingTags, ...newTags])];\r\n\r\n    return this.updateTags(id, uniqueTags);\r\n  }\r\n\r\n  /**\r\n   * Remove tags from product\r\n   */\r\n  async removeTags(id: number, tagsToRemove: string[]): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const existingTags = currentProduct.tags || [];\r\n    const filteredTags = existingTags.filter(tag => !tagsToRemove.includes(tag));\r\n\r\n    return this.updateTags(id, filteredTags);\r\n  }\r\n\r\n  /**\r\n   * Move product to different collection\r\n   */\r\n  async moveToCollection(id: number, newCollectionId: number): Promise<Product> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    \r\n    const data: UpdateProductRequest = {\r\n      ...this.mapProductToUpdateRequest(currentProduct),\r\n      collectionId: newCollectionId\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Adjust stock (add or subtract)\r\n   */\r\n  async adjustStock(id: number, adjustment: number): Promise<boolean> {\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const newStock = currentProduct.stock + adjustment;\r\n    \r\n    if (newStock < 0) {\r\n      throw new Error('Stock adjustment would result in negative stock');\r\n    }\r\n\r\n    return this.updateStock(id, newStock);\r\n  }\r\n\r\n  /**\r\n   * Increase stock\r\n   */\r\n  async increaseStock(id: number, amount: number): Promise<boolean> {\r\n    if (amount <= 0) {\r\n      throw new Error('Amount must be positive');\r\n    }\r\n    return this.adjustStock(id, amount);\r\n  }\r\n\r\n  /**\r\n   * Decrease stock\r\n   */\r\n  async decreaseStock(id: number, amount: number): Promise<boolean> {\r\n    if (amount <= 0) {\r\n      throw new Error('Amount must be positive');\r\n    }\r\n    return this.adjustStock(id, -amount);\r\n  }\r\n\r\n  /**\r\n   * Apply discount to product price\r\n   */\r\n  async applyDiscount(\r\n    id: number, \r\n    discountPercentage: number\r\n  ): Promise<Product> {\r\n    if (discountPercentage < 0 || discountPercentage > 100) {\r\n      throw new Error('Discount percentage must be between 0 and 100');\r\n    }\r\n\r\n    const currentProduct = await this.getCurrentProduct(id);\r\n    const discountedPrice = currentProduct.price * (1 - discountPercentage / 100);\r\n    \r\n    return this.updatePrice(id, Math.round(discountedPrice * 100) / 100);\r\n  }\r\n\r\n  /**\r\n   * Get current product data\r\n   */\r\n  private async getCurrentProduct(id: number): Promise<Product> {\r\n    const response = await this.client.get<Product>(ApiEndpoints.Products.ById(id));\r\n    \r\n    if (!response.success || !response.data) {\r\n      throw new Error('Product not found');\r\n    }\r\n    \r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Map Product to UpdateProductRequest\r\n   */\r\n  private mapProductToUpdateRequest(product: Product): UpdateProductRequest {\r\n    return {\r\n      name: product.name,\r\n      description: product.description,\r\n      price: product.price,\r\n      stock: product.stock,\r\n      collectionId: product.collectionId,\r\n      images: product.images,\r\n      tags: product.tags\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Validate update product request\r\n   */\r\n  private validateUpdateRequest(data: UpdateProductRequest): void {\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Product name is required');\r\n    }\r\n\r\n    if (data.name.length > 200) {\r\n      throw new Error('Product name must be 200 characters or less');\r\n    }\r\n\r\n    if (data.price <= 0) {\r\n      throw new Error('Product price must be greater than 0');\r\n    }\r\n\r\n    if (data.stock < 0) {\r\n      throw new Error('Product stock cannot be negative');\r\n    }\r\n\r\n    if (!data.collectionId) {\r\n      throw new Error('Collection ID is required');\r\n    }\r\n\r\n    if (data.description && data.description.length > 1000) {\r\n      throw new Error('Description must be 1000 characters or less');\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const productUpdateService = new ProductUpdateService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAA0B,EAAoB;QACrE,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;QAEvD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,QAAgB,EAAoB;QAChE,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK;YAAE,OAAO;QAAS;QAElF,IAAI,WAAW,GAAG;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,QAAgB,EAAoB;QAChE,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD,OAAO;QACT;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,gBACJ,EAAU,EACV,IAAY,EACZ,WAAmB,EACD;QAClB,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,MAAgB,EAAoB;QACjE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAAE,SAAmB,EAAoB;QACjE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,iBAAiB,eAAe,MAAM,IAAI,EAAE;QAClD,MAAM,YAAY;eAAI;eAAmB;SAAU;QAEnD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IAC/B;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,cAAwB,EAAoB;QACzE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,iBAAiB,eAAe,MAAM,IAAI,EAAE;QAClD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,MAAO,CAAC,eAAe,QAAQ,CAAC;QAE7E,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IAC/B;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,IAAc,EAAoB;QAC7D,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAE,OAAiB,EAAoB;QAC7D,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,eAAe,eAAe,IAAI,IAAI,EAAE;QAC9C,MAAM,aAAa;eAAI,IAAI,IAAI;mBAAI;mBAAiB;aAAQ;SAAE;QAE9D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,YAAsB,EAAoB;QACrE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,eAAe,eAAe,IAAI,IAAI,EAAE;QAC9C,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,CAAC,aAAa,QAAQ,CAAC;QAEvE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAE,eAAuB,EAAoB;QAC5E,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,MAAM,OAA6B;YACjC,GAAG,IAAI,CAAC,yBAAyB,CAAC,eAAe;YACjD,cAAc;QAChB;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,YAAY,EAAU,EAAE,UAAkB,EAAoB;QAClE,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,WAAW,eAAe,KAAK,GAAG;QAExC,IAAI,WAAW,GAAG;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,MAAc,EAAoB;QAChE,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAC9B;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAE,MAAc,EAAoB;QAChE,IAAI,UAAU,GAAG;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC/B;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,kBAA0B,EACR;QAClB,IAAI,qBAAqB,KAAK,qBAAqB,KAAK;YACtD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QACpD,MAAM,kBAAkB,eAAe,KAAK,GAAG,CAAC,IAAI,qBAAqB,GAAG;QAE5E,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,KAAK,CAAC,kBAAkB,OAAO;IAClE;IAEA;;GAEC,GACD,MAAc,kBAAkB,EAAU,EAAoB;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE3E,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,AAAQ,0BAA0B,OAAgB,EAAwB;QACxE,OAAO;YACL,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,cAAc,QAAQ,YAAY;YAClC,QAAQ,QAAQ,MAAM;YACtB,MAAM,QAAQ,IAAI;QACpB;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAA0B,EAAQ;QAC9D,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,IAAI,GAAG;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,GAAG,GAAG;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/delete.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class ProductDeleteService extends BaseService {\r\n  /**\r\n   * Delete a product by ID\r\n   */\r\n  async delete(id: number): Promise<boolean> {\r\n    this.logApiCall('DELETE', ApiEndpoints.Products.ById(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.delete(ApiEndpoints.Products.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete multiple products\r\n   */\r\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\r\n    this.logApiCall('DELETE', 'Batch Products', { count: ids.length });\r\n    \r\n    const promises = ids.map(id => this.delete(id));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Soft delete - set stock to 0 instead of deleting\r\n   */\r\n  async softDelete(id: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', `Soft Delete Product ${id}`);\r\n    \r\n    try {\r\n      // Import here to avoid circular dependency\r\n      const { productUpdateService } = await import('./update');\r\n      await productUpdateService.updateStock(id, 0);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Failed to soft delete product:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if product can be safely deleted\r\n   */\r\n  async canDelete(id: number): Promise<{\r\n    canDelete: boolean;\r\n    reason?: string;\r\n    hasOrders?: boolean;\r\n    isInActiveOrders?: boolean;\r\n  }> {\r\n    try {\r\n      // This would require checking if product is in any orders\r\n      // For now, we'll assume it can be deleted\r\n      // In a real implementation, you'd check:\r\n      // 1. If product is in any pending orders\r\n      // 2. If product is in any active shopping carts\r\n      // 3. If product has any dependencies\r\n      \r\n      return {\r\n        canDelete: true,\r\n        hasOrders: false,\r\n        isInActiveOrders: false\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking if product can be deleted:', error);\r\n      return {\r\n        canDelete: false,\r\n        reason: 'Error checking product dependencies'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Safe delete - checks dependencies before deleting\r\n   */\r\n  async safeDelete(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const deleteCheck = await this.canDelete(id);\r\n      \r\n      if (!deleteCheck.canDelete) {\r\n        return {\r\n          success: false,\r\n          message: deleteCheck.reason || 'Product cannot be deleted'\r\n        };\r\n      }\r\n\r\n      const deleted = await this.delete(id);\r\n      \r\n      if (deleted) {\r\n        return {\r\n          success: true,\r\n          message: 'Product deleted successfully'\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: 'Failed to delete product'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during safe delete:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Archive product (soft delete with archive flag)\r\n   */\r\n  async archive(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      // Set stock to 0 and add archive tag\r\n      const { productUpdateService } = await import('./update');\r\n      \r\n      await productUpdateService.updateStock(id, 0);\r\n      await productUpdateService.addTags(id, ['archived']);\r\n      \r\n      return {\r\n        success: true,\r\n        message: 'Product archived successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error archiving product:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to archive product'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Restore archived product\r\n   */\r\n  async restore(id: number, newStock: number = 1): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const { productUpdateService } = await import('./update');\r\n      \r\n      await productUpdateService.updateStock(id, newStock);\r\n      await productUpdateService.removeTags(id, ['archived']);\r\n      \r\n      return {\r\n        success: true,\r\n        message: 'Product restored successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error restoring product:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to restore product'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete all products in a collection\r\n   */\r\n  async deleteByCollection(collectionId: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n  }> {\r\n    try {\r\n      // Get all products in the collection\r\n      const { productGetService } = await import('./get');\r\n      const products = await productGetService.getByCollection(collectionId);\r\n      \r\n      if (products.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: 'No products found in collection',\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      // Delete all products\r\n      const productIds = products.map(p => p.id);\r\n      const results = await this.deleteBatch(productIds);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === products.length,\r\n        message: `Deleted ${deletedCount} of ${products.length} products`,\r\n        deletedCount\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting products by collection:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete products',\r\n        deletedCount: 0\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete out of stock products\r\n   */\r\n  async deleteOutOfStock(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n  }> {\r\n    try {\r\n      const { productGetService } = await import('./get');\r\n      const outOfStockProducts = await productGetService.getOutOfStock();\r\n      \r\n      if (outOfStockProducts.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: 'No out of stock products found',\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      const productIds = outOfStockProducts.map(p => p.id);\r\n      const results = await this.deleteBatch(productIds);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === outOfStockProducts.length,\r\n        message: `Deleted ${deletedCount} out of stock products`,\r\n        deletedCount\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting out of stock products:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete out of stock products',\r\n        deletedCount: 0\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const productDeleteService = new ProductDeleteService();\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AAEO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;QAErD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;IAElD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,kBAAkB;YAAE,OAAO,IAAI,MAAM;QAAC;QAEhE,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI;QAEpD,IAAI;YACF,2CAA2C;YAC3C,MAAM,EAAE,oBAAoB,EAAE,GAAG;YACjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,0DAA0D;YAC1D,0CAA0C;YAC1C,yCAAyC;YACzC,yCAAyC;YACzC,gDAAgD;YAChD,qCAAqC;YAErC,OAAO;gBACL,WAAW;gBACX,WAAW;gBACX,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,qCAAqC;YACrC,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,MAAM,qBAAqB,OAAO,CAAC,IAAI;gBAAC;aAAW;YAEnD,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAE,WAAmB,CAAC,EAG3C;QACD,IAAI;YACF,MAAM,EAAE,oBAAoB,EAAE,GAAG;YAEjC,MAAM,qBAAqB,WAAW,CAAC,IAAI;YAC3C,MAAM,qBAAqB,UAAU,CAAC,IAAI;gBAAC;aAAW;YAEtD,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,YAAoB,EAI1C;QACD,IAAI;YACF,qCAAqC;YACrC,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,WAAW,MAAM,kBAAkB,eAAe,CAAC;YAEzD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,sBAAsB;YACtB,MAAM,aAAa,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACzC,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,SAAS,MAAM;gBACzC,SAAS,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;gBACjE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAIH;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,qBAAqB,MAAM,kBAAkB,aAAa;YAEhE,IAAI,mBAAmB,MAAM,KAAK,GAAG;gBACnC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,aAAa,mBAAmB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACnD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,mBAAmB,MAAM;gBACnD,SAAS,CAAC,QAAQ,EAAE,aAAa,sBAAsB,CAAC;gBACxD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/products/index.ts"], "sourcesContent": ["// Product Services\r\nexport { ProductGetService, productGetService } from './get';\r\nexport { ProductPostService, productPostService } from './post';\r\nexport { ProductUpdateService, productUpdateService } from './update';\r\nexport { ProductDeleteService, productDeleteService } from './delete';\r\n\r\n// Combined Product Service\r\nimport { productGetService } from './get';\r\nimport { productPostService } from './post';\r\nimport { productUpdateService } from './update';\r\nimport { productDeleteService } from './delete';\r\n\r\nexport class ProductService {\r\n  get = productGetService;\r\n  post = productPostService;\r\n  update = productUpdateService;\r\n  delete = productDeleteService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const productService = new ProductService();\r\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;AACnB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,yIAAA,CAAA,oBAAiB,CAAC;IACxB,OAAO,0IAAA,CAAA,qBAAkB,CAAC;IAC1B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAC9B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;AAChC;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 1855, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productSpecifications/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductSpecifications } from '../../types/entities';\r\n\r\nexport class ProductSpecificationsGetService extends BaseService {\r\n  /**\r\n   * Get all product specifications\r\n   */\r\n  async getAll(): Promise<ProductSpecifications[]> {\r\n    this.logApiCall('GET', ApiEndpoints.ProductSpecifications.Base);\r\n    return this.handleResponse(this.client.get<ProductSpecifications[]>(ApiEndpoints.ProductSpecifications.Base));\r\n  }\r\n\r\n  /**\r\n   * Get product specifications by ID\r\n   */\r\n  async getById(id: number): Promise<ProductSpecifications> {\r\n    this.logApiCall('GET', `${ApiEndpoints.ProductSpecifications.Base}/${id}`);\r\n    return this.handleResponse(this.client.get<ProductSpecifications>(`${ApiEndpoints.ProductSpecifications.Base}/${id}`));\r\n  }\r\n\r\n  /**\r\n   * Get product specifications by product ID\r\n   */\r\n  async getByProductId(productId: number): Promise<ProductSpecifications | null> {\r\n    this.logApiCall('GET', `${ApiEndpoints.ProductSpecifications.Base}/product/${productId}`);\r\n    try {\r\n      return await this.handleResponse(this.client.get<ProductSpecifications>(`${ApiEndpoints.ProductSpecifications.Base}/product/${productId}`));\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;AACrD;AACA;;;AAGO,MAAM,wCAAwC,wIAAA,CAAA,cAAW;IAC9D;;GAEC,GACD,MAAM,SAA2C;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI;QAC9D,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAA0B,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI;IAC7G;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAkC;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QACzE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IACtH;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAyC;QAC7E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QACxF,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QAC3I,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productSpecifications/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductSpecifications, CreateProductSpecificationsRequest } from '../../types/entities';\r\n\r\nexport class ProductSpecificationsPostService extends BaseService {\r\n  /**\r\n   * Create new product specifications\r\n   */\r\n  async create(data: CreateProductSpecificationsRequest): Promise<ProductSpecifications> {\r\n    this.logApiCall('POST', ApiEndpoints.ProductSpecifications.Base, data);\r\n    return this.handleResponse(this.client.post<ProductSpecifications>(ApiEndpoints.ProductSpecifications.Base, data));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,yCAAyC,wIAAA,CAAA,cAAW;IAC/D;;GAEC,GACD,MAAM,OAAO,IAAwC,EAAkC;QACrF,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,EAAE;QACjE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAwB,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,EAAE;IAC9G;AACF", "debugId": null}}, {"offset": {"line": 1916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productSpecifications/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductSpecifications, UpdateProductSpecificationsRequest } from '../../types/entities';\r\n\r\nexport class ProductSpecificationsUpdateService extends BaseService {\r\n  /**\r\n   * Update product specifications\r\n   */\r\n  async update(id: number, data: UpdateProductSpecificationsRequest): Promise<ProductSpecifications> {\r\n    this.logApiCall('PUT', `${ApiEndpoints.ProductSpecifications.Base}/${id}`, data);\r\n    return this.handleResponse(this.client.put<ProductSpecifications>(`${ApiEndpoints.ProductSpecifications.Base}/${id}`, data));\r\n  }\r\n\r\n  /**\r\n   * Delete product specifications\r\n   */\r\n  async delete(id: number): Promise<void> {\r\n    this.logApiCall('DELETE', `${ApiEndpoints.ProductSpecifications.Base}/${id}`);\r\n    return this.handleResponse(this.client.delete(`${ApiEndpoints.ProductSpecifications.Base}/${id}`));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,2CAA2C,wIAAA,CAAA,cAAW;IACjE;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAAwC,EAAkC;QACjG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;QAC3E,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;IACxH;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAAiB;QACtC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QAC5E,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,sIAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IAClG;AACF", "debugId": null}}, {"offset": {"line": 1943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productSpecifications/index.ts"], "sourcesContent": ["import { ProductSpecificationsGetService } from './get';\r\nimport { ProductSpecificationsPostService } from './post';\r\nimport { ProductSpecificationsUpdateService } from './update';\r\n\r\nexport class ProductSpecificationsService {\r\n  public get = new ProductSpecificationsGetService();\r\n  public post = new ProductSpecificationsPostService();\r\n  public update = new ProductSpecificationsUpdateService();\r\n}\r\n\r\nexport const productSpecificationsService = new ProductSpecificationsService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM;IACJ,MAAM,IAAI,sJAAA,CAAA,kCAA+B,GAAG;IAC5C,OAAO,IAAI,uJAAA,CAAA,mCAAgC,GAAG;IAC9C,SAAS,IAAI,yJAAA,CAAA,qCAAkC,GAAG;AAC3D;AAEO,MAAM,+BAA+B,IAAI", "debugId": null}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productDetails/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductDetails } from '../../types/entities';\r\n\r\nexport class ProductDetailsGetService extends BaseService {\r\n  /**\r\n   * Get all product details\r\n   */\r\n  async getAll(): Promise<ProductDetails[]> {\r\n    this.logApiCall('GET', ApiEndpoints.ProductDetails.Base);\r\n    return this.handleResponse(this.client.get<ProductDetails[]>(ApiEndpoints.ProductDetails.Base));\r\n  }\r\n\r\n  /**\r\n   * Get product details by ID\r\n   */\r\n  async getById(id: number): Promise<ProductDetails> {\r\n    this.logApiCall('GET', `${ApiEndpoints.ProductDetails.Base}/${id}`);\r\n    return this.handleResponse(this.client.get<ProductDetails>(`${ApiEndpoints.ProductDetails.Base}/${id}`));\r\n  }\r\n\r\n  /**\r\n   * Get product details by product ID\r\n   */\r\n  async getByProductId(productId: number): Promise<ProductDetails | null> {\r\n    this.logApiCall('GET', `${ApiEndpoints.ProductDetails.Base}/product/${productId}`);\r\n    try {\r\n      return await this.handleResponse(this.client.get<ProductDetails>(`${ApiEndpoints.ProductDetails.Base}/product/${productId}`));\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;AACrD;AACA;;;AAGO,MAAM,iCAAiC,wIAAA,CAAA,cAAW;IACvD;;GAEC,GACD,MAAM,SAAoC;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;QACvD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAmB,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI;IAC/F;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAA2B;QACjD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QAClE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IACxG;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAkC;QACtE,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QACjF,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QAC7H,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productDetails/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductDetails, CreateProductDetailsRequest } from '../../types/entities';\r\n\r\nexport class ProductDetailsPostService extends BaseService {\r\n  /**\r\n   * Create new product details\r\n   */\r\n  async create(data: CreateProductDetailsRequest): Promise<ProductDetails> {\r\n    this.logApiCall('POST', ApiEndpoints.ProductDetails.Base, data);\r\n    return this.handleResponse(this.client.post<ProductDetails>(ApiEndpoints.ProductDetails.Base, data));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,kCAAkC,wIAAA,CAAA,cAAW;IACxD;;GAEC,GACD,MAAM,OAAO,IAAiC,EAA2B;QACvE,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,EAAE;QAC1D,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAiB,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,EAAE;IAChG;AACF", "debugId": null}}, {"offset": {"line": 2026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productDetails/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ProductDetails, UpdateProductDetailsRequest } from '../../types/entities';\r\n\r\nexport class ProductDetailsUpdateService extends BaseService {\r\n  /**\r\n   * Update product details\r\n   */\r\n  async update(id: number, data: UpdateProductDetailsRequest): Promise<ProductDetails> {\r\n    this.logApiCall('PUT', `${ApiEndpoints.ProductDetails.Base}/${id}`, data);\r\n    return this.handleResponse(this.client.put<ProductDetails>(`${ApiEndpoints.ProductDetails.Base}/${id}`, data));\r\n  }\r\n\r\n  /**\r\n   * Delete product details\r\n   */\r\n  async delete(id: number): Promise<void> {\r\n    this.logApiCall('DELETE', `${ApiEndpoints.ProductDetails.Base}/${id}`);\r\n    return this.handleResponse(this.client.delete(`${ApiEndpoints.ProductDetails.Base}/${id}`));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,oCAAoC,wIAAA,CAAA,cAAW;IAC1D;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAAiC,EAA2B;QACnF,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;QACpE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;IAC1G;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAAiB;QACtC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QACrE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,sIAAA,CAAA,eAAY,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IAC3F;AACF", "debugId": null}}, {"offset": {"line": 2053, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/productDetails/index.ts"], "sourcesContent": ["import { ProductDetailsGetService } from './get';\r\nimport { ProductDetailsPostService } from './post';\r\nimport { ProductDetailsUpdateService } from './update';\r\n\r\nexport class ProductDetailsService {\r\n  public get = new ProductDetailsGetService();\r\n  public post = new ProductDetailsPostService();\r\n  public update = new ProductDetailsUpdateService();\r\n}\r\n\r\nexport const productDetailsService = new ProductDetailsService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM;IACJ,MAAM,IAAI,+IAAA,CAAA,2BAAwB,GAAG;IACrC,OAAO,IAAI,gJAAA,CAAA,4BAAyB,GAAG;IACvC,SAAS,IAAI,kJAAA,CAAA,8BAA2B,GAAG;AACpD;AAEO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 2075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/downloadableContent/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { DownloadableContent } from '../../types/entities';\r\n\r\nexport class DownloadableContentGetService extends BaseService {\r\n  /**\r\n   * Get all downloadable content\r\n   */\r\n  async getAll(): Promise<DownloadableContent[]> {\r\n    this.logApiCall('GET', ApiEndpoints.DownloadableContent.Base);\r\n    return this.handleResponse(this.client.get<DownloadableContent[]>(ApiEndpoints.DownloadableContent.Base));\r\n  }\r\n\r\n  /**\r\n   * Get downloadable content by ID\r\n   */\r\n  async getById(id: number): Promise<DownloadableContent> {\r\n    this.logApiCall('GET', `${ApiEndpoints.DownloadableContent.Base}/${id}`);\r\n    return this.handleResponse(this.client.get<DownloadableContent>(`${ApiEndpoints.DownloadableContent.Base}/${id}`));\r\n  }\r\n\r\n  /**\r\n   * Get downloadable content by product ID\r\n   */\r\n  async getByProductId(productId: number): Promise<DownloadableContent | null> {\r\n    this.logApiCall('GET', `${ApiEndpoints.DownloadableContent.Base}/product/${productId}`);\r\n    try {\r\n      return await this.handleResponse(this.client.get<DownloadableContent>(`${ApiEndpoints.DownloadableContent.Base}/product/${productId}`));\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;AACrD;AACA;;;AAGO,MAAM,sCAAsC,wIAAA,CAAA,cAAW;IAC5D;;GAEC,GACD,MAAM,SAAyC;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI;QAC5D,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI;IACzG;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAgC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QACvE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAsB,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IAClH;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAuC;QAC3E,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QACtF,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAsB,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW;QACvI,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/downloadableContent/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { DownloadableContent, CreateDownloadableContentRequest } from '../../types/entities';\r\n\r\nexport class DownloadableContentPostService extends BaseService {\r\n  /**\r\n   * Create new downloadable content\r\n   */\r\n  async create(data: CreateDownloadableContentRequest): Promise<DownloadableContent> {\r\n    this.logApiCall('POST', ApiEndpoints.DownloadableContent.Base, data);\r\n    return this.handleResponse(this.client.post<DownloadableContent>(ApiEndpoints.DownloadableContent.Base, data));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,uCAAuC,wIAAA,CAAA,cAAW;IAC7D;;GAEC,GACD,MAAM,OAAO,IAAsC,EAAgC;QACjF,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,EAAE;QAC/D,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAsB,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,EAAE;IAC1G;AACF", "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/downloadableContent/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { DownloadableContent, UpdateDownloadableContentRequest } from '../../types/entities';\r\n\r\nexport class DownloadableContentUpdateService extends BaseService {\r\n  /**\r\n   * Update downloadable content\r\n   */\r\n  async update(id: number, data: UpdateDownloadableContentRequest): Promise<DownloadableContent> {\r\n    this.logApiCall('PUT', `${ApiEndpoints.DownloadableContent.Base}/${id}`, data);\r\n    return this.handleResponse(this.client.put<DownloadableContent>(`${ApiEndpoints.DownloadableContent.Base}/${id}`, data));\r\n  }\r\n\r\n  /**\r\n   * Delete downloadable content\r\n   */\r\n  async delete(id: number): Promise<void> {\r\n    this.logApiCall('DELETE', `${ApiEndpoints.DownloadableContent.Base}/${id}`);\r\n    return this.handleResponse(this.client.delete(`${ApiEndpoints.DownloadableContent.Base}/${id}`));\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,yCAAyC,wIAAA,CAAA,cAAW;IAC/D;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAAsC,EAAgC;QAC7F,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;QACzE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAsB,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;IACpH;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAAiB;QACtC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;QAC1E,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI;IAChG;AACF", "debugId": null}}, {"offset": {"line": 2163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/downloadableContent/index.ts"], "sourcesContent": ["import { DownloadableContentGetService } from './get';\r\nimport { DownloadableContentPostService } from './post';\r\nimport { DownloadableContentUpdateService } from './update';\r\n\r\nexport class DownloadableContentService {\r\n  public get = new DownloadableContentGetService();\r\n  public post = new DownloadableContentPostService();\r\n  public update = new DownloadableContentUpdateService();\r\n}\r\n\r\nexport const downloadableContentService = new DownloadableContentService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM;IACJ,MAAM,IAAI,oJAAA,CAAA,gCAA6B,GAAG;IAC1C,OAAO,IAAI,qJAAA,CAAA,iCAA8B,GAAG;IAC5C,SAAS,IAAI,uJAAA,CAAA,mCAAgC,GAAG;AACzD;AAEO,MAAM,6BAA6B,IAAI", "debugId": null}}, {"offset": {"line": 2185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/get.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints, PaginatedResponse } from '../../config/apiConfig';\r\nimport { \r\n  Order, \r\n  OrderSummary, \r\n  OrderFilterRequest,\r\n   \r\n} from '../../types/entities';\r\n\r\nexport class OrderGetService extends BaseService {\r\n  /**\r\n   * Get all orders (summary)\r\n   */\r\n  async getAll(): Promise<OrderSummary[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Base);\r\n    return this.handleResponse(\r\n      this.client.get<OrderSummary[]>(ApiEndpoints.Orders.Base)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get order by ID\r\n   */\r\n  async getById(id: number): Promise<Order> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.ById(id));\r\n    return this.handleResponse(\r\n      this.client.get<Order>(ApiEndpoints.Orders.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get order with full details\r\n   */\r\n  async getDetails(id: number): Promise<Order> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Details(id));\r\n    return this.handleResponse(\r\n      this.client.get<Order>(ApiEndpoints.Orders.Details(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get orders by user ID\r\n   */\r\n  async getByUser(userId: number): Promise<Order[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.ByUser(userId));\r\n    return this.handleResponse(\r\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByUser(userId))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get orders by email\r\n   */\r\n  async getByEmail(email: string): Promise<Order[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.ByEmail(email));\r\n    return this.handleResponse(\r\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByEmail(email))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get orders by status\r\n   */\r\n  async getByStatus(statusId: number): Promise<Order[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.ByStatus(statusId));\r\n    return this.handleResponse(\r\n      this.client.get<Order[]>(ApiEndpoints.Orders.ByStatus(statusId))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get pending orders\r\n   */\r\n  async getPending(): Promise<Order[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Pending);\r\n    return this.handleResponse(\r\n      this.client.get<Order[]>(ApiEndpoints.Orders.Pending)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get recent orders\r\n   */\r\n  async getRecent(count: number = 10): Promise<OrderSummary[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Recent, { count });\r\n    return this.handleResponse(\r\n      this.client.get<OrderSummary[]>(ApiEndpoints.Orders.Recent, { count })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get total revenue\r\n   */\r\n  async getTotalRevenue(): Promise<number> {\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Revenue.Total);\r\n    return this.handleResponse(\r\n      this.client.get<number>(ApiEndpoints.Orders.Revenue.Total)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get revenue by date range\r\n   */\r\n  async getRevenueByDateRange(startDate: Date, endDate: Date): Promise<number> {\r\n    const params = {\r\n      startDate: ServiceUtils.formatDate(startDate),\r\n      endDate: ServiceUtils.formatDate(endDate)\r\n    };\r\n    \r\n    this.logApiCall('GET', ApiEndpoints.Orders.Revenue.Range, params);\r\n    return this.handleResponse(\r\n      this.client.get<number>(ApiEndpoints.Orders.Revenue.Range, params)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get orders with advanced filtering and pagination\r\n   */\r\n  async getFiltered(filters: OrderFilterRequest): Promise<PaginatedResponse<Order>> {\r\n    const cleanFilters = ServiceUtils.cleanObject(filters);\r\n    this.logApiCall('GET', ApiEndpoints.Orders.Filter, cleanFilters);\r\n    \r\n    return this.handlePaginatedResponse(\r\n      this.client.get<PaginatedResponse<Order>>(\r\n        ApiEndpoints.Orders.Filter, \r\n        cleanFilters\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get orders with default pagination\r\n   */\r\n  async getPaginated(\r\n    pageNumber: number = 1, \r\n    pageSize: number = 10,\r\n    sortBy: string = 'createdAt',\r\n    sortDirection: 'asc' | 'desc' = 'desc'\r\n  ): Promise<PaginatedResponse<Order>> {\r\n    const filters: OrderFilterRequest = {\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy,\r\n      sortDirection\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get orders by amount range\r\n   */\r\n  async getByAmountRange(minAmount: number, maxAmount: number): Promise<Order[]> {\r\n    const filters: OrderFilterRequest = {\r\n      minAmount,\r\n      maxAmount,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get orders by payment method\r\n   */\r\n  async getByPaymentMethod(paymentMethod: string): Promise<Order[]> {\r\n    const filters: OrderFilterRequest = {\r\n      paymentMethod,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get orders by country\r\n   */\r\n  async getByCountry(country: string): Promise<Order[]> {\r\n    const filters: OrderFilterRequest = {\r\n      country,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get orders by city\r\n   */\r\n  async getByCity(city: string): Promise<Order[]> {\r\n    const filters: OrderFilterRequest = {\r\n      city,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get orders created within date range\r\n   */\r\n  async getByDateRange(startDate: Date, endDate: Date): Promise<Order[]> {\r\n    const filters: OrderFilterRequest = {\r\n      createdAfter: ServiceUtils.formatDate(startDate),\r\n      createdBefore: ServiceUtils.formatDate(endDate),\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get orders by multiple statuses\r\n   */\r\n  async getByStatuses(statusIds: number[]): Promise<Order[]> {\r\n    const promises = statusIds.map(statusId => this.getByStatus(statusId));\r\n    const results = await Promise.all(promises);\r\n    \r\n    // Flatten and remove duplicates\r\n    const allOrders = results.flat();\r\n    const uniqueOrders = allOrders.filter((order, index, self) => \r\n      index === self.findIndex(o => o.id === order.id)\r\n    );\r\n    \r\n    return uniqueOrders;\r\n  }\r\n\r\n  /**\r\n   * Get user's order history with pagination\r\n   */\r\n  async getUserOrderHistory(\r\n    userId: number,\r\n    pageNumber: number = 1,\r\n    pageSize: number = 10\r\n  ): Promise<PaginatedResponse<Order>> {\r\n    const filters: OrderFilterRequest = {\r\n      userId,\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy: 'createdAt',\r\n      sortDirection: 'desc'\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get guest orders by email with pagination\r\n   */\r\n  async getGuestOrderHistory(\r\n    email: string,\r\n    pageNumber: number = 1,\r\n    pageSize: number = 10\r\n  ): Promise<PaginatedResponse<Order>> {\r\n    const filters: OrderFilterRequest = {\r\n      email,\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy: 'createdAt',\r\n      sortDirection: 'desc'\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get order statistics\r\n   */\r\n  async getStatistics(): Promise<{\r\n    totalOrders: number;\r\n    totalRevenue: number;\r\n    averageOrderValue: number;\r\n    pendingOrders: number;\r\n    completedOrders: number;\r\n  }> {\r\n    try {\r\n      const [totalRevenue, pendingOrders, allOrders] = await Promise.all([\r\n        this.getTotalRevenue(),\r\n        this.getPending(),\r\n        this.getAll()\r\n      ]);\r\n\r\n      const totalOrders = allOrders.length;\r\n      const completedOrders = allOrders.filter(order => \r\n        order.statusName.toLowerCase().includes('delivered') || \r\n        order.statusName.toLowerCase().includes('completed')\r\n      ).length;\r\n\r\n      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;\r\n\r\n      return {\r\n        totalOrders,\r\n        totalRevenue,\r\n        averageOrderValue: Math.round(averageOrderValue * 100) / 100,\r\n        pendingOrders: pendingOrders.length,\r\n        completedOrders\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting order statistics:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const orderGetService = new OrderGetService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQO,MAAM,wBAAwB,wIAAA,CAAA,cAAW;IAC9C;;GAEC,GACD,MAAM,SAAkC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI;QAC/C,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI;IAE5D;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAkB;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;QAChD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;IAEpD;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAkB;QAC3C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;IAEvD;IAEA;;GAEC,GACD,MAAM,UAAU,MAAc,EAAoB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;IAExD;IAEA;;GAEC,GACD,MAAM,WAAW,KAAa,EAAoB;QAChD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC;IAEzD;IAEA;;GAEC,GACD,MAAM,YAAY,QAAgB,EAAoB;QACpD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;IAE1D;IAEA;;GAEC,GACD,MAAM,aAA+B;QACnC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO;IAExD;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAA2B;QAC3D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;YAAE;QAAM;QAC3D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAiB,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;YAAE;QAAM;IAExE;IAEA;;GAEC,GACD,MAAM,kBAAmC;QACvC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;QACxD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;IAE7D;IAEA;;GAEC,GACD,MAAM,sBAAsB,SAAe,EAAE,OAAa,EAAmB;QAC3E,MAAM,SAAS;YACb,WAAW,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACnC,SAAS,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;QACnC;QAEA,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;QAC1D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;IAE/D;IAEA;;GAEC,GACD,MAAM,YAAY,OAA2B,EAAqC;QAChF,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAAE;QAEnD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,EAC1B;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACH;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,iBAAiB,SAAiB,EAAE,SAAiB,EAAoB;QAC7E,MAAM,UAA8B;YAClC;YACA;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAoB;QAChE,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,aAAa,OAAe,EAAoB;QACpD,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,UAAU,IAAY,EAAoB;QAC9C,MAAM,UAA8B;YAClC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAoB;QACrE,MAAM,UAA8B;YAClC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,cAAc,SAAmB,EAAoB;QACzD,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,WAAY,IAAI,CAAC,WAAW,CAAC;QAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAElC,gCAAgC;QAChC,MAAM,YAAY,QAAQ,IAAI;QAC9B,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,OAAO,OAAO,OACnD,UAAU,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;QAGjD,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACc;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,qBACJ,KAAa,EACb,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACc;QACnC,MAAM,UAA8B;YAClC;YACA;YACA;YACA,QAAQ;YACR,eAAe;QACjB;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,gBAMH;QACD,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjE,IAAI,CAAC,eAAe;gBACpB,IAAI,CAAC,UAAU;gBACf,IAAI,CAAC,MAAM;aACZ;YAED,MAAM,cAAc,UAAU,MAAM;YACpC,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAA,QACvC,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACxC,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,cACxC,MAAM;YAER,MAAM,oBAAoB,cAAc,IAAI,eAAe,cAAc;YAEzE,OAAO;gBACL;gBACA;gBACA,mBAAmB,KAAK,KAAK,CAAC,oBAAoB,OAAO;gBACzD,eAAe,cAAc,MAAM;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 2398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/post.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Order, CreateOrderRequest, CreateOrderItemRequest } from '../../types/entities';\r\n\r\nexport class OrderPostService extends BaseService {\r\n  /**\r\n   * Create a new order\r\n   */\r\n  async create(data: CreateOrderRequest): Promise<Order> {\r\n    this.logApiCall('POST', ApiEndpoints.Orders.Base, data);\r\n    \r\n    // Validate required fields\r\n    this.validateCreateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<Order>(ApiEndpoints.Orders.Base, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a customer order (with user ID)\r\n   */\r\n  async createCustomerOrder(\r\n    userId: number,\r\n    email: string,\r\n    orderItems: CreateOrderItemRequest[],\r\n    shippingInfo: {\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    } = {},\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    const data: CreateOrderRequest = {\r\n      userId,\r\n      email,\r\n      phoneNumber: shippingInfo.phoneNumber,\r\n      country: shippingInfo.country,\r\n      city: shippingInfo.city,\r\n      zipCode: shippingInfo.zipCode,\r\n      paymentMethod,\r\n      orderItems\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create a guest order (without user ID)\r\n   */\r\n  async createGuestOrder(\r\n    email: string,\r\n    orderItems: CreateOrderItemRequest[],\r\n    shippingInfo: {\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    } = {},\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    const data: CreateOrderRequest = {\r\n      userId: undefined,\r\n      email,\r\n      phoneNumber: shippingInfo.phoneNumber,\r\n      country: shippingInfo.country,\r\n      city: shippingInfo.city,\r\n      zipCode: shippingInfo.zipCode,\r\n      paymentMethod,\r\n      orderItems\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create order from shopping cart\r\n   */\r\n  async createFromCart(\r\n    cartItems: Array<{\r\n      productId: number;\r\n      quantity: number;\r\n    }>,\r\n    customerInfo: {\r\n      userId?: number;\r\n      email: string;\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    },\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    const orderItems: CreateOrderItemRequest[] = cartItems.map(item => ({\r\n      productId: item.productId,\r\n      quantity: item.quantity\r\n    }));\r\n\r\n    const data: CreateOrderRequest = {\r\n      userId: customerInfo.userId,\r\n      email: customerInfo.email,\r\n      phoneNumber: customerInfo.phoneNumber,\r\n      country: customerInfo.country,\r\n      city: customerInfo.city,\r\n      zipCode: customerInfo.zipCode,\r\n      paymentMethod,\r\n      orderItems\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create single item order (quick order)\r\n   */\r\n  async createQuickOrder(\r\n    productId: number,\r\n    quantity: number,\r\n    customerInfo: {\r\n      userId?: number;\r\n      email: string;\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    },\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    const orderItems: CreateOrderItemRequest[] = [{\r\n      productId,\r\n      quantity\r\n    }];\r\n\r\n    return this.createFromCart([{ productId, quantity }], customerInfo, paymentMethod);\r\n  }\r\n\r\n  /**\r\n   * Create bulk order (multiple products with quantities)\r\n   */\r\n  async createBulkOrder(\r\n    products: Array<{\r\n      productId: number;\r\n      quantity: number;\r\n    }>,\r\n    customerInfo: {\r\n      userId?: number;\r\n      email: string;\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    },\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    return this.createFromCart(products, customerInfo, paymentMethod);\r\n  }\r\n\r\n  /**\r\n   * Create repeat order (duplicate previous order)\r\n   */\r\n  async createRepeatOrder(\r\n    originalOrderId: number,\r\n    customerInfo: {\r\n      userId?: number;\r\n      email: string;\r\n      phoneNumber?: string;\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    },\r\n    paymentMethod?: string\r\n  ): Promise<Order> {\r\n    try {\r\n      // Get the original order details\r\n      const { orderGetService } = await import('./get');\r\n      const originalOrder = await orderGetService.getDetails(originalOrderId);\r\n\r\n      // Create new order items from original order\r\n      const orderItems: CreateOrderItemRequest[] = originalOrder.orderItems.map(item => ({\r\n        productId: item.productId,\r\n        quantity: item.quantity\r\n      }));\r\n\r\n      const data: CreateOrderRequest = {\r\n        userId: customerInfo.userId,\r\n        email: customerInfo.email,\r\n        phoneNumber: customerInfo.phoneNumber,\r\n        country: customerInfo.country,\r\n        city: customerInfo.city,\r\n        zipCode: customerInfo.zipCode,\r\n        paymentMethod,\r\n        orderItems\r\n      };\r\n\r\n      return this.create(data);\r\n    } catch (error) {\r\n      console.error('Error creating repeat order:', error);\r\n      throw new Error('Failed to create repeat order');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create order with validation and stock check\r\n   */\r\n  async createWithValidation(data: CreateOrderRequest): Promise<{\r\n    success: boolean;\r\n    order?: Order;\r\n    errors?: string[];\r\n  }> {\r\n    try {\r\n      // Validate stock availability (this would require product service)\r\n      const stockValidation = await this.validateStock(data.orderItems);\r\n      \r\n      if (!stockValidation.valid) {\r\n        return {\r\n          success: false,\r\n          errors: stockValidation.errors\r\n        };\r\n      }\r\n\r\n      const order = await this.create(data);\r\n      \r\n      return {\r\n        success: true,\r\n        order\r\n      };\r\n    } catch (error) {\r\n      console.error('Error creating order with validation:', error);\r\n      return {\r\n        success: false,\r\n        errors: [error instanceof Error ? error.message : 'Unknown error occurred']\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate stock availability for order items\r\n   */\r\n  private async validateStock(orderItems: CreateOrderItemRequest[]): Promise<{\r\n    valid: boolean;\r\n    errors?: string[];\r\n  }> {\r\n    try {\r\n      // This would require product service to check stock\r\n      // For now, we'll assume stock is valid\r\n      // In a real implementation, you'd check each product's stock\r\n      \r\n      const errors: string[] = [];\r\n      \r\n      for (const item of orderItems) {\r\n        if (item.quantity <= 0) {\r\n          errors.push(`Invalid quantity for product ${item.productId}`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        valid: errors.length === 0,\r\n        errors: errors.length > 0 ? errors : undefined\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating stock:', error);\r\n      return {\r\n        valid: false,\r\n        errors: ['Error validating stock availability']\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate create order request\r\n   */\r\n  private validateCreateRequest(data: CreateOrderRequest): void {\r\n    if (!data.email || !ServiceUtils.isValidEmail(data.email)) {\r\n      throw new Error('Valid email is required');\r\n    }\r\n\r\n    if (!data.orderItems || data.orderItems.length === 0) {\r\n      throw new Error('At least one order item is required');\r\n    }\r\n\r\n    // Validate order items\r\n    for (const item of data.orderItems) {\r\n      if (!item.productId || item.productId <= 0) {\r\n        throw new Error('Valid product ID is required for all order items');\r\n      }\r\n\r\n      if (!item.quantity || item.quantity <= 0) {\r\n        throw new Error('Quantity must be greater than 0 for all order items');\r\n      }\r\n    }\r\n\r\n    // Validate phone number format if provided\r\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\r\n      throw new Error('Invalid phone number format');\r\n    }\r\n\r\n    // Validate zip code format if provided\r\n    if (data.zipCode && data.zipCode.length > 20) {\r\n      throw new Error('Zip code must be 20 characters or less');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate phone number format\r\n   */\r\n  private isValidPhoneNumber(phone: string): boolean {\r\n    // Basic phone number validation (can be enhanced)\r\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const orderPostService = new OrderPostService();\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AAGO,MAAM,yBAAyB,wIAAA,CAAA,cAAW;IAC/C;;GAEC,GACD,MAAM,OAAO,IAAwB,EAAkB;QACrD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,EAAE;QAElD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAQ,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,EAAE;IAEtD;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,KAAa,EACb,UAAoC,EACpC,eAKI,CAAC,CAAC,EACN,aAAsB,EACN;QAChB,MAAM,OAA2B;YAC/B;YACA;YACA,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,KAAa,EACb,UAAoC,EACpC,eAKI,CAAC,CAAC,EACN,aAAsB,EACN;QAChB,MAAM,OAA2B;YAC/B,QAAQ;YACR;YACA,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,eACJ,SAGE,EACF,YAOC,EACD,aAAsB,EACN;QAChB,MAAM,aAAuC,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClE,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;YACzB,CAAC;QAED,MAAM,OAA2B;YAC/B,QAAQ,aAAa,MAAM;YAC3B,OAAO,aAAa,KAAK;YACzB,aAAa,aAAa,WAAW;YACrC,SAAS,aAAa,OAAO;YAC7B,MAAM,aAAa,IAAI;YACvB,SAAS,aAAa,OAAO;YAC7B;YACA;QACF;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,iBACJ,SAAiB,EACjB,QAAgB,EAChB,YAOC,EACD,aAAsB,EACN;QAChB,MAAM,aAAuC;YAAC;gBAC5C;gBACA;YACF;SAAE;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC;YAAC;gBAAE;gBAAW;YAAS;SAAE,EAAE,cAAc;IACtE;IAEA;;GAEC,GACD,MAAM,gBACJ,QAGE,EACF,YAOC,EACD,aAAsB,EACN;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,cAAc;IACrD;IAEA;;GAEC,GACD,MAAM,kBACJ,eAAuB,EACvB,YAOC,EACD,aAAsB,EACN;QAChB,IAAI;YACF,iCAAiC;YACjC,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,gBAAgB,MAAM,gBAAgB,UAAU,CAAC;YAEvD,6CAA6C;YAC7C,MAAM,aAAuC,cAAc,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjF,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;gBACzB,CAAC;YAED,MAAM,OAA2B;gBAC/B,QAAQ,aAAa,MAAM;gBAC3B,OAAO,aAAa,KAAK;gBACzB,aAAa,aAAa,WAAW;gBACrC,SAAS,aAAa,OAAO;gBAC7B,MAAM,aAAa,IAAI;gBACvB,SAAS,aAAa,OAAO;gBAC7B;gBACA;YACF;YAEA,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,IAAwB,EAIhD;QACD,IAAI;YACF,mEAAmE;YACnE,MAAM,kBAAkB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,UAAU;YAEhE,IAAI,CAAC,gBAAgB,KAAK,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,QAAQ,gBAAgB,MAAM;gBAChC;YACF;YAEA,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,CAAC;YAEhC,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;iBAAyB;YAC7E;QACF;IACF;IAEA;;GAEC,GACD,MAAc,cAAc,UAAoC,EAG7D;QACD,IAAI;YACF,oDAAoD;YACpD,uCAAuC;YACvC,6DAA6D;YAE7D,MAAM,SAAmB,EAAE;YAE3B,KAAK,MAAM,QAAQ,WAAY;gBAC7B,IAAI,KAAK,QAAQ,IAAI,GAAG;oBACtB,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,SAAS,EAAE;gBAC9D;YACF;YAEA,OAAO;gBACL,OAAO,OAAO,MAAM,KAAK;gBACzB,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,OAAO;gBACP,QAAQ;oBAAC;iBAAsC;YACjD;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAwB,EAAQ;QAC5D,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,wIAAA,CAAA,eAAY,CAAC,YAAY,CAAC,KAAK,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,uBAAuB;QACvB,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAE;YAClC,IAAI,CAAC,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,GAAG;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG;gBACxC,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,uCAAuC;QACvC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,kDAAkD;QAClD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;AACF;AAGO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 2609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { UpdateOrderStatusRequest } from '../../types/entities';\r\n\r\nexport class OrderUpdateService extends BaseService {\r\n  /**\r\n   * Update order status\r\n   */\r\n  async updateStatus(id: number, statusId: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', ApiEndpoints.Orders.UpdateStatus(id), { statusId });\r\n    \r\n    const data: UpdateOrderStatusRequest = { statusId };\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.patch(ApiEndpoints.Orders.UpdateStatus(id), data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Cancel an order\r\n   */\r\n  async cancel(id: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', ApiEndpoints.Orders.Cancel(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.patch(ApiEndpoints.Orders.Cancel(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Mark order as confirmed\r\n   */\r\n  async confirm(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 2); // Status ID 2 = Confirmed\r\n  }\r\n\r\n  /**\r\n   * Mark order as processing\r\n   */\r\n  async markAsProcessing(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 3); // Status ID 3 = Processing\r\n  }\r\n\r\n  /**\r\n   * Mark order as shipped\r\n   */\r\n  async markAsShipped(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 4); // Status ID 4 = Shipped\r\n  }\r\n\r\n  /**\r\n   * Mark order as delivered\r\n   */\r\n  async markAsDelivered(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 5); // Status ID 5 = Delivered\r\n  }\r\n\r\n  /**\r\n   * Mark order as returned\r\n   */\r\n  async markAsReturned(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 7); // Status ID 7 = Returned\r\n  }\r\n\r\n  /**\r\n   * Mark order as refunded\r\n   */\r\n  async markAsRefunded(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 8); // Status ID 8 = Refunded\r\n  }\r\n\r\n  /**\r\n   * Update payment status to completed\r\n   */\r\n  async markPaymentCompleted(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 10); // Status ID 10 = Payment Completed\r\n  }\r\n\r\n  /**\r\n   * Update payment status to failed\r\n   */\r\n  async markPaymentFailed(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 11); // Status ID 11 = Payment Failed\r\n  }\r\n\r\n  /**\r\n   * Update payment status to refunded\r\n   */\r\n  async markPaymentRefunded(id: number): Promise<boolean> {\r\n    return this.updateStatus(id, 12); // Status ID 12 = Payment Refunded\r\n  }\r\n\r\n  /**\r\n   * Bulk update order statuses\r\n   */\r\n  async bulkUpdateStatus(orderIds: number[], statusId: number): Promise<{\r\n    success: boolean;\r\n    updatedCount: number;\r\n    errors: string[];\r\n  }> {\r\n    this.logApiCall('PATCH', 'Bulk Update Order Status', { \r\n      orderIds, \r\n      statusId, \r\n      count: orderIds.length \r\n    });\r\n\r\n    const results = await Promise.allSettled(\r\n      orderIds.map(id => this.updateStatus(id, statusId))\r\n    );\r\n\r\n    const successful = results.filter(result => \r\n      result.status === 'fulfilled' && result.value === true\r\n    ).length;\r\n\r\n    const errors = results\r\n      .filter(result => result.status === 'rejected')\r\n      .map((result, index) => \r\n        `Order ${orderIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\r\n      );\r\n\r\n    return {\r\n      success: successful === orderIds.length,\r\n      updatedCount: successful,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Process order through workflow (pending -> confirmed -> processing)\r\n   */\r\n  async processOrder(id: number): Promise<{\r\n    success: boolean;\r\n    currentStatus: string;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      // Get current order status\r\n      const { orderGetService } = await import('./get');\r\n      const order = await orderGetService.getById(id);\r\n\r\n      switch (order.statusId) {\r\n        case 1: // Pending\r\n          await this.confirm(id);\r\n          return {\r\n            success: true,\r\n            currentStatus: 'Confirmed',\r\n            message: 'Order confirmed successfully'\r\n          };\r\n\r\n        case 2: // Confirmed\r\n          await this.markAsProcessing(id);\r\n          return {\r\n            success: true,\r\n            currentStatus: 'Processing',\r\n            message: 'Order moved to processing'\r\n          };\r\n\r\n        case 3: // Processing\r\n          await this.markAsShipped(id);\r\n          return {\r\n            success: true,\r\n            currentStatus: 'Shipped',\r\n            message: 'Order marked as shipped'\r\n          };\r\n\r\n        case 4: // Shipped\r\n          await this.markAsDelivered(id);\r\n          return {\r\n            success: true,\r\n            currentStatus: 'Delivered',\r\n            message: 'Order marked as delivered'\r\n          };\r\n\r\n        default:\r\n          return {\r\n            success: false,\r\n            currentStatus: order.status.statusName,\r\n            message: 'Order cannot be processed further'\r\n          };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing order:', error);\r\n      return {\r\n        success: false,\r\n        currentStatus: 'Unknown',\r\n        message: error instanceof Error ? error.message : 'Failed to process order'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reverse order status (for corrections)\r\n   */\r\n  async reverseStatus(id: number): Promise<{\r\n    success: boolean;\r\n    previousStatus: string;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const { orderGetService } = await import('./get');\r\n      const order = await orderGetService.getById(id);\r\n\r\n      let newStatusId: number;\r\n      let statusName: string;\r\n\r\n      switch (order.statusId) {\r\n        case 5: // Delivered -> Shipped\r\n          newStatusId = 4;\r\n          statusName = 'Shipped';\r\n          break;\r\n\r\n        case 4: // Shipped -> Processing\r\n          newStatusId = 3;\r\n          statusName = 'Processing';\r\n          break;\r\n\r\n        case 3: // Processing -> Confirmed\r\n          newStatusId = 2;\r\n          statusName = 'Confirmed';\r\n          break;\r\n\r\n        case 2: // Confirmed -> Pending\r\n          newStatusId = 1;\r\n          statusName = 'Pending';\r\n          break;\r\n\r\n        default:\r\n          return {\r\n            success: false,\r\n            previousStatus: order.status.statusName,\r\n            message: 'Order status cannot be reversed'\r\n          };\r\n      }\r\n\r\n      await this.updateStatus(id, newStatusId);\r\n\r\n      return {\r\n        success: true,\r\n        previousStatus: statusName,\r\n        message: `Order status reversed to ${statusName}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Error reversing order status:', error);\r\n      return {\r\n        success: false,\r\n        previousStatus: 'Unknown',\r\n        message: error instanceof Error ? error.message : 'Failed to reverse order status'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if status update is valid\r\n   */\r\n  async canUpdateStatus(id: number, newStatusId: number): Promise<{\r\n    canUpdate: boolean;\r\n    reason?: string;\r\n    currentStatus?: string;\r\n  }> {\r\n    try {\r\n      const { orderGetService } = await import('./get');\r\n      const order = await orderGetService.getById(id);\r\n\r\n      // Define valid status transitions\r\n      const validTransitions: Record<number, number[]> = {\r\n        1: [2, 6], // Pending -> Confirmed, Cancelled\r\n        2: [3, 6], // Confirmed -> Processing, Cancelled\r\n        3: [4, 6], // Processing -> Shipped, Cancelled\r\n        4: [5, 7], // Shipped -> Delivered, Returned\r\n        5: [7],    // Delivered -> Returned\r\n        6: [],     // Cancelled (final)\r\n        7: [8],    // Returned -> Refunded\r\n        8: []      // Refunded (final)\r\n      };\r\n\r\n      const allowedStatuses = validTransitions[order.statusId] || [];\r\n      const canUpdate = allowedStatuses.includes(newStatusId);\r\n\r\n      return {\r\n        canUpdate,\r\n        currentStatus: order.status.statusName,\r\n        reason: canUpdate ? undefined : 'Invalid status transition'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking status update validity:', error);\r\n      return {\r\n        canUpdate: false,\r\n        reason: 'Error checking order status'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Safe status update with validation\r\n   */\r\n  async safeUpdateStatus(id: number, statusId: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const validation = await this.canUpdateStatus(id, statusId);\r\n\r\n      if (!validation.canUpdate) {\r\n        return {\r\n          success: false,\r\n          message: validation.reason || 'Status update not allowed'\r\n        };\r\n      }\r\n\r\n      const updated = await this.updateStatus(id, statusId);\r\n\r\n      return {\r\n        success: updated,\r\n        message: updated ? 'Order status updated successfully' : 'Failed to update order status'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error in safe status update:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const orderUpdateService = new OrderUpdateService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,aAAa,EAAU,EAAE,QAAgB,EAAoB;QACjE,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK;YAAE;QAAS;QAE1E,MAAM,OAAiC;YAAE;QAAS;QAElD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK;IAE5D;IAEA;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAEpD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,MAAM,CAAC;IAEjD;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAoB;QAC1C,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,0BAA0B;IAC7D;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAoB;QACnD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,2BAA2B;IAC9D;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAoB;QAChD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,wBAAwB;IAC3D;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAAoB;QAClD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,0BAA0B;IAC7D;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAoB;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,yBAAyB;IAC5D;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAoB;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,yBAAyB;IAC5D;IAEA;;GAEC,GACD,MAAM,qBAAqB,EAAU,EAAoB;QACvD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,mCAAmC;IACvE;IAEA;;GAEC,GACD,MAAM,kBAAkB,EAAU,EAAoB;QACpD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,gCAAgC;IACpE;IAEA;;GAEC,GACD,MAAM,oBAAoB,EAAU,EAAoB;QACtD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,kCAAkC;IACtE;IAEA;;GAEC,GACD,MAAM,iBAAiB,QAAkB,EAAE,QAAgB,EAIxD;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,4BAA4B;YACnD;YACA;YACA,OAAO,SAAS,MAAM;QACxB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,SAAS,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,YAAY,CAAC,IAAI;QAG3C,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAGjG,OAAO;YACL,SAAS,eAAe,SAAS,MAAM;YACvC,cAAc;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAI1B;QACD,IAAI;YACF,2BAA2B;YAC3B,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,OAAQ,MAAM,QAAQ;gBACpB,KAAK;oBACH,MAAM,IAAI,CAAC,OAAO,CAAC;oBACnB,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBAC5B,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,aAAa,CAAC;oBACzB,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF,KAAK;oBACH,MAAM,IAAI,CAAC,eAAe,CAAC;oBAC3B,OAAO;wBACL,SAAS;wBACT,eAAe;wBACf,SAAS;oBACX;gBAEF;oBACE,OAAO;wBACL,SAAS;wBACT,eAAe,MAAM,MAAM,CAAC,UAAU;wBACtC,SAAS;oBACX;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,eAAe;gBACf,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAI3B;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,IAAI;YACJ,IAAI;YAEJ,OAAQ,MAAM,QAAQ;gBACpB,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF,KAAK;oBACH,cAAc;oBACd,aAAa;oBACb;gBAEF;oBACE,OAAO;wBACL,SAAS;wBACT,gBAAgB,MAAM,MAAM,CAAC,UAAU;wBACvC,SAAS;oBACX;YACJ;YAEA,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI;YAE5B,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB,SAAS,CAAC,yBAAyB,EAAE,YAAY;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,EAAU,EAAE,WAAmB,EAIlD;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,kCAAkC;YAClC,MAAM,mBAA6C;gBACjD,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;oBAAG;iBAAE;gBACT,GAAG;oBAAC;iBAAE;gBACN,GAAG,EAAE;gBACL,GAAG;oBAAC;iBAAE;gBACN,GAAG,EAAE,CAAM,mBAAmB;YAChC;YAEA,MAAM,kBAAkB,gBAAgB,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE;YAC9D,MAAM,YAAY,gBAAgB,QAAQ,CAAC;YAE3C,OAAO;gBACL;gBACA,eAAe,MAAM,MAAM,CAAC,UAAU;gBACtC,QAAQ,YAAY,YAAY;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAE,QAAgB,EAGhD;QACD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI;YAElD,IAAI,CAAC,WAAW,SAAS,EAAE;gBACzB,OAAO;oBACL,SAAS;oBACT,SAAS,WAAW,MAAM,IAAI;gBAChC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI;YAE5C,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,sCAAsC;YAC3D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class OrderDeleteService extends BaseService {\r\n  /**\r\n   * Delete an order by ID\r\n   */\r\n  async delete(id: number): Promise<boolean> {\r\n    this.logApiCall('DELETE', ApiEndpoints.Orders.ById(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.delete(ApiEndpoints.Orders.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete multiple orders\r\n   */\r\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\r\n    this.logApiCall('DELETE', 'Batch Orders', { count: ids.length });\r\n    \r\n    const promises = ids.map(id => this.delete(id));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Check if order can be safely deleted\r\n   */\r\n  async canDelete(id: number): Promise<{\r\n    canDelete: boolean;\r\n    reason?: string;\r\n    orderStatus?: string;\r\n  }> {\r\n    try {\r\n      const { orderGetService } = await import('./get');\r\n      const order = await orderGetService.getById(id);\r\n\r\n      // Orders can typically only be deleted if they are:\r\n      // - Pending (status 1)\r\n      // - Cancelled (status 6)\r\n      // - In some cases, failed payment orders\r\n      const deletableStatuses = [1, 6, 11]; // Pending, Cancelled, Payment Failed\r\n\r\n      const canDelete = deletableStatuses.includes(order.statusId);\r\n      \r\n      let reason: string | undefined;\r\n      if (!canDelete) {\r\n        switch (order.statusId) {\r\n          case 2:\r\n          case 3:\r\n          case 4:\r\n            reason = 'Cannot delete confirmed or processing orders';\r\n            break;\r\n          case 5:\r\n            reason = 'Cannot delete delivered orders';\r\n            break;\r\n          case 7:\r\n          case 8:\r\n            reason = 'Cannot delete returned or refunded orders';\r\n            break;\r\n          default:\r\n            reason = 'Order cannot be deleted in current status';\r\n        }\r\n      }\r\n\r\n      return {\r\n        canDelete,\r\n        reason,\r\n        orderStatus: order.status.statusName\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking if order can be deleted:', error);\r\n      return {\r\n        canDelete: false,\r\n        reason: 'Error checking order status'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Safe delete - checks if order can be deleted first\r\n   */\r\n  async safeDelete(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const deleteCheck = await this.canDelete(id);\r\n      \r\n      if (!deleteCheck.canDelete) {\r\n        return {\r\n          success: false,\r\n          message: deleteCheck.reason || 'Order cannot be deleted'\r\n        };\r\n      }\r\n\r\n      const deleted = await this.delete(id);\r\n      \r\n      if (deleted) {\r\n        return {\r\n          success: true,\r\n          message: 'Order deleted successfully'\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: 'Failed to delete order'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during safe delete:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cancel order instead of deleting (safer option)\r\n   */\r\n  async cancelInsteadOfDelete(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const { orderUpdateService } = await import('./update');\r\n      const cancelled = await orderUpdateService.cancel(id);\r\n      \r\n      return {\r\n        success: cancelled,\r\n        message: cancelled \r\n          ? 'Order cancelled successfully' \r\n          : 'Failed to cancel order'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error cancelling order:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to cancel order'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete orders by status (bulk operation)\r\n   */\r\n  async deleteByStatus(statusId: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n    totalCount: number;\r\n  }> {\r\n    try {\r\n      const { orderGetService } = await import('./get');\r\n      const orders = await orderGetService.getByStatus(statusId);\r\n      \r\n      if (orders.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: 'No orders found with specified status',\r\n          deletedCount: 0,\r\n          totalCount: 0\r\n        };\r\n      }\r\n\r\n      // Check if any orders can be deleted\r\n      const deletableOrders: number[] = [];\r\n      for (const order of orders) {\r\n        const canDelete = await this.canDelete(order.id);\r\n        if (canDelete.canDelete) {\r\n          deletableOrders.push(order.id);\r\n        }\r\n      }\r\n\r\n      if (deletableOrders.length === 0) {\r\n        return {\r\n          success: false,\r\n          message: 'No orders can be deleted in current status',\r\n          deletedCount: 0,\r\n          totalCount: orders.length\r\n        };\r\n      }\r\n\r\n      const results = await this.deleteBatch(deletableOrders);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === deletableOrders.length,\r\n        message: `Deleted ${deletedCount} of ${deletableOrders.length} eligible orders`,\r\n        deletedCount,\r\n        totalCount: orders.length\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting orders by status:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete orders',\r\n        deletedCount: 0,\r\n        totalCount: 0\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete old cancelled orders (cleanup operation)\r\n   */\r\n  async deleteOldCancelledOrders(olderThanDays: number = 30): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n  }> {\r\n    try {\r\n      const { orderGetService } = await import('./get');\r\n      const cutoffDate = new Date();\r\n      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);\r\n\r\n      // Get cancelled orders\r\n      const cancelledOrders = await orderGetService.getByStatus(6); // Status 6 = Cancelled\r\n      \r\n      // Filter orders older than cutoff date\r\n      const oldOrders = cancelledOrders.filter(order => \r\n        new Date(order.createdAt) < cutoffDate\r\n      );\r\n\r\n      if (oldOrders.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: `No cancelled orders older than ${olderThanDays} days found`,\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      const orderIds = oldOrders.map(order => order.id);\r\n      const results = await this.deleteBatch(orderIds);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === oldOrders.length,\r\n        message: `Deleted ${deletedCount} old cancelled orders`,\r\n        deletedCount\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting old cancelled orders:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete old orders',\r\n        deletedCount: 0\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Archive order instead of deleting (move to archive status)\r\n   */\r\n  async archive(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      // This would require adding an \"Archived\" status to the system\r\n      // For now, we'll use the existing status system\r\n      const { orderUpdateService } = await import('./update');\r\n      \r\n      // Check if order can be archived (similar rules to deletion)\r\n      const canDelete = await this.canDelete(id);\r\n      if (!canDelete.canDelete) {\r\n        return {\r\n          success: false,\r\n          message: canDelete.reason || 'Order cannot be archived'\r\n        };\r\n      }\r\n\r\n      // In a real system, you might have a specific \"Archived\" status\r\n      // For now, we'll just mark it as cancelled\r\n      const archived = await orderUpdateService.cancel(id);\r\n      \r\n      return {\r\n        success: archived,\r\n        message: archived \r\n          ? 'Order archived successfully' \r\n          : 'Failed to archive order'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error archiving order:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to archive order'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const orderDeleteService = new OrderDeleteService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,2BAA2B,wIAAA,CAAA,cAAW;IACjD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;QAEnD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,MAAM,CAAC,IAAI,CAAC;IAEhD;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,gBAAgB;YAAE,OAAO,IAAI,MAAM;QAAC;QAE9D,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAIvB;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,QAAQ,MAAM,gBAAgB,OAAO,CAAC;YAE5C,oDAAoD;YACpD,uBAAuB;YACvB,yBAAyB;YACzB,yCAAyC;YACzC,MAAM,oBAAoB;gBAAC;gBAAG;gBAAG;aAAG,EAAE,qCAAqC;YAE3E,MAAM,YAAY,kBAAkB,QAAQ,CAAC,MAAM,QAAQ;YAE3D,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,OAAQ,MAAM,QAAQ;oBACpB,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;wBACH,SAAS;wBACT;oBACF,KAAK;oBACL,KAAK;wBACH,SAAS;wBACT;oBACF;wBACE,SAAS;gBACb;YACF;YAEA,OAAO;gBACL;gBACA;gBACA,aAAa,MAAM,MAAM,CAAC,UAAU;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,EAAU,EAGnC;QACD,IAAI;YACF,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAC/B,MAAM,YAAY,MAAM,mBAAmB,MAAM,CAAC;YAElD,OAAO;gBACL,SAAS;gBACT,SAAS,YACL,iCACA;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,QAAgB,EAKlC;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,SAAS,MAAM,gBAAgB,WAAW,CAAC;YAEjD,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,YAAY;gBACd;YACF;YAEA,qCAAqC;YACrC,MAAM,kBAA4B,EAAE;YACpC,KAAK,MAAM,SAAS,OAAQ;gBAC1B,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC/C,IAAI,UAAU,SAAS,EAAE;oBACvB,gBAAgB,IAAI,CAAC,MAAM,EAAE;gBAC/B;YACF;YAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBAChC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;oBACd,YAAY,OAAO,MAAM;gBAC3B;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,gBAAgB,MAAM;gBAChD,SAAS,CAAC,QAAQ,EAAE,aAAa,IAAI,EAAE,gBAAgB,MAAM,CAAC,gBAAgB,CAAC;gBAC/E;gBACA,YAAY,OAAO,MAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;gBACd,YAAY;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB,gBAAwB,EAAE,EAItD;QACD,IAAI;YACF,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,uBAAuB;YACvB,MAAM,kBAAkB,MAAM,gBAAgB,WAAW,CAAC,IAAI,uBAAuB;YAErF,uCAAuC;YACvC,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAA,QACvC,IAAI,KAAK,MAAM,SAAS,IAAI;YAG9B,IAAI,UAAU,MAAM,KAAK,GAAG;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,+BAA+B,EAAE,cAAc,WAAW,CAAC;oBACrE,cAAc;gBAChB;YACF;YAEA,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE;YAChD,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,UAAU,MAAM;gBAC1C,SAAS,CAAC,QAAQ,EAAE,aAAa,qBAAqB,CAAC;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,+DAA+D;YAC/D,gDAAgD;YAChD,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAE/B,6DAA6D;YAC7D,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC;YACvC,IAAI,CAAC,UAAU,SAAS,EAAE;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,UAAU,MAAM,IAAI;gBAC/B;YACF;YAEA,gEAAgE;YAChE,2CAA2C;YAC3C,MAAM,WAAW,MAAM,mBAAmB,MAAM,CAAC;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS,WACL,gCACA;YACN;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/orders/index.ts"], "sourcesContent": ["// Order Services\r\nexport { OrderGetService, orderGetService } from './get';\r\nexport { OrderPostService, orderPostService } from './post';\r\nexport { OrderUpdateService, orderUpdateService } from './update';\r\nexport { OrderDeleteService, orderDeleteService } from './delete';\r\n\r\n// Combined Order Service\r\nimport { orderGetService } from './get';\r\nimport { orderPostService } from './post';\r\nimport { orderUpdateService } from './update';\r\nimport { orderDeleteService } from './delete';\r\n\r\nexport class OrderService {\r\n  get = orderGetService;\r\n  post = orderPostService;\r\n  update = orderUpdateService;\r\n  delete = orderDeleteService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const orderService = new OrderService();\r\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;AACjB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,uIAAA,CAAA,kBAAe,CAAC;IACtB,OAAO,wIAAA,CAAA,mBAAgB,CAAC;IACxB,SAAS,0IAAA,CAAA,qBAAkB,CAAC;IAC5B,SAAS,0IAAA,CAAA,qBAAkB,CAAC;AAC9B;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 3160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { ApiEndpoints, PaginatedResponse } from '@/services/config/apiConfig';\r\nimport { BaseService, ServiceUtils } from '../../config/baseService';\r\n// import { ApiEndpoints } from '../../config/apiConfig';\r\nimport { \r\n  User, \r\n  UserFilterRequest,\r\n   \r\n} from '../../types/entities';\r\n\r\nexport class UserGetService extends BaseService {\r\n  /**\r\n   * Get all users\r\n   */\r\n  async getAll(): Promise<User[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.Base);\r\n    return this.handleResponse(\r\n      this.client.get<User[]>(ApiEndpoints.Users.Base)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get user by ID\r\n   */\r\n  async getById(id: number): Promise<User> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.ById(id));\r\n    return this.handleResponse(\r\n      this.client.get<User>(ApiEndpoints.Users.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get user by email\r\n   */\r\n  async getByEmail(email: string): Promise<User> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.ByEmail(email));\r\n    return this.handleResponse(\r\n      this.client.get<User>(ApiEndpoints.Users.ByEmail(email))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get users by role\r\n   */\r\n  async getByRole(role: string): Promise<User[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.ByRole(role));\r\n    return this.handleResponse(\r\n      this.client.get<User[]>(ApiEndpoints.Users.ByRole(role))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get active users\r\n   */\r\n  async getActive(): Promise<User[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.Active);\r\n    return this.handleResponse(\r\n      this.client.get<User[]>(ApiEndpoints.Users.Active)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get recent users\r\n   */\r\n  async getRecent(count: number = 10): Promise<User[]> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.Recent, { count });\r\n    return this.handleResponse(\r\n      this.client.get<User[]>(ApiEndpoints.Users.Recent, { count })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get user with orders\r\n   */\r\n  async getWithOrders(id: number): Promise<User> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.WithOrders(id));\r\n    return this.handleResponse(\r\n      this.client.get<User>(ApiEndpoints.Users.WithOrders(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if email exists\r\n   */\r\n  async emailExists(email: string): Promise<boolean> {\r\n    this.logApiCall('GET', ApiEndpoints.Users.EmailExists(email));\r\n    return this.handleResponse(\r\n      this.client.get<boolean>(ApiEndpoints.Users.EmailExists(email))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get users with advanced filtering and pagination\r\n   */\r\n  async getFiltered(filters: UserFilterRequest): Promise<PaginatedResponse<User>> {\r\n    const cleanFilters = ServiceUtils.cleanObject(filters);\r\n    this.logApiCall('GET', ApiEndpoints.Users.Filter, cleanFilters);\r\n    \r\n    return this.handlePaginatedResponse(\r\n      this.client.get<PaginatedResponse<User>>(\r\n        ApiEndpoints.Users.Filter, \r\n        cleanFilters\r\n      )\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get users with default pagination\r\n   */\r\n  async getPaginated(\r\n    pageNumber: number = 1, \r\n    pageSize: number = 10,\r\n    sortBy: string = 'createdAt',\r\n    sortDirection: 'asc' | 'desc' = 'desc'\r\n  ): Promise<PaginatedResponse<User>> {\r\n    const filters: UserFilterRequest = {\r\n      pageNumber,\r\n      pageSize,\r\n      sortBy,\r\n      sortDirection\r\n    };\r\n    \r\n    return this.getFiltered(filters);\r\n  }\r\n\r\n  /**\r\n   * Get admin users\r\n   */\r\n  async getAdmins(): Promise<User[]> {\r\n    return this.getByRole('admin');\r\n  }\r\n\r\n  /**\r\n   * Get customer users\r\n   */\r\n  async getCustomers(): Promise<User[]> {\r\n    return this.getByRole('customer');\r\n  }\r\n\r\n  /**\r\n   * Get inactive users\r\n   */\r\n  async getInactive(): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      active: false,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get users by country\r\n   */\r\n  async getByCountry(country: string): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      country,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get users by city\r\n   */\r\n  async getByCity(city: string): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      city,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Search users by name\r\n   */\r\n  async searchByName(name: string): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      name,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Search users by email pattern\r\n   */\r\n  async searchByEmail(emailPattern: string): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      email: emailPattern,\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get users created within date range\r\n   */\r\n  async getByDateRange(startDate: Date, endDate: Date): Promise<User[]> {\r\n    const filters: UserFilterRequest = {\r\n      createdAfter: ServiceUtils.formatDate(startDate),\r\n      createdBefore: ServiceUtils.formatDate(endDate),\r\n      pageSize: 100\r\n    };\r\n    \r\n    const result = await this.getFiltered(filters);\r\n    return result.data;\r\n  }\r\n\r\n  /**\r\n   * Get user statistics\r\n   */\r\n  async getStatistics(): Promise<{\r\n    totalUsers: number;\r\n    activeUsers: number;\r\n    inactiveUsers: number;\r\n    adminUsers: number;\r\n    customerUsers: number;\r\n    recentSignups: number; // Last 30 days\r\n  }> {\r\n    try {\r\n      const [allUsers, activeUsers, adminUsers, customerUsers] = await Promise.all([\r\n        this.getAll(),\r\n        this.getActive(),\r\n        this.getAdmins(),\r\n        this.getCustomers()\r\n      ]);\r\n\r\n      // Calculate recent signups (last 30 days)\r\n      const thirtyDaysAgo = new Date();\r\n      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n      const recentUsers = await this.getByDateRange(thirtyDaysAgo, new Date());\r\n\r\n      return {\r\n        totalUsers: allUsers.length,\r\n        activeUsers: activeUsers.length,\r\n        inactiveUsers: allUsers.length - activeUsers.length,\r\n        adminUsers: adminUsers.length,\r\n        customerUsers: customerUsers.length,\r\n        recentSignups: recentUsers.length\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting user statistics:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get user profile with additional data\r\n   */\r\n  async getProfile(id: number): Promise<{\r\n    user: User;\r\n    orderCount: number;\r\n    totalSpent: number;\r\n    lastOrderDate?: string;\r\n  }> {\r\n    try {\r\n      const userWithOrders = await this.getWithOrders(id);\r\n      \r\n      // Calculate order statistics (this would require order data in the response)\r\n      // For now, we'll return basic user data\r\n      return {\r\n        user: userWithOrders,\r\n        orderCount: 0, // Would be calculated from orders\r\n        totalSpent: 0, // Would be calculated from orders\r\n        lastOrderDate: undefined // Would be from most recent order\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting user profile:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate user exists and is active\r\n   */\r\n  async validateUser(id: number): Promise<{\r\n    exists: boolean;\r\n    active: boolean;\r\n    user?: User;\r\n  }> {\r\n    try {\r\n      const user = await this.getById(id);\r\n      return {\r\n        exists: true,\r\n        active: user.active,\r\n        user\r\n      };\r\n    } catch (error) {\r\n      return {\r\n        exists: false,\r\n        active: false\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate admin credentials\r\n   */\r\n  async validateAdminCredentials(email: string, password: string): Promise<User | null> {\r\n    try {\r\n      // First get the user by email\r\n      const user = await this.getByEmail(email);\r\n\r\n      // Check if user exists and is an admin\r\n      if (!user || user.role !== 'admin' || !user.active) {\r\n        return null;\r\n      }\r\n\r\n      // For now, we'll use a simple validation approach\r\n      // In a real application, you'd want to implement proper JWT authentication\r\n      // Since the backend doesn't have a login endpoint, we'll validate against known admin\r\n      const isValidAdmin = email === '<EMAIL>' && password === '132Trent@!';\r\n\r\n      return isValidAdmin ? user : null;\r\n    } catch (error) {\r\n      console.error('Error validating admin credentials:', error);\r\n      return null;\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const userGetService = new UserGetService();\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;AACA;;;AAQO,MAAM,uBAAuB,wIAAA,CAAA,cAAW;IAC7C;;GAEC,GACD,MAAM,SAA0B;QAC9B,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;QAC9C,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI;IAEnD;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAiB;QACvC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;QAC/C,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;IAElD;IAEA;;GAEC,GACD,MAAM,WAAW,KAAa,EAAiB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO,CAAC;IAErD;IAEA;;GAEC,GACD,MAAM,UAAU,IAAY,EAAmB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,CAAC;QACjD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,CAAC;IAEtD;IAEA;;GAEC,GACD,MAAM,YAA6B;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM;QAChD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM;IAErD;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAAmB;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,EAAE;YAAE;QAAM;QAC1D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,EAAE;YAAE;QAAM;IAE/D;IAEA;;GAEC,GACD,MAAM,cAAc,EAAU,EAAiB;QAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;QACrD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;IAExD;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAoB;QACjD,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,WAAW,CAAC;QACtD,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAU,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,WAAW,CAAC;IAE5D;IAEA;;GAEC,GACD,MAAM,YAAY,OAA0B,EAAoC;QAC9E,MAAM,eAAe,wIAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,EAAE;QAElD,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,MAAM,EACzB;IAGN;IAEA;;GAEC,GACD,MAAM,aACJ,aAAqB,CAAC,EACtB,WAAmB,EAAE,EACrB,SAAiB,WAAW,EAC5B,gBAAgC,MAAM,EACJ;QAClC,MAAM,UAA6B;YACjC;YACA;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,MAAM,YAA6B;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA;;GAEC,GACD,MAAM,eAAgC;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA;;GAEC,GACD,MAAM,cAA+B;QACnC,MAAM,UAA6B;YACjC,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,aAAa,OAAe,EAAmB;QACnD,MAAM,UAA6B;YACjC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,UAAU,IAAY,EAAmB;QAC7C,MAAM,UAA6B;YACjC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,aAAa,IAAY,EAAmB;QAChD,MAAM,UAA6B;YACjC;YACA,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,cAAc,YAAoB,EAAmB;QACzD,MAAM,UAA6B;YACjC,OAAO;YACP,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAmB;QACpE,MAAM,UAA6B;YACjC,cAAc,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACtC,eAAe,wIAAA,CAAA,eAAY,CAAC,UAAU,CAAC;YACvC,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,OAAO,IAAI;IACpB;IAEA;;GAEC,GACD,MAAM,gBAOH;QACD,IAAI;YACF,MAAM,CAAC,UAAU,aAAa,YAAY,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3E,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,YAAY;aAClB;YAED,0CAA0C;YAC1C,MAAM,gBAAgB,IAAI;YAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;YAChD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,IAAI;YAEjE,OAAO;gBACL,YAAY,SAAS,MAAM;gBAC3B,aAAa,YAAY,MAAM;gBAC/B,eAAe,SAAS,MAAM,GAAG,YAAY,MAAM;gBACnD,YAAY,WAAW,MAAM;gBAC7B,eAAe,cAAc,MAAM;gBACnC,eAAe,YAAY,MAAM;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAKxB;QACD,IAAI;YACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,aAAa,CAAC;YAEhD,6EAA6E;YAC7E,wCAAwC;YACxC,OAAO;gBACL,MAAM;gBACN,YAAY;gBACZ,YAAY;gBACZ,eAAe,UAAU,kCAAkC;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,EAAU,EAI1B;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAChC,OAAO;gBACL,QAAQ;gBACR,QAAQ,KAAK,MAAM;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,QAAQ;gBACR,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBAAyB,KAAa,EAAE,QAAgB,EAAwB;QACpF,IAAI;YACF,8BAA8B;YAC9B,MAAM,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC;YAEnC,uCAAuC;YACvC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW,CAAC,KAAK,MAAM,EAAE;gBAClD,OAAO;YACT;YAEA,kDAAkD;YAClD,2EAA2E;YAC3E,sFAAsF;YACtF,MAAM,eAAe,UAAU,kCAAkC,aAAa;YAE9E,OAAO,eAAe,OAAO;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF;AACF;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/post.ts"], "sourcesContent": ["import { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { User, CreateUserRequest } from '../../types/entities';\r\n\r\nexport class UserPostService extends BaseService {\r\n  /**\r\n   * Create a new user\r\n   */\r\n  async create(data: CreateUserRequest): Promise<User> {\r\n    this.logApiCall('POST', ApiEndpoints.Users.Base, { ...data, password: '[HIDDEN]' });\r\n    \r\n    // Validate required fields\r\n    this.validateCreateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<User>(ApiEndpoints.Users.Base, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a customer user\r\n   */\r\n  async createCustomer(\r\n    email: string,\r\n    password: string,\r\n    name?: string,\r\n    phoneNumber?: string,\r\n    address?: {\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    }\r\n  ): Promise<User> {\r\n    const data: CreateUserRequest = {\r\n      role: 'customer',\r\n      email,\r\n      password,\r\n      name,\r\n      phoneNumber,\r\n      country: address?.country,\r\n      city: address?.city,\r\n      zipCode: address?.zipCode,\r\n      active: true\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create an admin user\r\n   */\r\n  async createAdmin(\r\n    email: string,\r\n    password: string,\r\n    name?: string,\r\n    phoneNumber?: string\r\n  ): Promise<User> {\r\n    const data: CreateUserRequest = {\r\n      role: 'admin',\r\n      email,\r\n      password,\r\n      name,\r\n      phoneNumber,\r\n      active: true\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Register a new customer (public registration)\r\n   */\r\n  async register(\r\n    email: string,\r\n    password: string,\r\n    name?: string,\r\n    phoneNumber?: string\r\n  ): Promise<User> {\r\n    // Check if email already exists\r\n    const { userGetService } = await import('./get');\r\n    const emailExists = await userGetService.emailExists(email);\r\n    \r\n    if (emailExists) {\r\n      throw new Error('Email already exists');\r\n    }\r\n\r\n    return this.createCustomer(email, password, name, phoneNumber);\r\n  }\r\n\r\n  /**\r\n   * Create guest user (for guest checkout)\r\n   */\r\n  async createGuest(email: string): Promise<User> {\r\n    const data: CreateUserRequest = {\r\n      role: 'guest',\r\n      email,\r\n      password: this.generateRandomPassword(), // Generate temporary password\r\n      active: true\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Create multiple users in batch\r\n   */\r\n  async createBatch(users: CreateUserRequest[]): Promise<User[]> {\r\n    this.logApiCall('POST', 'Batch Users', { count: users.length });\r\n    \r\n    const promises = users.map(user => this.create(user));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Import users from CSV data\r\n   */\r\n  async importFromCsv(csvData: Array<{\r\n    email: string;\r\n    name?: string;\r\n    role?: string;\r\n    phoneNumber?: string;\r\n    country?: string;\r\n    city?: string;\r\n    zipCode?: string;\r\n  }>): Promise<{\r\n    success: boolean;\r\n    imported: number;\r\n    failed: number;\r\n    errors: string[];\r\n  }> {\r\n    const results = {\r\n      success: true,\r\n      imported: 0,\r\n      failed: 0,\r\n      errors: [] as string[]\r\n    };\r\n\r\n    for (const [index, userData] of csvData.entries()) {\r\n      try {\r\n        const createData: CreateUserRequest = {\r\n          role: userData.role || 'customer',\r\n          email: userData.email,\r\n          password: this.generateRandomPassword(),\r\n          name: userData.name,\r\n          phoneNumber: userData.phoneNumber,\r\n          country: userData.country,\r\n          city: userData.city,\r\n          zipCode: userData.zipCode,\r\n          active: true\r\n        };\r\n\r\n        await this.create(createData);\r\n        results.imported++;\r\n      } catch (error) {\r\n        results.failed++;\r\n        results.errors.push(\r\n          `Row ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n        );\r\n      }\r\n    }\r\n\r\n    results.success = results.failed === 0;\r\n    return results;\r\n  }\r\n\r\n  /**\r\n   * Create user with email verification\r\n   */\r\n  async createWithVerification(data: CreateUserRequest): Promise<{\r\n    user: User;\r\n    verificationRequired: boolean;\r\n    verificationToken?: string;\r\n  }> {\r\n    try {\r\n      const user = await this.create(data);\r\n      \r\n      // In a real application, you would:\r\n      // 1. Generate a verification token\r\n      // 2. Send verification email\r\n      // 3. Set user as inactive until verified\r\n      \r\n      return {\r\n        user,\r\n        verificationRequired: true,\r\n        verificationToken: this.generateVerificationToken()\r\n      };\r\n    } catch (error) {\r\n      console.error('Error creating user with verification:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create user with social login data\r\n   */\r\n  async createFromSocialLogin(\r\n    provider: 'google' | 'facebook' | 'apple',\r\n    socialId: string,\r\n    email: string,\r\n    name?: string\r\n  ): Promise<User> {\r\n    const data: CreateUserRequest = {\r\n      role: 'customer',\r\n      email,\r\n      password: this.generateRandomPassword(), // Social users don't need password\r\n      name,\r\n      active: true\r\n    };\r\n\r\n    // In a real application, you would also store the social provider info\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Generate random password for system-created users\r\n   */\r\n  private generateRandomPassword(): string {\r\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\r\n    let password = '';\r\n    for (let i = 0; i < 12; i++) {\r\n      password += chars.charAt(Math.floor(Math.random() * chars.length));\r\n    }\r\n    return password;\r\n  }\r\n\r\n  /**\r\n   * Generate verification token\r\n   */\r\n  private generateVerificationToken(): string {\r\n    return Math.random().toString(36).substring(2, 15) + \r\n           Math.random().toString(36).substring(2, 15);\r\n  }\r\n\r\n  /**\r\n   * Validate create user request\r\n   */\r\n  private validateCreateRequest(data: CreateUserRequest): void {\r\n    if (!data.email || !ServiceUtils.isValidEmail(data.email)) {\r\n      throw new Error('Valid email is required');\r\n    }\r\n\r\n    if (!data.password || data.password.length < 6) {\r\n      throw new Error('Password must be at least 6 characters long');\r\n    }\r\n\r\n    if (!data.role || !['admin', 'customer', 'guest'].includes(data.role)) {\r\n      throw new Error('Valid role is required (admin, customer, or guest)');\r\n    }\r\n\r\n    // Validate phone number format if provided\r\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\r\n      throw new Error('Invalid phone number format');\r\n    }\r\n\r\n    // Validate name length if provided\r\n    if (data.name && data.name.length > 100) {\r\n      throw new Error('Name must be 100 characters or less');\r\n    }\r\n\r\n    // Validate address fields if provided\r\n    if (data.country && data.country.length > 100) {\r\n      throw new Error('Country must be 100 characters or less');\r\n    }\r\n\r\n    if (data.city && data.city.length > 100) {\r\n      throw new Error('City must be 100 characters or less');\r\n    }\r\n\r\n    if (data.zipCode && data.zipCode.length > 20) {\r\n      throw new Error('Zip code must be 20 characters or less');\r\n    }\r\n\r\n    // Password strength validation\r\n    if (!this.isStrongPassword(data.password)) {\r\n      throw new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate phone number format\r\n   */\r\n  private isValidPhoneNumber(phone: string): boolean {\r\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\r\n  }\r\n\r\n  /**\r\n   * Check password strength\r\n   */\r\n  private isStrongPassword(password: string): boolean {\r\n    const hasUpperCase = /[A-Z]/.test(password);\r\n    const hasLowerCase = /[a-z]/.test(password);\r\n    const hasNumbers = /\\d/.test(password);\r\n    \r\n    return hasUpperCase && hasLowerCase && hasNumbers;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const userPostService = new UserPostService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,wBAAwB,wIAAA,CAAA,cAAW;IAC9C;;GAEC,GACD,MAAM,OAAO,IAAuB,EAAiB;QACnD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,EAAE;YAAE,GAAG,IAAI;YAAE,UAAU;QAAW;QAEjF,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,EAAE;IAEpD;IAEA;;GAEC,GACD,MAAM,eACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA;YACA;YACA;YACA,SAAS,SAAS;YAClB,MAAM,SAAS;YACf,SAAS,SAAS;YAClB,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACL;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA;YACA;YACA;YACA,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,SACJ,KAAa,EACb,QAAgB,EAChB,IAAa,EACb,WAAoB,EACL;QACf,gCAAgC;QAChC,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,MAAM,cAAc,MAAM,eAAe,WAAW,CAAC;QAErD,IAAI,aAAa;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,UAAU,MAAM;IACpD;IAEA;;GAEC,GACD,MAAM,YAAY,KAAa,EAAiB;QAC9C,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA,UAAU,IAAI,CAAC,sBAAsB;YACrC,QAAQ;QACV;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,YAAY,KAA0B,EAAmB;QAC7D,IAAI,CAAC,UAAU,CAAC,QAAQ,eAAe;YAAE,OAAO,MAAM,MAAM;QAAC;QAE7D,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,CAAC;QAC/C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,cAAc,OAQlB,EAKC;QACD,MAAM,UAAU;YACd,SAAS;YACT,UAAU;YACV,QAAQ;YACR,QAAQ,EAAE;QACZ;QAEA,KAAK,MAAM,CAAC,OAAO,SAAS,IAAI,QAAQ,OAAO,GAAI;YACjD,IAAI;gBACF,MAAM,aAAgC;oBACpC,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,SAAS,KAAK;oBACrB,UAAU,IAAI,CAAC,sBAAsB;oBACrC,MAAM,SAAS,IAAI;oBACnB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,QAAQ;gBACV;gBAEA,MAAM,IAAI,CAAC,MAAM,CAAC;gBAClB,QAAQ,QAAQ;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,MAAM;gBACd,QAAQ,MAAM,CAAC,IAAI,CACjB,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAEnF;QACF;QAEA,QAAQ,OAAO,GAAG,QAAQ,MAAM,KAAK;QACrC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,uBAAuB,IAAuB,EAIjD;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC;YAE/B,oCAAoC;YACpC,mCAAmC;YACnC,6BAA6B;YAC7B,yCAAyC;YAEzC,OAAO;gBACL;gBACA,sBAAsB;gBACtB,mBAAmB,IAAI,CAAC,yBAAyB;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBACJ,QAAyC,EACzC,QAAgB,EAChB,KAAa,EACb,IAAa,EACE;QACf,MAAM,OAA0B;YAC9B,MAAM;YACN;YACA,UAAU,IAAI,CAAC,sBAAsB;YACrC;YACA,QAAQ;QACV;QAEA,uEAAuE;QACvE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,AAAQ,yBAAiC;QACvC,MAAM,QAAQ;QACd,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,YAAY,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;QAClE;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,4BAAoC;QAC1C,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAuB,EAAQ;QAC3D,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,wIAAA,CAAA,eAAY,CAAC,YAAY,CAAC,KAAK,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrE,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK;YAC7C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;QAEA,+BAA+B;QAC/B,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,QAAQ,GAAG;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAAgB,EAAW;QAClD,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,aAAa,KAAK,IAAI,CAAC;QAE7B,OAAO,gBAAgB,gBAAgB;IACzC;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 3620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/update.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { User, UpdateUserRequest } from '../../types/entities';\r\n\r\nexport class UserUpdateService extends BaseService {\r\n  /**\r\n   * Update an existing user\r\n   */\r\n  async update(id: number, data: UpdateUserRequest): Promise<User> {\r\n    this.logApiCall('PUT', ApiEndpoints.Users.ById(id), data);\r\n    \r\n    // Validate required fields\r\n    this.validateUpdateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.put<User>(ApiEndpoints.Users.ById(id), data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Activate a user\r\n   */\r\n  async activate(id: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', ApiEndpoints.Users.Activate(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.patch(ApiEndpoints.Users.Activate(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Deactivate a user\r\n   */\r\n  async deactivate(id: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', ApiEndpoints.Users.Deactivate(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.patch(ApiEndpoints.Users.Deactivate(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update user profile information\r\n   */\r\n  async updateProfile(\r\n    id: number,\r\n    name?: string,\r\n    phoneNumber?: string,\r\n    address?: {\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    }\r\n  ): Promise<User> {\r\n    const currentUser = await this.getCurrentUser(id);\r\n    \r\n    const data: UpdateUserRequest = {\r\n      role: currentUser.role,\r\n      name: name ?? currentUser.name,\r\n      phoneNumber: phoneNumber ?? currentUser.phoneNumber,\r\n      country: address?.country ?? currentUser.country,\r\n      city: address?.city ?? currentUser.city,\r\n      zipCode: address?.zipCode ?? currentUser.zipCode,\r\n      active: currentUser.active\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update user contact information\r\n   */\r\n  async updateContact(\r\n    id: number,\r\n    phoneNumber?: string,\r\n    address?: {\r\n      country?: string;\r\n      city?: string;\r\n      zipCode?: string;\r\n    }\r\n  ): Promise<User> {\r\n    const currentUser = await this.getCurrentUser(id);\r\n    \r\n    const data: UpdateUserRequest = {\r\n      role: currentUser.role,\r\n      name: currentUser.name,\r\n      phoneNumber: phoneNumber ?? currentUser.phoneNumber,\r\n      country: address?.country ?? currentUser.country,\r\n      city: address?.city ?? currentUser.city,\r\n      zipCode: address?.zipCode ?? currentUser.zipCode,\r\n      active: currentUser.active\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update user role\r\n   */\r\n  async updateRole(id: number, newRole: string): Promise<User> {\r\n    if (!['admin', 'customer', 'guest'].includes(newRole)) {\r\n      throw new Error('Invalid role. Must be admin, customer, or guest');\r\n    }\r\n\r\n    const currentUser = await this.getCurrentUser(id);\r\n    \r\n    const data: UpdateUserRequest = {\r\n      role: newRole,\r\n      name: currentUser.name,\r\n      phoneNumber: currentUser.phoneNumber,\r\n      country: currentUser.country,\r\n      city: currentUser.city,\r\n      zipCode: currentUser.zipCode,\r\n      active: currentUser.active\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Update user address\r\n   */\r\n  async updateAddress(\r\n    id: number,\r\n    country?: string,\r\n    city?: string,\r\n    zipCode?: string\r\n  ): Promise<User> {\r\n    const currentUser = await this.getCurrentUser(id);\r\n    \r\n    const data: UpdateUserRequest = {\r\n      role: currentUser.role,\r\n      name: currentUser.name,\r\n      phoneNumber: currentUser.phoneNumber,\r\n      country: country ?? currentUser.country,\r\n      city: city ?? currentUser.city,\r\n      zipCode: zipCode ?? currentUser.zipCode,\r\n      active: currentUser.active\r\n    };\r\n\r\n    return this.update(id, data);\r\n  }\r\n\r\n  /**\r\n   * Toggle user active status\r\n   */\r\n  async toggleActiveStatus(id: number): Promise<User> {\r\n    const currentUser = await this.getCurrentUser(id);\r\n    \r\n    if (currentUser.active) {\r\n      await this.deactivate(id);\r\n    } else {\r\n      await this.activate(id);\r\n    }\r\n\r\n    // Return updated user\r\n    return this.getCurrentUser(id);\r\n  }\r\n\r\n  /**\r\n   * Bulk update user roles\r\n   */\r\n  async bulkUpdateRole(userIds: number[], newRole: string): Promise<{\r\n    success: boolean;\r\n    updatedCount: number;\r\n    errors: string[];\r\n  }> {\r\n    this.logApiCall('PUT', 'Bulk Update User Roles', { \r\n      userIds, \r\n      newRole, \r\n      count: userIds.length \r\n    });\r\n\r\n    const results = await Promise.allSettled(\r\n      userIds.map(id => this.updateRole(id, newRole))\r\n    );\r\n\r\n    const successful = results.filter(result => \r\n      result.status === 'fulfilled'\r\n    ).length;\r\n\r\n    const errors = results\r\n      .filter(result => result.status === 'rejected')\r\n      .map((result, index) => \r\n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\r\n      );\r\n\r\n    return {\r\n      success: successful === userIds.length,\r\n      updatedCount: successful,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Bulk activate users\r\n   */\r\n  async bulkActivate(userIds: number[]): Promise<{\r\n    success: boolean;\r\n    activatedCount: number;\r\n    errors: string[];\r\n  }> {\r\n    this.logApiCall('PATCH', 'Bulk Activate Users', { \r\n      userIds, \r\n      count: userIds.length \r\n    });\r\n\r\n    const results = await Promise.allSettled(\r\n      userIds.map(id => this.activate(id))\r\n    );\r\n\r\n    const successful = results.filter(result => \r\n      result.status === 'fulfilled' && result.value === true\r\n    ).length;\r\n\r\n    const errors = results\r\n      .filter(result => result.status === 'rejected')\r\n      .map((result, index) => \r\n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\r\n      );\r\n\r\n    return {\r\n      success: successful === userIds.length,\r\n      activatedCount: successful,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Bulk deactivate users\r\n   */\r\n  async bulkDeactivate(userIds: number[]): Promise<{\r\n    success: boolean;\r\n    deactivatedCount: number;\r\n    errors: string[];\r\n  }> {\r\n    this.logApiCall('PATCH', 'Bulk Deactivate Users', { \r\n      userIds, \r\n      count: userIds.length \r\n    });\r\n\r\n    const results = await Promise.allSettled(\r\n      userIds.map(id => this.deactivate(id))\r\n    );\r\n\r\n    const successful = results.filter(result => \r\n      result.status === 'fulfilled' && result.value === true\r\n    ).length;\r\n\r\n    const errors = results\r\n      .filter(result => result.status === 'rejected')\r\n      .map((result, index) => \r\n        `User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\r\n      );\r\n\r\n    return {\r\n      success: successful === userIds.length,\r\n      deactivatedCount: successful,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Promote user to admin\r\n   */\r\n  async promoteToAdmin(id: number): Promise<User> {\r\n    return this.updateRole(id, 'admin');\r\n  }\r\n\r\n  /**\r\n   * Demote admin to customer\r\n   */\r\n  async demoteToCustomer(id: number): Promise<User> {\r\n    return this.updateRole(id, 'customer');\r\n  }\r\n\r\n  /**\r\n   * Update user preferences (placeholder for future implementation)\r\n   */\r\n  async updatePreferences(\r\n    id: number,\r\n    preferences: Record<string, any>\r\n  ): Promise<User> {\r\n    // This would require extending the user model to include preferences\r\n    // For now, we'll just return the current user\r\n    console.log('User preferences update not implemented:', preferences);\r\n    return this.getCurrentUser(id);\r\n  }\r\n\r\n  /**\r\n   * Get current user data\r\n   */\r\n  private async getCurrentUser(id: number): Promise<User> {\r\n    const response = await this.client.get<User>(ApiEndpoints.Users.ById(id));\r\n    \r\n    if (!response.success || !response.data) {\r\n      throw new Error('User not found');\r\n    }\r\n    \r\n    return response.data;\r\n  }\r\n\r\n  /**\r\n   * Validate update user request\r\n   */\r\n  private validateUpdateRequest(data: UpdateUserRequest): void {\r\n    if (!data.role || !['admin', 'customer', 'guest'].includes(data.role)) {\r\n      throw new Error('Valid role is required (admin, customer, or guest)');\r\n    }\r\n\r\n    // Validate phone number format if provided\r\n    if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {\r\n      throw new Error('Invalid phone number format');\r\n    }\r\n\r\n    // Validate name length if provided\r\n    if (data.name && data.name.length > 100) {\r\n      throw new Error('Name must be 100 characters or less');\r\n    }\r\n\r\n    // Validate address fields if provided\r\n    if (data.country && data.country.length > 100) {\r\n      throw new Error('Country must be 100 characters or less');\r\n    }\r\n\r\n    if (data.city && data.city.length > 100) {\r\n      throw new Error('City must be 100 characters or less');\r\n    }\r\n\r\n    if (data.zipCode && data.zipCode.length > 20) {\r\n      throw new Error('Zip code must be 20 characters or less');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate phone number format\r\n   */\r\n  private isValidPhoneNumber(phone: string): boolean {\r\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const userUpdateService = new UserUpdateService();\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;;;AAGO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAE,IAAuB,EAAiB;QAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;QAEpD,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;IAEvD;IAEA;;GAEC,GACD,MAAM,SAAS,EAAU,EAAoB;QAC3C,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;QAErD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;IAElD;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;QAEvD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,CAAC;IAEpD;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,IAAa,EACb,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,QAAQ,YAAY,IAAI;YAC9B,aAAa,eAAe,YAAY,WAAW;YACnD,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,MAAM,SAAS,QAAQ,YAAY,IAAI;YACvC,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,WAAoB,EACpB,OAIC,EACc;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,YAAY,IAAI;YACtB,aAAa,eAAe,YAAY,WAAW;YACnD,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,MAAM,SAAS,QAAQ,YAAY,IAAI;YACvC,SAAS,SAAS,WAAW,YAAY,OAAO;YAChD,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAE,OAAe,EAAiB;QAC3D,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,UAAU;YACrD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM;YACN,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,SAAS,YAAY,OAAO;YAC5B,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,OAAO;YAC5B,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,cACJ,EAAU,EACV,OAAgB,EAChB,IAAa,EACb,OAAgB,EACD;QACf,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,MAAM,OAA0B;YAC9B,MAAM,YAAY,IAAI;YACtB,MAAM,YAAY,IAAI;YACtB,aAAa,YAAY,WAAW;YACpC,SAAS,WAAW,YAAY,OAAO;YACvC,MAAM,QAAQ,YAAY,IAAI;YAC9B,SAAS,WAAW,YAAY,OAAO;YACvC,QAAQ,YAAY,MAAM;QAC5B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI;IACzB;IAEA;;GAEC,GACD,MAAM,mBAAmB,EAAU,EAAiB;QAClD,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAE9C,IAAI,YAAY,MAAM,EAAE;YACtB,MAAM,IAAI,CAAC,UAAU,CAAC;QACxB,OAAO;YACL,MAAM,IAAI,CAAC,QAAQ,CAAC;QACtB;QAEA,sBAAsB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA;;GAEC,GACD,MAAM,eAAe,OAAiB,EAAE,OAAe,EAIpD;QACD,IAAI,CAAC,UAAU,CAAC,OAAO,0BAA0B;YAC/C;YACA;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC,IAAI;QAGxC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,aAClB,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,cAAc;YACd;QACF;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,OAAiB,EAIjC;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,uBAAuB;YAC9C;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,QAAQ,CAAC;QAGlC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,gBAAgB;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,OAAiB,EAInC;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,yBAAyB;YAChD;YACA,OAAO,QAAQ,MAAM;QACvB;QAEA,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,QAAQ,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC;QAGpC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG/F,OAAO;YACL,SAAS,eAAe,QAAQ,MAAM;YACtC,kBAAkB;YAClB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,EAAU,EAAiB;QAC9C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,iBAAiB,EAAU,EAAiB;QAChD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC7B;IAEA;;GAEC,GACD,MAAM,kBACJ,EAAU,EACV,WAAgC,EACjB;QACf,qEAAqE;QACrE,8CAA8C;QAC9C,QAAQ,GAAG,CAAC,4CAA4C;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEA;;GAEC,GACD,MAAc,eAAe,EAAU,EAAiB;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;QAErE,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAuB,EAAQ;QAC3D,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;YAAC;YAAS;YAAY;SAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrE,MAAM,IAAI,MAAM;QAClB;QAEA,2CAA2C;QAC3C,IAAI,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAClE,MAAM,IAAI,MAAM;QAClB;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK;YAC7C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5C,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 3847, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class UserDeleteService extends BaseService {\r\n  /**\r\n   * Delete a user by ID\r\n   */\r\n  async delete(id: number): Promise<boolean> {\r\n    this.logApiCall('DELETE', ApiEndpoints.Users.ById(id));\r\n    \r\n    return this.handleVoidResponse(\r\n      this.client.delete(ApiEndpoints.Users.ById(id))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Delete multiple users\r\n   */\r\n  async deleteBatch(ids: number[]): Promise<boolean[]> {\r\n    this.logApiCall('DELETE', 'Batch Users', { count: ids.length });\r\n    \r\n    const promises = ids.map(id => this.delete(id));\r\n    return Promise.all(promises);\r\n  }\r\n\r\n  /**\r\n   * Soft delete - deactivate user instead of deleting\r\n   */\r\n  async softDelete(id: number): Promise<boolean> {\r\n    this.logApiCall('PATCH', `Soft Delete User ${id}`);\r\n    \r\n    try {\r\n      const { userUpdateService } = await import('./update');\r\n      await userUpdateService.deactivate(id);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Failed to soft delete user:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if user can be safely deleted\r\n   */\r\n  async canDelete(id: number): Promise<{\r\n    canDelete: boolean;\r\n    reason?: string;\r\n    hasOrders?: boolean;\r\n    isAdmin?: boolean;\r\n  }> {\r\n    try {\r\n      const { userGetService } = await import('./get');\r\n      const user = await userGetService.getById(id);\r\n\r\n      // Check if user is admin\r\n      const isAdmin = user.role === 'admin';\r\n      \r\n      // Check if user has orders (this would require order service integration)\r\n      // For now, we'll assume users can be deleted\r\n      const hasOrders = false; // Would check order history\r\n\r\n      // Admins typically shouldn't be deleted\r\n      const canDelete = !isAdmin && !hasOrders;\r\n      \r\n      let reason: string | undefined;\r\n      if (!canDelete) {\r\n        if (isAdmin) {\r\n          reason = 'Cannot delete admin users';\r\n        } else if (hasOrders) {\r\n          reason = 'Cannot delete users with order history';\r\n        }\r\n      }\r\n\r\n      return {\r\n        canDelete,\r\n        reason,\r\n        hasOrders,\r\n        isAdmin\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking if user can be deleted:', error);\r\n      return {\r\n        canDelete: false,\r\n        reason: 'Error checking user dependencies'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Safe delete - checks dependencies before deleting\r\n   */\r\n  async safeDelete(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const deleteCheck = await this.canDelete(id);\r\n      \r\n      if (!deleteCheck.canDelete) {\r\n        return {\r\n          success: false,\r\n          message: deleteCheck.reason || 'User cannot be deleted'\r\n        };\r\n      }\r\n\r\n      const deleted = await this.delete(id);\r\n      \r\n      if (deleted) {\r\n        return {\r\n          success: true,\r\n          message: 'User deleted successfully'\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: 'Failed to delete user'\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error during safe delete:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Archive user (soft delete with archive flag)\r\n   */\r\n  async archive(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      // Deactivate the user\r\n      const { userUpdateService } = await import('./update');\r\n      await userUpdateService.deactivate(id);\r\n      \r\n      return {\r\n        success: true,\r\n        message: 'User archived successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error archiving user:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to archive user'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Restore archived user\r\n   */\r\n  async restore(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const { userUpdateService } = await import('./update');\r\n      await userUpdateService.activate(id);\r\n      \r\n      return {\r\n        success: true,\r\n        message: 'User restored successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error restoring user:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to restore user'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete inactive users (cleanup operation)\r\n   */\r\n  async deleteInactiveUsers(inactiveDays: number = 365): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n  }> {\r\n    try {\r\n      const { userGetService } = await import('./get');\r\n      const cutoffDate = new Date();\r\n      cutoffDate.setDate(cutoffDate.getDate() - inactiveDays);\r\n\r\n      // Get inactive users\r\n      const inactiveUsers = await userGetService.getInactive();\r\n      \r\n      // Filter users inactive for specified period\r\n      const oldInactiveUsers = inactiveUsers.filter(user => \r\n        new Date(user.createdAt) < cutoffDate\r\n      );\r\n\r\n      if (oldInactiveUsers.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: `No inactive users older than ${inactiveDays} days found`,\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      // Check which users can be safely deleted\r\n      const deletableUsers: number[] = [];\r\n      for (const user of oldInactiveUsers) {\r\n        const canDelete = await this.canDelete(user.id);\r\n        if (canDelete.canDelete) {\r\n          deletableUsers.push(user.id);\r\n        }\r\n      }\r\n\r\n      if (deletableUsers.length === 0) {\r\n        return {\r\n          success: false,\r\n          message: 'No inactive users can be safely deleted',\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      const results = await this.deleteBatch(deletableUsers);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === deletableUsers.length,\r\n        message: `Deleted ${deletedCount} inactive users`,\r\n        deletedCount\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting inactive users:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete inactive users',\r\n        deletedCount: 0\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete guest users (cleanup operation)\r\n   */\r\n  async deleteGuestUsers(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    deletedCount: number;\r\n  }> {\r\n    try {\r\n      const { userGetService } = await import('./get');\r\n      const guestUsers = await userGetService.getByRole('guest');\r\n      \r\n      if (guestUsers.length === 0) {\r\n        return {\r\n          success: true,\r\n          message: 'No guest users found',\r\n          deletedCount: 0\r\n        };\r\n      }\r\n\r\n      const userIds = guestUsers.map(user => user.id);\r\n      const results = await this.deleteBatch(userIds);\r\n      const deletedCount = results.filter(result => result).length;\r\n      \r\n      return {\r\n        success: deletedCount === guestUsers.length,\r\n        message: `Deleted ${deletedCount} guest users`,\r\n        deletedCount\r\n      };\r\n    } catch (error) {\r\n      console.error('Error deleting guest users:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to delete guest users',\r\n        deletedCount: 0\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Anonymize user data (GDPR compliance)\r\n   */\r\n  async anonymize(id: number): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      const { userUpdateService } = await import('./update');\r\n      \r\n      // Replace personal data with anonymized values\r\n      const anonymizedData = {\r\n        role: 'customer',\r\n        name: 'Anonymized User',\r\n        phoneNumber: undefined,\r\n        country: undefined,\r\n        city: undefined,\r\n        zipCode: undefined,\r\n        active: false\r\n      };\r\n\r\n      await userUpdateService.update(id, anonymizedData);\r\n      \r\n      return {\r\n        success: true,\r\n        message: 'User data anonymized successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error anonymizing user:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to anonymize user'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Bulk soft delete (deactivate multiple users)\r\n   */\r\n  async bulkSoftDelete(ids: number[]): Promise<{\r\n    success: boolean;\r\n    deactivatedCount: number;\r\n    errors: string[];\r\n  }> {\r\n    this.logApiCall('PATCH', 'Bulk Soft Delete Users', { count: ids.length });\r\n\r\n    const results = await Promise.allSettled(\r\n      ids.map(id => this.softDelete(id))\r\n    );\r\n\r\n    const successful = results.filter(result => \r\n      result.status === 'fulfilled' && result.value === true\r\n    ).length;\r\n\r\n    const errors = results\r\n      .filter(result => result.status === 'rejected')\r\n      .map((result, index) => \r\n        `User ${ids[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`\r\n      );\r\n\r\n    return {\r\n      success: successful === ids.length,\r\n      deactivatedCount: successful,\r\n      errors\r\n    };\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const userDeleteService = new UserDeleteService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,OAAO,EAAU,EAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;QAElD,OAAO,IAAI,CAAC,kBAAkB,CAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,IAAI,CAAC;IAE/C;IAEA;;GAEC,GACD,MAAM,YAAY,GAAa,EAAsB;QACnD,IAAI,CAAC,UAAU,CAAC,UAAU,eAAe;YAAE,OAAO,IAAI,MAAM;QAAC;QAE7D,MAAM,WAAW,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,MAAM,CAAC;QAC3C,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI;QAEjD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,UAAU,CAAC;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAKvB;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,OAAO,MAAM,eAAe,OAAO,CAAC;YAE1C,yBAAyB;YACzB,MAAM,UAAU,KAAK,IAAI,KAAK;YAE9B,0EAA0E;YAC1E,6CAA6C;YAC7C,MAAM,YAAY,OAAO,4BAA4B;YAErD,wCAAwC;YACxC,MAAM,YAAY,CAAC,WAAW,CAAC;YAE/B,IAAI;YACJ,IAAI,CAAC,WAAW;gBACd,IAAI,SAAS;oBACX,SAAS;gBACX,OAAO,IAAI,WAAW;oBACpB,SAAS;gBACX;YACF;YAEA,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBACL,WAAW;gBACX,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,EAAU,EAGxB;QACD,IAAI;YACF,MAAM,cAAc,MAAM,IAAI,CAAC,SAAS,CAAC;YAEzC,IAAI,CAAC,YAAY,SAAS,EAAE;gBAC1B,OAAO;oBACL,SAAS;oBACT,SAAS,YAAY,MAAM,IAAI;gBACjC;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,CAAC;YAElC,IAAI,SAAS;gBACX,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,UAAU,CAAC;YAEnC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAGrB;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,kBAAkB,QAAQ,CAAC;YAEjC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBAAoB,eAAuB,GAAG,EAIjD;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,aAAa,IAAI;YACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK;YAE1C,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,eAAe,WAAW;YAEtD,6CAA6C;YAC7C,MAAM,mBAAmB,cAAc,MAAM,CAAC,CAAA,OAC5C,IAAI,KAAK,KAAK,SAAS,IAAI;YAG7B,IAAI,iBAAiB,MAAM,KAAK,GAAG;gBACjC,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,6BAA6B,EAAE,aAAa,WAAW,CAAC;oBAClE,cAAc;gBAChB;YACF;YAEA,0CAA0C;YAC1C,MAAM,iBAA2B,EAAE;YACnC,KAAK,MAAM,QAAQ,iBAAkB;gBACnC,MAAM,YAAY,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;gBAC9C,IAAI,UAAU,SAAS,EAAE;oBACvB,eAAe,IAAI,CAAC,KAAK,EAAE;gBAC7B;YACF;YAEA,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,eAAe,MAAM;gBAC/C,SAAS,CAAC,QAAQ,EAAE,aAAa,eAAe,CAAC;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,mBAIH;QACD,IAAI;YACF,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,aAAa,MAAM,eAAe,SAAS,CAAC;YAElD,IAAI,WAAW,MAAM,KAAK,GAAG;gBAC3B,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,UAAU,WAAW,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;YAC9C,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,QAAQ,MAAM;YAE5D,OAAO;gBACL,SAAS,iBAAiB,WAAW,MAAM;gBAC3C,SAAS,CAAC,QAAQ,EAAE,aAAa,YAAY,CAAC;gBAC9C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,cAAc;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,EAAU,EAGvB;QACD,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAE9B,+CAA+C;YAC/C,MAAM,iBAAiB;gBACrB,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;YAEA,MAAM,kBAAkB,MAAM,CAAC,IAAI;YAEnC,OAAO;gBACL,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,GAAa,EAI/B;QACD,IAAI,CAAC,UAAU,CAAC,SAAS,0BAA0B;YAAE,OAAO,IAAI,MAAM;QAAC;QAEvE,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,IAAI,GAAG,CAAC,CAAA,KAAM,IAAI,CAAC,UAAU,CAAC;QAGhC,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAA,SAChC,OAAO,MAAM,KAAK,eAAe,OAAO,KAAK,KAAK,MAClD,MAAM;QAER,MAAM,SAAS,QACZ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK,YACnC,GAAG,CAAC,CAAC,QAAQ,QACZ,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,GAAG,iBAAiB;QAG3F,OAAO;YACL,SAAS,eAAe,IAAI,MAAM;YAClC,kBAAkB;YAClB;QACF;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/users/index.ts"], "sourcesContent": ["// User Services\r\nexport { UserGetService, userGetService } from './get';\r\nexport { UserPostService, userPostService } from './post';\r\nexport { UserUpdateService, userUpdateService } from './update';\r\nexport { UserDeleteService, userDeleteService } from './delete';\r\n\r\n// Combined User Service\r\nimport { userGetService } from './get';\r\nimport { userPostService } from './post';\r\nimport { userUpdateService } from './update';\r\nimport { userDeleteService } from './delete';\r\n\r\nexport class UserService {\r\n  get = userGetService;\r\n  post = userPostService;\r\n  update = userUpdateService;\r\n  delete = userDeleteService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const userService = new UserService();\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,sIAAA,CAAA,iBAAc,CAAC;IACrB,OAAO,uIAAA,CAAA,kBAAe,CAAC;IACvB,SAAS,yIAAA,CAAA,oBAAiB,CAAC;IAC3B,SAAS,yIAAA,CAAA,oBAAiB,CAAC;AAC7B;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 4159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/cart/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Cart, CartSummary } from '../../types/entities';\r\n\r\nexport class CartGetService extends BaseService {\r\n  /**\r\n   * Get cart by user ID\r\n   */\r\n  async getByUserId(userId: number): Promise<Cart | null> {\r\n    const endpoint = ApiEndpoints.Cart.ByUserId(userId);\r\n    this.logApiCall('GET', endpoint);\r\n    try {\r\n      return this.handleResponse(\r\n        this.client.get<Cart>(endpoint)\r\n      );\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cart by session ID\r\n   */\r\n  async getBySessionId(sessionId: string): Promise<Cart | null> {\r\n    const endpoint = ApiEndpoints.Cart.BySessionId(sessionId);\r\n    this.logApiCall('GET', endpoint);\r\n    try {\r\n      return this.handleResponse(\r\n        this.client.get<Cart>(endpoint)\r\n      );\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cart summary by user ID\r\n   */\r\n  async getSummaryByUserId(userId: number): Promise<CartSummary | null> {\r\n    const endpoint = ApiEndpoints.Cart.SummaryByUserId(userId);\r\n    this.logApiCall('GET', endpoint);\r\n    try {\r\n      return this.handleResponse(\r\n        this.client.get<CartSummary>(endpoint)\r\n      );\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get cart summary by session ID\r\n   */\r\n  async getSummaryBySessionId(sessionId: string): Promise<CartSummary | null> {\r\n    const endpoint = ApiEndpoints.Cart.SummaryBySessionId(sessionId);\r\n    this.logApiCall('GET', endpoint);\r\n    try {\r\n      return this.handleResponse(\r\n        this.client.get<CartSummary>(endpoint)\r\n      );\r\n    } catch (error: any) {\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get or create cart\r\n   */\r\n  async getOrCreate(userId?: number, sessionId?: string): Promise<Cart> {\r\n    const params: Record<string, any> = {};\r\n    if (userId) params.userId = userId;\r\n    if (sessionId) params.sessionId = sessionId;\r\n\r\n    const endpoint = ApiEndpoints.Cart.GetOrCreate;\r\n    this.logApiCall('POST', endpoint, params);\r\n    return this.handleResponse(\r\n      this.client.post<Cart>(endpoint, null, { params })\r\n    );\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const cartGetService = new CartGetService();\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;;;AAGO,MAAM,uBAAuB,wIAAA,CAAA,cAAW;IAC7C;;GAEC,GACD,MAAM,YAAY,MAAc,EAAwB;QACtD,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,OAAO;QACvB,IAAI;YACF,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO;QAE1B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,eAAe,SAAiB,EAAwB;QAC5D,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO;QACvB,IAAI;YACF,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO;QAE1B,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAA+B;QACpE,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,eAAe,CAAC;QACnD,IAAI,CAAC,UAAU,CAAC,OAAO;QACvB,IAAI;YACF,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAc;QAEjC,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,SAAiB,EAA+B;QAC1E,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO;QACvB,IAAI;YACF,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAc;QAEjC,EAAE,OAAO,OAAY;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,OAAO;YACT;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,MAAe,EAAE,SAAkB,EAAiB;QACpE,MAAM,SAA8B,CAAC;QACrC,IAAI,QAAQ,OAAO,MAAM,GAAG;QAC5B,IAAI,WAAW,OAAO,SAAS,GAAG;QAElC,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;QAC9C,IAAI,CAAC,UAAU,CAAC,QAAQ,UAAU;QAClC,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAO,UAAU,MAAM;YAAE;QAAO;IAEpD;AACF;AAGO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 4244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/cart/post.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Cart, AddToCartRequest } from '../../types/entities';\r\n\r\nexport class CartPostService extends BaseService {\r\n  /**\r\n   * Add item to cart\r\n   */\r\n  async addToCart(request: AddToCartRequest): Promise<Cart> {\r\n    const endpoint = ApiEndpoints.Cart.Add;\r\n    this.logApiCall('POST', endpoint);\r\n    return this.handleResponse(\r\n      this.client.post<Cart>(endpoint, request)\r\n    );\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const cartPostService = new CartPostService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,wBAAwB,wIAAA,CAAA,cAAW;IAC9C;;GAEC,GACD,MAAM,UAAU,OAAyB,EAAiB;QACxD,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG;QACtC,IAAI,CAAC,UAAU,CAAC,QAAQ;QACxB,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAO,UAAU;IAErC;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 4268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/cart/update.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { Cart, UpdateCartItemRequest } from '../../types/entities';\r\n\r\nexport class CartUpdateService extends BaseService {\r\n  /**\r\n   * Update cart item quantity\r\n   */\r\n  async updateCartItem(cartId: number, productId: number, request: UpdateCartItemRequest): Promise<Cart> {\r\n    const endpoint = ApiEndpoints.Cart.UpdateItem(cartId, productId);\r\n    this.logApiCall('PUT', endpoint);\r\n    return this.handleResponse(\r\n      this.client.put<Cart>(endpoint, request)\r\n    );\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const cartUpdateService = new CartUpdateService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,eAAe,MAAc,EAAE,SAAiB,EAAE,OAA8B,EAAiB;QACrG,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO;QACvB,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAO,UAAU;IAEpC;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 4292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/cart/delete.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class CartDeleteService extends BaseService {\r\n  /**\r\n   * Remove item from cart\r\n   */\r\n  async removeFromCart(cartId: number, productId: number): Promise<boolean> {\r\n    const endpoint = ApiEndpoints.Cart.RemoveItem(cartId, productId);\r\n    this.logApiCall('DELETE', endpoint);\r\n    return this.handleResponse(\r\n      this.client.delete<boolean>(endpoint)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Remove cart item by ID\r\n   */\r\n  async removeCartItem(cartItemId: number): Promise<boolean> {\r\n    const endpoint = ApiEndpoints.Cart.RemoveCartItem(cartItemId);\r\n    this.logApiCall('DELETE', endpoint);\r\n    return this.handleResponse(\r\n      this.client.delete<boolean>(endpoint)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear entire cart\r\n   */\r\n  async clearCart(cartId: number): Promise<boolean> {\r\n    const endpoint = ApiEndpoints.Cart.Clear(cartId);\r\n    this.logApiCall('DELETE', endpoint);\r\n    return this.handleResponse(\r\n      this.client.delete<boolean>(endpoint)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear cart by user ID\r\n   */\r\n  async clearCartByUserId(userId: number): Promise<boolean> {\r\n    const endpoint = ApiEndpoints.Cart.ClearByUserId(userId);\r\n    this.logApiCall('DELETE', endpoint);\r\n    return this.handleResponse(\r\n      this.client.delete<boolean>(endpoint)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear cart by session ID\r\n   */\r\n  async clearCartBySessionId(sessionId: string): Promise<boolean> {\r\n    const endpoint = ApiEndpoints.Cart.ClearBySessionId(sessionId);\r\n    this.logApiCall('DELETE', endpoint);\r\n    return this.handleResponse(\r\n      this.client.delete<boolean>(endpoint)\r\n    );\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const cartDeleteService = new CartDeleteService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,0BAA0B,wIAAA,CAAA,cAAW;IAChD;;GAEC,GACD,MAAM,eAAe,MAAc,EAAE,SAAiB,EAAoB;QACxE,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ;QACtD,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAU;IAEhC;IAEA;;GAEC,GACD,MAAM,eAAe,UAAkB,EAAoB;QACzD,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,cAAc,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAU;IAEhC;IAEA;;GAEC,GACD,MAAM,UAAU,MAAc,EAAoB;QAChD,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAU;IAEhC;IAEA;;GAEC,GACD,MAAM,kBAAkB,MAAc,EAAoB;QACxD,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,aAAa,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAU;IAEhC;IAEA;;GAEC,GACD,MAAM,qBAAqB,SAAiB,EAAoB;QAC9D,MAAM,WAAW,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAU;IAEhC;AACF;AAGO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 4344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/cart/index.ts"], "sourcesContent": ["// Cart Services\r\nexport { CartGetService, cartGetService } from './get';\r\nexport { CartPostService, cartPostService } from './post';\r\nexport { CartUpdateService, cartUpdateService } from './update';\r\nexport { CartDeleteService, cartDeleteService } from './delete';\r\n\r\n// Combined Cart Service\r\nimport { cartGetService } from './get';\r\nimport { cartPostService } from './post';\r\nimport { cartUpdateService } from './update';\r\nimport { cartDeleteService } from './delete';\r\n\r\nexport class CartService {\r\n  get = cartGetService;\r\n  post = cartPostService;\r\n  update = cartUpdateService;\r\n  delete = cartDeleteService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const cartService = new CartService();\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAChB;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM;IACX,MAAM,qIAAA,CAAA,iBAAc,CAAC;IACrB,OAAO,sIAAA,CAAA,kBAAe,CAAC;IACvB,SAAS,wIAAA,CAAA,oBAAiB,CAAC;IAC3B,SAAS,wIAAA,CAAA,oBAAiB,CAAC;AAC7B;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 4386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/payments/types.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-empty-object-type */\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n// Payment-related TypeScript types for frontend\r\n\r\nexport interface PaymentRequest {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: 'stripe' | 'paypal' | 'apple_pay' | 'affirm';\r\n  returnUrl?: string;\r\n  cancelUrl?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface StripePaymentRequest {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethodType: 'card' | 'affirm' | 'apple_pay';\r\n  confirmationMethod: boolean;\r\n  returnUrl?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface PayPalOrderRequest {\r\n  amount: number;\r\n  currency: string;\r\n  intent: 'CAPTURE' | 'AUTHORIZE';\r\n  returnUrl?: string;\r\n  cancelUrl?: string;\r\n  purchaseUnit: PayPalPurchaseUnit;\r\n}\r\n\r\nexport interface PayPalPurchaseUnit {\r\n  referenceId?: string;\r\n  description?: string;\r\n  amount: PayPalAmount;\r\n}\r\n\r\nexport interface PayPalAmount {\r\n  currencyCode: string;\r\n  value: string;\r\n}\r\n\r\nexport interface PaymentConfirmationRequest {\r\n  paymentIntentId: string;\r\n  paymentMethod: string;\r\n  orderId: number;\r\n}\r\n\r\n// Response types\r\nexport interface PaymentResponse {\r\n  success: boolean;\r\n  clientSecret?: string;\r\n  paymentIntentId?: string;\r\n  paymentMethod?: string;\r\n  status?: string;\r\n  message?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface PayPalOrderResponse {\r\n  success: boolean;\r\n  orderId?: string;\r\n  status?: string;\r\n  approvalUrl?: string;\r\n  message?: string;\r\n  orderDetails?: PayPalOrderDetails;\r\n}\r\n\r\nexport interface PayPalOrderDetails {\r\n  id: string;\r\n  status: string;\r\n  intent: string;\r\n  links: PayPalLink[];\r\n}\r\n\r\nexport interface PayPalLink {\r\n  href: string;\r\n  rel: string;\r\n  method: string;\r\n}\r\n\r\nexport interface PaymentConfirmationResponse {\r\n  success: boolean;\r\n  transactionId?: string;\r\n  status?: string;\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  processedAt: string;\r\n  message?: string;\r\n}\r\n\r\nexport interface EmailNotificationResponse {\r\n  success: boolean;\r\n  message?: string;\r\n  sentAt: string;\r\n  recipientEmail?: string;\r\n}\r\n\r\nexport interface PaymentConfirmationWithNotificationResponse {\r\n  payment: PaymentConfirmationResponse;\r\n  emailNotification: EmailNotificationResponse;\r\n}\r\n\r\n// Stripe-specific types\r\nexport interface StripeElements {\r\n  create(type: string, options?: any): StripeElement;\r\n  getElement(type: string): StripeElement | null;\r\n}\r\n\r\nexport interface StripeElement {\r\n  mount(domElement: string | HTMLElement): void;\r\n  unmount(): void;\r\n  destroy(): void;\r\n  on(event: string, handler: (event: any) => void): void;\r\n  update(options: any): void;\r\n}\r\n\r\nexport interface StripeCardElement extends StripeElement {\r\n  // Card-specific methods\r\n}\r\n\r\nexport interface StripePaymentElement extends StripeElement {\r\n  // Payment Element specific methods\r\n}\r\n\r\n// PayPal-specific types\r\nexport interface PayPalButtonsComponentOptions {\r\n  createOrder: (data: any, actions: any) => Promise<string>;\r\n  onApprove: (data: any, actions: any) => Promise<void>;\r\n  onError?: (err: any) => void;\r\n  onCancel?: (data: any) => void;\r\n  style?: {\r\n    layout?: 'vertical' | 'horizontal';\r\n    color?: 'gold' | 'blue' | 'silver' | 'white' | 'black';\r\n    shape?: 'rect' | 'pill';\r\n    label?: 'paypal' | 'checkout' | 'buynow' | 'pay' | 'installment';\r\n    tagline?: boolean;\r\n    height?: number;\r\n  };\r\n}\r\n\r\n// Apple Pay types\r\nexport interface ApplePaySession {\r\n  begin(): void;\r\n  abort(): void;\r\n  completeMerchantValidation(merchantSession: any): void;\r\n  completePayment(result: ApplePayPaymentAuthorizationResult): void;\r\n}\r\n\r\nexport interface ApplePayPaymentRequest {\r\n  countryCode: string;\r\n  currencyCode: string;\r\n  supportedNetworks: string[];\r\n  merchantCapabilities: string[];\r\n  total: ApplePayLineItem;\r\n  lineItems?: ApplePayLineItem[];\r\n}\r\n\r\nexport interface ApplePayLineItem {\r\n  label: string;\r\n  amount: string;\r\n  type?: 'final' | 'pending';\r\n}\r\n\r\nexport interface ApplePayPaymentAuthorizationResult {\r\n  status: number;\r\n  errors?: ApplePayError[];\r\n}\r\n\r\nexport interface ApplePayError {\r\n  code: string;\r\n  message: string;\r\n  contactField?: string;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D,GAC1D,qDAAqD,GACrD,gDAAgD", "debugId": null}}, {"offset": {"line": 4395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/payments/stripe.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\n// Extend the Window interface to include ApplePaySession\r\ndeclare global {\r\n  interface Window {\r\n    ApplePaySession?: any;\r\n  }\r\n}\r\nimport { \r\n  StripePaymentRequest, \r\n  PaymentResponse, \r\n  PaymentConfirmationRequest,\r\n  PaymentConfirmationResponse \r\n} from './types';\r\n\r\nexport class StripePaymentService extends BaseService {\r\n  /**\r\n   * Create a Stripe payment intent with specific payment method\r\n   */\r\n  async createPaymentIntent(data: StripePaymentRequest): Promise<PaymentResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/stripe/create-intent`, data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PaymentResponse>(`${ApiEndpoints.Payments}/stripe/create-intent`, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create a legacy Stripe payment intent (backward compatibility)\r\n   */\r\n  async createLegacyIntent(amount: number, currency: string = 'usd'): Promise<{ clientSecret: string }> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/create-intent`, { amount, currency });\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<{ clientSecret: string }>(`${ApiEndpoints.Payments}/create-intent`, {\r\n        amount,\r\n        currency\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Confirm a Stripe payment\r\n   */\r\n  async confirmPayment(data: PaymentConfirmationRequest): Promise<PaymentConfirmationResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/stripe/confirm`, data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PaymentConfirmationResponse>(`${ApiEndpoints.Payments}/stripe/confirm`, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create Affirm payment intent\r\n   */\r\n  async createAffirmIntent(amount: number, currency: string = 'usd', metadata?: Record<string, any>): Promise<PaymentResponse> {\r\n    const data: StripePaymentRequest = {\r\n      amount,\r\n      currency,\r\n      paymentMethodType: 'affirm',\r\n      confirmationMethod: false,\r\n      metadata\r\n    };\r\n\r\n    return this.createPaymentIntent(data);\r\n  }\r\n\r\n  /**\r\n   * Create Apple Pay payment intent\r\n   */\r\n  async createApplePayIntent(amount: number, currency: string = 'usd', metadata?: Record<string, any>): Promise<PaymentResponse> {\r\n    const data: StripePaymentRequest = {\r\n      amount,\r\n      currency,\r\n      paymentMethodType: 'apple_pay',\r\n      confirmationMethod: false,\r\n      metadata\r\n    };\r\n\r\n    return this.createPaymentIntent(data);\r\n  }\r\n\r\n  /**\r\n   * Create Apple Pay session\r\n   */\r\n  async createApplePaySession(domainName: string): Promise<PaymentResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/apple-pay/session`, domainName);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PaymentResponse>(`${ApiEndpoints.Payments}/apple-pay/session`, domainName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Validate Apple Pay merchant\r\n   */\r\n  async validateApplePayMerchant(validationURL: string, domainName: string): Promise<any> {\r\n    // This would typically be handled by your backend\r\n    // For now, we'll create a session through our API\r\n    return this.createApplePaySession(domainName);\r\n  }\r\n\r\n  /**\r\n   * Process Apple Pay payment\r\n   */\r\n  async processApplePayPayment(\r\n    paymentData: any,\r\n    amount: number,\r\n    currency: string = 'usd'\r\n  ): Promise<PaymentConfirmationResponse> {\r\n    // Create payment intent for Apple Pay\r\n    const intentResponse = await this.createApplePayIntent(amount, currency, {\r\n      apple_pay_payment: paymentData\r\n    });\r\n\r\n    if (!intentResponse.success || !intentResponse.paymentIntentId) {\r\n      throw new Error(intentResponse.message || 'Failed to create Apple Pay payment intent');\r\n    }\r\n\r\n    // Confirm the payment\r\n    return this.confirmPayment({\r\n      paymentIntentId: intentResponse.paymentIntentId,\r\n      paymentMethod: 'apple_pay',\r\n      orderId: 0 // This should be set by the calling code\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Process Affirm payment\r\n   */\r\n  async processAffirmPayment(\r\n    amount: number,\r\n    currency: string = 'usd',\r\n    orderId: number,\r\n    metadata?: Record<string, any>\r\n  ): Promise<PaymentResponse> {\r\n    return this.createAffirmIntent(amount, currency, {\r\n      ...metadata,\r\n      order_id: orderId\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handle Stripe webhook events (if needed on frontend)\r\n   */\r\n  async handleWebhookEvent(event: any): Promise<void> {\r\n    // Log the webhook event for debugging\r\n    console.log('Stripe webhook event received:', event);\r\n    \r\n    // Handle different event types\r\n    switch (event.type) {\r\n      case 'payment_intent.succeeded':\r\n        console.log('Payment succeeded:', event.data.object);\r\n        break;\r\n      case 'payment_intent.payment_failed':\r\n        console.log('Payment failed:', event.data.object);\r\n        break;\r\n      default:\r\n        console.log('Unhandled event type:', event.type);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get payment method capabilities\r\n   */\r\n  getPaymentMethodCapabilities(): {\r\n    card: boolean;\r\n    affirm: boolean;\r\n    applePay: boolean;\r\n  } {\r\n    return {\r\n      card: true,\r\n      affirm: true,\r\n      applePay: this.isApplePayAvailable()\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Check if Apple Pay is available\r\n   */\r\n  private isApplePayAvailable(): boolean {\r\n    if (typeof window === 'undefined') return false;\r\n    \r\n    return (\r\n      window.ApplePaySession &&\r\n      window.ApplePaySession.canMakePayments &&\r\n      window.ApplePaySession.canMakePayments()\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Format amount for Stripe (convert to cents)\r\n   */\r\n  formatAmountForStripe(amount: number): number {\r\n    return Math.round(amount * 100);\r\n  }\r\n\r\n  /**\r\n   * Format amount from Stripe (convert from cents)\r\n   */\r\n  formatAmountFromStripe(amount: number): number {\r\n    return amount / 100;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const stripePaymentService = new StripePaymentService();\r\nexport default stripePaymentService;\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;AACA;;;AAeO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,oBAAoB,IAA0B,EAA4B;QAC9E,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;QAEzE,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAkB,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE;IAEvF;IAEA;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAAE,WAAmB,KAAK,EAAqC;QACpG,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YAAE;YAAQ;QAAS;QAErF,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAA2B,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACnF;YACA;QACF;IAEJ;IAEA;;GAEC,GACD,MAAM,eAAe,IAAgC,EAAwC;QAC3F,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;QAEnE,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAA8B,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;IAE7F;IAEA;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAAE,WAAmB,KAAK,EAAE,QAA8B,EAA4B;QAC3H,MAAM,OAA6B;YACjC;YACA;YACA,mBAAmB;YACnB,oBAAoB;YACpB;QACF;QAEA,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC;IAEA;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAAE,WAAmB,KAAK,EAAE,QAA8B,EAA4B;QAC7H,MAAM,OAA6B;YACjC;YACA;YACA,mBAAmB;YACnB,oBAAoB;YACpB;QACF;QAEA,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC;IAEA;;GAEC,GACD,MAAM,sBAAsB,UAAkB,EAA4B;QACxE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAEtE,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAkB,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;IAEpF;IAEA;;GAEC,GACD,MAAM,yBAAyB,aAAqB,EAAE,UAAkB,EAAgB;QACtF,kDAAkD;QAClD,kDAAkD;QAClD,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC;IAEA;;GAEC,GACD,MAAM,uBACJ,WAAgB,EAChB,MAAc,EACd,WAAmB,KAAK,EACc;QACtC,sCAAsC;QACtC,MAAM,iBAAiB,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,UAAU;YACvE,mBAAmB;QACrB;QAEA,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,eAAe,eAAe,EAAE;YAC9D,MAAM,IAAI,MAAM,eAAe,OAAO,IAAI;QAC5C;QAEA,sBAAsB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;YACzB,iBAAiB,eAAe,eAAe;YAC/C,eAAe;YACf,SAAS,EAAE,yCAAyC;QACtD;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,MAAc,EACd,WAAmB,KAAK,EACxB,OAAe,EACf,QAA8B,EACJ;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YAC/C,GAAG,QAAQ;YACX,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,MAAM,mBAAmB,KAAU,EAAiB;QAClD,sCAAsC;QACtC,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,+BAA+B;QAC/B,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,QAAQ,GAAG,CAAC,sBAAsB,MAAM,IAAI,CAAC,MAAM;gBACnD;YACF,KAAK;gBACH,QAAQ,GAAG,CAAC,mBAAmB,MAAM,IAAI,CAAC,MAAM;gBAChD;YACF;gBACE,QAAQ,GAAG,CAAC,yBAAyB,MAAM,IAAI;QACnD;IACF;IAEA;;GAEC,GACD,+BAIE;QACA,OAAO;YACL,MAAM;YACN,QAAQ;YACR,UAAU,IAAI,CAAC,mBAAmB;QACpC;IACF;IAEA;;GAEC,GACD,AAAQ,sBAA+B;QACrC,wCAAmC,OAAO;;IAO5C;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAU;QAC5C,OAAO,KAAK,KAAK,CAAC,SAAS;IAC7B;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAU;QAC7C,OAAO,SAAS;IAClB;AACF;AAGO,MAAM,uBAAuB,IAAI;uCACzB", "debugId": null}}, {"offset": {"line": 4542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/payments/paypal.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { \r\n  PayPalOrderRequest, \r\n  PayPalOrderResponse, \r\n  PaymentConfirmationResponse,\r\n  PayPalButtonsComponentOptions \r\n} from './types';\r\n\r\nexport class PayPalPaymentService extends BaseService {\r\n  /**\r\n   * Create a PayPal order\r\n   */\r\n  async createOrder(data: PayPalOrderRequest): Promise<PayPalOrderResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/paypal/create-order`, data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PayPalOrderResponse>(`${ApiEndpoints.Payments}/paypal/create-order`, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Capture a PayPal order\r\n   */\r\n  async captureOrder(orderId: string): Promise<PaymentConfirmationResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/paypal/capture/${orderId}`, {});\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PaymentConfirmationResponse>(`${ApiEndpoints.Payments}/paypal/capture/${orderId}`, {})\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get PayPal order details\r\n   */\r\n  async getOrder(orderId: string): Promise<PayPalOrderResponse> {\r\n    this.logApiCall('GET', `${ApiEndpoints.Payments}/paypal/order/${orderId}`, {});\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<PayPalOrderResponse>(`${ApiEndpoints.Payments}/paypal/order/${orderId}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Create PayPal order for checkout\r\n   */\r\n  async createCheckoutOrder(\r\n    amount: number, \r\n    currency: string = 'USD',\r\n    description?: string,\r\n    returnUrl?: string,\r\n    cancelUrl?: string\r\n  ): Promise<PayPalOrderResponse> {\r\n    const orderData: PayPalOrderRequest = {\r\n      amount,\r\n      currency,\r\n      intent: 'CAPTURE',\r\n      returnUrl,\r\n      cancelUrl,\r\n      purchaseUnit: {\r\n        description: description || 'Cast Stone Purchase',\r\n        amount: {\r\n          currencyCode: currency,\r\n          value: amount.toFixed(2)\r\n        }\r\n      }\r\n    };\r\n\r\n    return this.createOrder(orderData);\r\n  }\r\n\r\n  /**\r\n   * Generate PayPal buttons configuration\r\n   */\r\n  generateButtonsConfig(\r\n    amount: number,\r\n    currency: string = 'USD',\r\n    onSuccess?: (data: any) => void,\r\n    onError?: (error: any) => void,\r\n    onCancel?: (data: any) => void,\r\n    description?: string\r\n  ): PayPalButtonsComponentOptions {\r\n    return {\r\n      createOrder: async (data, actions) => {\r\n        try {\r\n          const orderResponse = await this.createCheckoutOrder(\r\n            amount,\r\n            currency,\r\n            description\r\n          );\r\n\r\n          if (!orderResponse.success || !orderResponse.orderId) {\r\n            throw new Error(orderResponse.message || 'Failed to create PayPal order');\r\n          }\r\n\r\n          return orderResponse.orderId;\r\n        } catch (error) {\r\n          console.error('Error creating PayPal order:', error);\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      onApprove: async (data, actions) => {\r\n        try {\r\n          const captureResponse = await this.captureOrder(data.orderID);\r\n          \r\n          if (captureResponse.success) {\r\n            console.log('PayPal payment captured successfully:', captureResponse);\r\n            onSuccess?.(captureResponse);\r\n          } else {\r\n            throw new Error(captureResponse.message || 'Failed to capture PayPal payment');\r\n          }\r\n        } catch (error) {\r\n          console.error('Error capturing PayPal payment:', error);\r\n          onError?.(error);\r\n        }\r\n      },\r\n\r\n      onError: (err) => {\r\n        console.error('PayPal error:', err);\r\n        onError?.(err);\r\n      },\r\n\r\n      onCancel: (data) => {\r\n        console.log('PayPal payment cancelled:', data);\r\n        onCancel?.(data);\r\n      },\r\n\r\n      style: {\r\n        layout: 'vertical',\r\n        color: 'gold',\r\n        shape: 'rect',\r\n        label: 'paypal',\r\n        tagline: false,\r\n        height: 40\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Load PayPal SDK script\r\n   */\r\n  async loadPayPalSDK(clientId: string, currency: string = 'USD'): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      // Check if PayPal SDK is already loaded\r\n      if (window.paypal) {\r\n        resolve();\r\n        return;\r\n      }\r\n\r\n      // Create script element\r\n      const script = document.createElement('script');\r\n      script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=${currency}&intent=capture`;\r\n      script.async = true;\r\n\r\n      script.onload = () => {\r\n        if (window.paypal) {\r\n          resolve();\r\n        } else {\r\n          reject(new Error('PayPal SDK failed to load'));\r\n        }\r\n      };\r\n\r\n      script.onerror = () => {\r\n        reject(new Error('Failed to load PayPal SDK script'));\r\n      };\r\n\r\n      document.head.appendChild(script);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Render PayPal buttons\r\n   */\r\n  async renderPayPalButtons(\r\n    containerId: string,\r\n    config: PayPalButtonsComponentOptions\r\n  ): Promise<void> {\r\n    if (!window.paypal) {\r\n      throw new Error('PayPal SDK not loaded');\r\n    }\r\n\r\n    const container = document.getElementById(containerId);\r\n    if (!container) {\r\n      throw new Error(`Container with ID '${containerId}' not found`);\r\n    }\r\n\r\n    // Clear existing content\r\n    container.innerHTML = '';\r\n\r\n    // Render PayPal buttons\r\n    window.paypal.Buttons(config).render(`#${containerId}`);\r\n  }\r\n\r\n  /**\r\n   * Validate PayPal order amount\r\n   */\r\n  validateOrderAmount(amount: number, currency: string = 'USD'): boolean {\r\n    if (amount <= 0) {\r\n      return false;\r\n    }\r\n\r\n    // PayPal minimum amounts by currency\r\n    const minimumAmounts: Record<string, number> = {\r\n      'USD': 0.01,\r\n      'EUR': 0.01,\r\n      'GBP': 0.01,\r\n      'CAD': 0.01,\r\n      'AUD': 0.01,\r\n      'JPY': 1\r\n    };\r\n\r\n    const minimum = minimumAmounts[currency] || 0.01;\r\n    return amount >= minimum;\r\n  }\r\n\r\n  /**\r\n   * Format amount for PayPal\r\n   */\r\n  formatAmountForPayPal(amount: number): string {\r\n    return amount.toFixed(2);\r\n  }\r\n\r\n  /**\r\n   * Get PayPal supported currencies\r\n   */\r\n  getSupportedCurrencies(): string[] {\r\n    return [\r\n      'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', \r\n      'CHF', 'NOK', 'SEK', 'DKK', 'PLN', 'CZK',\r\n      'HUF', 'ILS', 'MXN', 'BRL', 'MYR', 'PHP',\r\n      'THB', 'TWD', 'NZD', 'HKD', 'SGD', 'RUB'\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Check if currency is supported\r\n   */\r\n  isCurrencySupported(currency: string): boolean {\r\n    return this.getSupportedCurrencies().includes(currency.toUpperCase());\r\n  }\r\n}\r\n\r\n// Extend window interface for PayPal\r\ndeclare global {\r\n  interface Window {\r\n    paypal?: any;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const paypalPaymentService = new PayPalPaymentService();\r\nexport default paypalPaymentService;\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,qDAAqD;;;;;AACrD;AACA;;;AAQO,MAAM,6BAA6B,wIAAA,CAAA,cAAW;IACnD;;GAEC,GACD,MAAM,YAAY,IAAwB,EAAgC;QACxE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QAExE,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAsB,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE;IAE1F;IAEA;;GAEC,GACD,MAAM,aAAa,OAAe,EAAwC;QACxE,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;QAE/E,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAA8B,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC;IAEzG;IAEA;;GAEC,GACD,MAAM,SAAS,OAAe,EAAgC;QAC5D,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC;QAE5E,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAsB,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,cAAc,EAAE,SAAS;IAE3F;IAEA;;GAEC,GACD,MAAM,oBACJ,MAAc,EACd,WAAmB,KAAK,EACxB,WAAoB,EACpB,SAAkB,EAClB,SAAkB,EACY;QAC9B,MAAM,YAAgC;YACpC;YACA;YACA,QAAQ;YACR;YACA;YACA,cAAc;gBACZ,aAAa,eAAe;gBAC5B,QAAQ;oBACN,cAAc;oBACd,OAAO,OAAO,OAAO,CAAC;gBACxB;YACF;QACF;QAEA,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA;;GAEC,GACD,sBACE,MAAc,EACd,WAAmB,KAAK,EACxB,SAA+B,EAC/B,OAA8B,EAC9B,QAA8B,EAC9B,WAAoB,EACW;QAC/B,OAAO;YACL,aAAa,OAAO,MAAM;gBACxB,IAAI;oBACF,MAAM,gBAAgB,MAAM,IAAI,CAAC,mBAAmB,CAClD,QACA,UACA;oBAGF,IAAI,CAAC,cAAc,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;wBACpD,MAAM,IAAI,MAAM,cAAc,OAAO,IAAI;oBAC3C;oBAEA,OAAO,cAAc,OAAO;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,MAAM;gBACR;YACF;YAEA,WAAW,OAAO,MAAM;gBACtB,IAAI;oBACF,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,OAAO;oBAE5D,IAAI,gBAAgB,OAAO,EAAE;wBAC3B,QAAQ,GAAG,CAAC,yCAAyC;wBACrD,YAAY;oBACd,OAAO;wBACL,MAAM,IAAI,MAAM,gBAAgB,OAAO,IAAI;oBAC7C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,UAAU;gBACZ;YACF;YAEA,SAAS,CAAC;gBACR,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,UAAU;YACZ;YAEA,UAAU,CAAC;gBACT,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,WAAW;YACb;YAEA,OAAO;gBACL,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,QAAQ;YACV;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,QAAgB,EAAE,WAAmB,KAAK,EAAiB;QAC7E,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,wCAAwC;YACxC,IAAI,OAAO,MAAM,EAAE;gBACjB;gBACA;YACF;YAEA,wBAAwB;YACxB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,GAAG,GAAG,CAAC,wCAAwC,EAAE,SAAS,UAAU,EAAE,SAAS,eAAe,CAAC;YACtG,OAAO,KAAK,GAAG;YAEf,OAAO,MAAM,GAAG;gBACd,IAAI,OAAO,MAAM,EAAE;oBACjB;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,MAAM;YACnB;YAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,WAAmB,EACnB,MAAqC,EACtB;QACf,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,YAAY,WAAW,CAAC;QAChE;QAEA,yBAAyB;QACzB,UAAU,SAAS,GAAG;QAEtB,wBAAwB;QACxB,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa;IACxD;IAEA;;GAEC,GACD,oBAAoB,MAAc,EAAE,WAAmB,KAAK,EAAW;QACrE,IAAI,UAAU,GAAG;YACf,OAAO;QACT;QAEA,qCAAqC;QACrC,MAAM,iBAAyC;YAC7C,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QAEA,MAAM,UAAU,cAAc,CAAC,SAAS,IAAI;QAC5C,OAAO,UAAU;IACnB;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAU;QAC5C,OAAO,OAAO,OAAO,CAAC;IACxB;IAEA;;GAEC,GACD,yBAAmC;QACjC,OAAO;YACL;YAAO;YAAO;YAAO;YAAO;YAAO;YACnC;YAAO;YAAO;YAAO;YAAO;YAAO;YACnC;YAAO;YAAO;YAAO;YAAO;YAAO;YACnC;YAAO;YAAO;YAAO;YAAO;YAAO;SACpC;IACH;IAEA;;GAEC,GACD,oBAAoB,QAAgB,EAAW;QAC7C,OAAO,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,SAAS,WAAW;IACpE;AACF;AAUO,MAAM,uBAAuB,IAAI;uCACzB", "debugId": null}}, {"offset": {"line": 4745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/payments/unified.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { stripePaymentService } from './stripe';\r\nimport { paypalPaymentService } from './paypal';\r\nimport { \r\n  PaymentConfirmationRequest,\r\n  PaymentConfirmationWithNotificationResponse,\r\n  PaymentResponse,\r\n  PaymentConfirmationResponse\r\n} from './types';\r\n\r\nexport class UnifiedPaymentService extends BaseService {\r\n  private stripe = stripePaymentService;\r\n  private paypal = paypalPaymentService;\r\n\r\n  /**\r\n   * Confirm payment and send email notification\r\n   */\r\n  async confirmPaymentAndNotify(data: PaymentConfirmationRequest): Promise<PaymentConfirmationWithNotificationResponse> {\r\n    this.logApiCall('POST', `${ApiEndpoints.Payments}/confirm-and-notify`, data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<PaymentConfirmationWithNotificationResponse>(`${ApiEndpoints.Payments}/confirm-and-notify`, data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Process payment based on method type\r\n   */\r\n  async processPayment(\r\n    paymentMethod: 'stripe' | 'paypal' | 'apple_pay' | 'affirm',\r\n    amount: number,\r\n    currency: string = 'USD',\r\n    orderId: number,\r\n    additionalData?: any\r\n  ): Promise<PaymentResponse | PaymentConfirmationResponse> {\r\n    switch (paymentMethod) {\r\n      case 'stripe':\r\n        return this.stripe.createPaymentIntent({\r\n          amount: this.stripe.formatAmountForStripe(amount),\r\n          currency: currency.toLowerCase(),\r\n          paymentMethodType: 'card',\r\n          confirmationMethod: false,\r\n          metadata: { order_id: orderId, ...additionalData }\r\n        });\r\n\r\n      case 'affirm':\r\n        return this.stripe.createAffirmIntent(\r\n          this.stripe.formatAmountForStripe(amount),\r\n          currency.toLowerCase(),\r\n          { order_id: orderId, ...additionalData }\r\n        );\r\n\r\n      case 'apple_pay':\r\n        return this.stripe.createApplePayIntent(\r\n          this.stripe.formatAmountForStripe(amount),\r\n          currency.toLowerCase(),\r\n          { order_id: orderId, ...additionalData }\r\n        );\r\n\r\n      case 'paypal':\r\n        return this.paypal.createCheckoutOrder(\r\n          amount,\r\n          currency.toUpperCase(),\r\n          additionalData?.description || 'Cast Stone Purchase',\r\n          additionalData?.returnUrl,\r\n          additionalData?.cancelUrl\r\n        );\r\n\r\n      default:\r\n        throw new Error(`Unsupported payment method: ${paymentMethod}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Complete payment process with email notification\r\n   */\r\n  async completePayment(\r\n    paymentMethod: 'stripe' | 'paypal' | 'apple_pay' | 'affirm',\r\n    paymentIntentId: string,\r\n    orderId: number\r\n  ): Promise<PaymentConfirmationWithNotificationResponse> {\r\n    const confirmationData: PaymentConfirmationRequest = {\r\n      paymentIntentId,\r\n      paymentMethod,\r\n      orderId\r\n    };\r\n\r\n    return this.confirmPaymentAndNotify(confirmationData);\r\n  }\r\n\r\n  /**\r\n   * Get available payment methods\r\n   */\r\n  getAvailablePaymentMethods(): {\r\n    method: string;\r\n    name: string;\r\n    description: string;\r\n    available: boolean;\r\n    icon: string;\r\n  }[] {\r\n    const stripeCapabilities = this.stripe.getPaymentMethodCapabilities();\r\n\r\n    return [\r\n      {\r\n        method: 'stripe',\r\n        name: 'Credit Card',\r\n        description: 'Visa, Mastercard, American Express',\r\n        available: stripeCapabilities.card,\r\n        icon: '💳'\r\n      },\r\n      {\r\n        method: 'paypal',\r\n        name: 'PayPal',\r\n        description: 'Pay securely with your PayPal account',\r\n        available: true,\r\n        icon: '🅿️'\r\n      },\r\n      {\r\n        method: 'affirm',\r\n        name: 'Affirm',\r\n        description: 'Buy now, pay later with Affirm',\r\n        available: stripeCapabilities.affirm,\r\n        icon: '📅'\r\n      },\r\n      {\r\n        method: 'apple_pay',\r\n        name: 'Apple Pay',\r\n        description: 'Pay with Touch ID or Face ID',\r\n        available: stripeCapabilities.applePay,\r\n        icon: '🍎'\r\n      }\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Validate payment amount for all methods\r\n   */\r\n  validatePaymentAmount(amount: number, currency: string = 'USD'): {\r\n    valid: boolean;\r\n    errors: string[];\r\n  } {\r\n    const errors: string[] = [];\r\n\r\n    if (amount <= 0) {\r\n      errors.push('Amount must be greater than zero');\r\n    }\r\n\r\n    // Check PayPal minimum\r\n    if (!this.paypal.validateOrderAmount(amount, currency)) {\r\n      errors.push(`Amount is below minimum for ${currency}`);\r\n    }\r\n\r\n    // Check Stripe minimum (usually $0.50 USD)\r\n    const stripeMinimum = currency.toLowerCase() === 'usd' ? 0.50 : 0.50;\r\n    if (amount < stripeMinimum) {\r\n      errors.push(`Amount is below Stripe minimum of ${stripeMinimum} ${currency}`);\r\n    }\r\n\r\n    return {\r\n      valid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Format currency for display\r\n   */\r\n  formatCurrency(amount: number, currency: string = 'USD'): string {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: currency.toUpperCase()\r\n    }).format(amount);\r\n  }\r\n\r\n  /**\r\n   * Get payment method configuration\r\n   */\r\n  getPaymentMethodConfig(method: string): any {\r\n    switch (method) {\r\n      case 'stripe':\r\n      case 'affirm':\r\n      case 'apple_pay':\r\n        return {\r\n          publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,\r\n          appearance: {\r\n            theme: 'stripe',\r\n            variables: {\r\n              colorPrimary: '#1e3a8a', // Navy blue theme\r\n              colorBackground: '#ffffff',\r\n              colorText: '#1f2937',\r\n              colorDanger: '#dc2626',\r\n              fontFamily: 'system-ui, sans-serif',\r\n              spacingUnit: '4px',\r\n              borderRadius: '6px'\r\n            }\r\n          }\r\n        };\r\n\r\n      case 'paypal':\r\n        return {\r\n          clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,\r\n          currency: 'USD',\r\n          intent: 'capture',\r\n          style: {\r\n            layout: 'vertical',\r\n            color: 'gold',\r\n            shape: 'rect',\r\n            label: 'paypal',\r\n            tagline: false,\r\n            height: 40\r\n          }\r\n        };\r\n\r\n      default:\r\n        return {};\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle payment errors\r\n   */\r\n  handlePaymentError(error: any, paymentMethod: string): {\r\n    userMessage: string;\r\n    technicalMessage: string;\r\n    shouldRetry: boolean;\r\n  } {\r\n    console.error(`Payment error (${paymentMethod}):`, error);\r\n\r\n    // Common error patterns\r\n    if (error.message?.includes('insufficient_funds')) {\r\n      return {\r\n        userMessage: 'Your payment method has insufficient funds. Please try a different payment method.',\r\n        technicalMessage: error.message,\r\n        shouldRetry: true\r\n      };\r\n    }\r\n\r\n    if (error.message?.includes('card_declined')) {\r\n      return {\r\n        userMessage: 'Your card was declined. Please check your card details or try a different payment method.',\r\n        technicalMessage: error.message,\r\n        shouldRetry: true\r\n      };\r\n    }\r\n\r\n    if (error.message?.includes('network')) {\r\n      return {\r\n        userMessage: 'Network error. Please check your connection and try again.',\r\n        technicalMessage: error.message,\r\n        shouldRetry: true\r\n      };\r\n    }\r\n\r\n    // Default error handling\r\n    return {\r\n      userMessage: 'An error occurred while processing your payment. Please try again.',\r\n      technicalMessage: error.message || 'Unknown payment error',\r\n      shouldRetry: true\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get Stripe service instance\r\n   */\r\n  get stripeService() {\r\n    return this.stripe;\r\n  }\r\n\r\n  /**\r\n   * Get PayPal service instance\r\n   */\r\n  get paypalService() {\r\n    return this.paypal;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const unifiedPaymentService = new UnifiedPaymentService();\r\nexport default unifiedPaymentService;\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;AACA;AACA;AACA;;;;;AAQO,MAAM,8BAA8B,wIAAA,CAAA,cAAW;IAC5C,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAC9B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAEtC;;GAEC,GACD,MAAM,wBAAwB,IAAgC,EAAwD;QACpH,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QAEvE,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAA8C,GAAG,sIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;IAEjH;IAEA;;GAEC,GACD,MAAM,eACJ,aAA2D,EAC3D,MAAc,EACd,WAAmB,KAAK,EACxB,OAAe,EACf,cAAoB,EACoC;QACxD,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;oBACrC,QAAQ,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;oBAC1C,UAAU,SAAS,WAAW;oBAC9B,mBAAmB;oBACnB,oBAAoB;oBACpB,UAAU;wBAAE,UAAU;wBAAS,GAAG,cAAc;oBAAC;gBACnD;YAEF,KAAK;gBACH,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAClC,SAAS,WAAW,IACpB;oBAAE,UAAU;oBAAS,GAAG,cAAc;gBAAC;YAG3C,KAAK;gBACH,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAClC,SAAS,WAAW,IACpB;oBAAE,UAAU;oBAAS,GAAG,cAAc;gBAAC;YAG3C,KAAK;gBACH,OAAO,IAAI,CAAC,MAAM,CAAC,mBAAmB,CACpC,QACA,SAAS,WAAW,IACpB,gBAAgB,eAAe,uBAC/B,gBAAgB,WAChB,gBAAgB;YAGpB;gBACE,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,eAAe;QAClE;IACF;IAEA;;GAEC,GACD,MAAM,gBACJ,aAA2D,EAC3D,eAAuB,EACvB,OAAe,EACuC;QACtD,MAAM,mBAA+C;YACnD;YACA;YACA;QACF;QAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC;IAEA;;GAEC,GACD,6BAMI;QACF,MAAM,qBAAqB,IAAI,CAAC,MAAM,CAAC,4BAA4B;QAEnE,OAAO;YACL;gBACE,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,WAAW,mBAAmB,IAAI;gBAClC,MAAM;YACR;YACA;gBACE,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,WAAW;gBACX,MAAM;YACR;YACA;gBACE,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,WAAW,mBAAmB,MAAM;gBACpC,MAAM;YACR;YACA;gBACE,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,WAAW,mBAAmB,QAAQ;gBACtC,MAAM;YACR;SACD;IACH;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAE,WAAmB,KAAK,EAG5D;QACA,MAAM,SAAmB,EAAE;QAE3B,IAAI,UAAU,GAAG;YACf,OAAO,IAAI,CAAC;QACd;QAEA,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,WAAW;YACtD,OAAO,IAAI,CAAC,CAAC,4BAA4B,EAAE,UAAU;QACvD;QAEA,2CAA2C;QAC3C,MAAM,gBAAgB,SAAS,WAAW,OAAO,QAAQ,OAAO;QAChE,IAAI,SAAS,eAAe;YAC1B,OAAO,IAAI,CAAC,CAAC,kCAAkC,EAAE,cAAc,CAAC,EAAE,UAAU;QAC9E;QAEA,OAAO;YACL,OAAO,OAAO,MAAM,KAAK;YACzB;QACF;IACF;IAEA;;GAEC,GACD,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU,SAAS,WAAW;QAChC,GAAG,MAAM,CAAC;IACZ;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAO;QAC1C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBACL,gBAAgB,QAAQ,GAAG,CAAC,kCAAkC;oBAC9D,YAAY;wBACV,OAAO;wBACP,WAAW;4BACT,cAAc;4BACd,iBAAiB;4BACjB,WAAW;4BACX,aAAa;4BACb,YAAY;4BACZ,aAAa;4BACb,cAAc;wBAChB;oBACF;gBACF;YAEF,KAAK;gBACH,OAAO;oBACL,UAAU,QAAQ,GAAG,CAAC,4BAA4B;oBAClD,UAAU;oBACV,QAAQ;oBACR,OAAO;wBACL,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,OAAO;wBACP,SAAS;wBACT,QAAQ;oBACV;gBACF;YAEF;gBACE,OAAO,CAAC;QACZ;IACF;IAEA;;GAEC,GACD,mBAAmB,KAAU,EAAE,aAAqB,EAIlD;QACA,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,cAAc,EAAE,CAAC,EAAE;QAEnD,wBAAwB;QACxB,IAAI,MAAM,OAAO,EAAE,SAAS,uBAAuB;YACjD,OAAO;gBACL,aAAa;gBACb,kBAAkB,MAAM,OAAO;gBAC/B,aAAa;YACf;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,kBAAkB;YAC5C,OAAO;gBACL,aAAa;gBACb,kBAAkB,MAAM,OAAO;gBAC/B,aAAa;YACf;QACF;QAEA,IAAI,MAAM,OAAO,EAAE,SAAS,YAAY;YACtC,OAAO;gBACL,aAAa;gBACb,kBAAkB,MAAM,OAAO;gBAC/B,aAAa;YACf;QACF;QAEA,yBAAyB;QACzB,OAAO;YACL,aAAa;YACb,kBAAkB,MAAM,OAAO,IAAI;YACnC,aAAa;QACf;IACF;IAEA;;GAEC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA;;GAEC,GACD,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAGO,MAAM,wBAAwB,IAAI;uCAC1B", "debugId": null}}, {"offset": {"line": 4964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/payments/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n// Payment Services Export\r\nexport * from './types';\r\nexport * from './stripe';\r\nexport * from './paypal';\r\nexport * from './unified';\r\n\r\n// Import services\r\nimport { stripePaymentService } from './stripe';\r\nimport { paypalPaymentService } from './paypal';\r\nimport { unifiedPaymentService } from './unified';\r\n\r\n// Combined payment service class\r\nexport class PaymentService {\r\n  stripe = stripePaymentService;\r\n  paypal = paypalPaymentService;\r\n  unified = unifiedPaymentService;\r\n\r\n  // Convenience methods that delegate to unified service\r\n  async processPayment(\r\n    paymentMethod: 'stripe' | 'paypal' | 'apple_pay' | 'affirm',\r\n    amount: number,\r\n    currency: string = 'USD',\r\n    orderId: number,\r\n    additionalData?: any\r\n  ) {\r\n    return this.unified.processPayment(paymentMethod, amount, currency, orderId, additionalData);\r\n  }\r\n\r\n  async completePayment(\r\n    paymentMethod: 'stripe' | 'paypal' | 'apple_pay' | 'affirm',\r\n    paymentIntentId: string,\r\n    orderId: number\r\n  ) {\r\n    return this.unified.completePayment(paymentMethod, paymentIntentId, orderId);\r\n  }\r\n\r\n  getAvailablePaymentMethods() {\r\n    return this.unified.getAvailablePaymentMethods();\r\n  }\r\n\r\n  validatePaymentAmount(amount: number, currency: string = 'USD') {\r\n    return this.unified.validatePaymentAmount(amount, currency);\r\n  }\r\n\r\n  formatCurrency(amount: number, currency: string = 'USD') {\r\n    return this.unified.formatCurrency(amount, currency);\r\n  }\r\n\r\n  getPaymentMethodConfig(method: string) {\r\n    return this.unified.getPaymentMethodConfig(method);\r\n  }\r\n\r\n  handlePaymentError(error: any, paymentMethod: string) {\r\n    return this.unified.handlePaymentError(error, paymentMethod);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const paymentService = new PaymentService();\r\n\r\n// Default export\r\nexport default paymentService;\r\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD,0BAA0B;;;;;;AAC1B;AACA;AACA;AACA;;;;;;;;AAQO,MAAM;IACX,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAC9B,SAAS,4IAAA,CAAA,uBAAoB,CAAC;IAC9B,UAAU,6IAAA,CAAA,wBAAqB,CAAC;IAEhC,uDAAuD;IACvD,MAAM,eACJ,aAA2D,EAC3D,MAAc,EACd,WAAmB,KAAK,EACxB,OAAe,EACf,cAAoB,EACpB;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,QAAQ,UAAU,SAAS;IAC/E;IAEA,MAAM,gBACJ,aAA2D,EAC3D,eAAuB,EACvB,OAAe,EACf;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,eAAe,iBAAiB;IACtE;IAEA,6BAA6B;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC,0BAA0B;IAChD;IAEA,sBAAsB,MAAc,EAAE,WAAmB,KAAK,EAAE;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ;IACpD;IAEA,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAE;QACvD,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ;IAC7C;IAEA,uBAAuB,MAAc,EAAE;QACrC,OAAO,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC;IAC7C;IAEA,mBAAmB,KAAU,EAAE,aAAqB,EAAE;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO;IAChD;AACF;AAGO,MAAM,iBAAiB,IAAI;uCAGnB", "debugId": null}}, {"offset": {"line": 5028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/seed/index.ts"], "sourcesContent": ["import { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\n\r\nexport class SeedService extends BaseService {\r\n  /**\r\n   * Seed all data (statuses, admin user, sample collections and products)\r\n   */\r\n  async seedAll(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Seed.All);\r\n    \r\n    try {\r\n      const response = await this.client.post(ApiEndpoints.Seed.All);\r\n      return {\r\n        success: response.success,\r\n        message: response.message || 'All data seeded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error seeding all data:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to seed data'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed status data\r\n   */\r\n  async seedStatuses(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Seed.Statuses);\r\n    \r\n    try {\r\n      const response = await this.client.post(ApiEndpoints.Seed.Statuses);\r\n      return {\r\n        success: response.success,\r\n        message: response.message || 'Status data seeded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error seeding statuses:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to seed statuses'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed admin user\r\n   */\r\n  async seedAdminUser(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Seed.AdminUser);\r\n    \r\n    try {\r\n      const response = await this.client.post(ApiEndpoints.Seed.AdminUser);\r\n      return {\r\n        success: response.success,\r\n        message: response.message || 'Admin user seeded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error seeding admin user:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to seed admin user'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed sample collections\r\n   */\r\n  async seedCollections(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Seed.Collections);\r\n    \r\n    try {\r\n      const response = await this.client.post(ApiEndpoints.Seed.Collections);\r\n      return {\r\n        success: response.success,\r\n        message: response.message || 'Sample collections seeded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error seeding collections:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to seed collections'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Seed sample products\r\n   */\r\n  async seedProducts(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    this.logApiCall('POST', ApiEndpoints.Seed.Products);\r\n    \r\n    try {\r\n      const response = await this.client.post(ApiEndpoints.Seed.Products);\r\n      return {\r\n        success: response.success,\r\n        message: response.message || 'Sample products seeded successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error seeding products:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to seed products'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize database with all required data\r\n   */\r\n  async initializeDatabase(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n    steps: Array<{\r\n      step: string;\r\n      success: boolean;\r\n      message: string;\r\n    }>;\r\n  }> {\r\n    const steps: Array<{\r\n      step: string;\r\n      success: boolean;\r\n      message: string;\r\n    }> = [];\r\n\r\n    try {\r\n      // Step 1: Seed statuses\r\n      const statusResult = await this.seedStatuses();\r\n      steps.push({\r\n        step: 'Seed Statuses',\r\n        success: statusResult.success,\r\n        message: statusResult.message\r\n      });\r\n\r\n      // Step 2: Seed admin user\r\n      const adminResult = await this.seedAdminUser();\r\n      steps.push({\r\n        step: 'Seed Admin User',\r\n        success: adminResult.success,\r\n        message: adminResult.message\r\n      });\r\n\r\n      // Step 3: Seed collections\r\n      const collectionsResult = await this.seedCollections();\r\n      steps.push({\r\n        step: 'Seed Collections',\r\n        success: collectionsResult.success,\r\n        message: collectionsResult.message\r\n      });\r\n\r\n      // Step 4: Seed products\r\n      const productsResult = await this.seedProducts();\r\n      steps.push({\r\n        step: 'Seed Products',\r\n        success: productsResult.success,\r\n        message: productsResult.message\r\n      });\r\n\r\n      const allSuccessful = steps.every(step => step.success);\r\n\r\n      return {\r\n        success: allSuccessful,\r\n        message: allSuccessful \r\n          ? 'Database initialized successfully' \r\n          : 'Database initialization completed with some errors',\r\n        steps\r\n      };\r\n    } catch (error) {\r\n      console.error('Error initializing database:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to initialize database',\r\n        steps\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if database needs seeding\r\n   */\r\n  async checkSeedingStatus(): Promise<{\r\n    needsSeeding: boolean;\r\n    missingData: string[];\r\n  }> {\r\n    try {\r\n      // This would require additional endpoints to check data existence\r\n      // For now, we'll return a placeholder response\r\n      return {\r\n        needsSeeding: false,\r\n        missingData: []\r\n      };\r\n    } catch (error) {\r\n      console.error('Error checking seeding status:', error);\r\n      return {\r\n        needsSeeding: true,\r\n        missingData: ['Unable to check status']\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset and reseed database (development only)\r\n   */\r\n  async resetAndReseed(): Promise<{\r\n    success: boolean;\r\n    message: string;\r\n  }> {\r\n    if (process.env.NODE_ENV === 'production') {\r\n      return {\r\n        success: false,\r\n        message: 'Reset and reseed is not allowed in production'\r\n      };\r\n    }\r\n\r\n    try {\r\n      // This would require additional endpoints to clear data\r\n      // For now, we'll just seed all data\r\n      return await this.seedAll();\r\n    } catch (error) {\r\n      console.error('Error resetting and reseeding:', error);\r\n      return {\r\n        success: false,\r\n        message: error instanceof Error ? error.message : 'Failed to reset and reseed'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const seedService = new SeedService();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,oBAAoB,wIAAA,CAAA,cAAW;IAC1C;;GAEC,GACD,MAAM,UAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG;QAE7C,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,GAAG;YAC7D,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAClE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,gBAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;QAEnD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,SAAS;YACnE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,kBAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;QAErD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;YACrE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,eAGH;QACD,IAAI,CAAC,UAAU,CAAC,QAAQ,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;YAClE,OAAO;gBACL,SAAS,SAAS,OAAO;gBACzB,SAAS,SAAS,OAAO,IAAI;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA;;GAEC,GACD,MAAM,qBAQH;QACD,MAAM,QAID,EAAE;QAEP,IAAI;YACF,wBAAwB;YACxB,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY;YAC5C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,aAAa,OAAO;gBAC7B,SAAS,aAAa,OAAO;YAC/B;YAEA,0BAA0B;YAC1B,MAAM,cAAc,MAAM,IAAI,CAAC,aAAa;YAC5C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,YAAY,OAAO;gBAC5B,SAAS,YAAY,OAAO;YAC9B;YAEA,2BAA2B;YAC3B,MAAM,oBAAoB,MAAM,IAAI,CAAC,eAAe;YACpD,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,kBAAkB,OAAO;gBAClC,SAAS,kBAAkB,OAAO;YACpC;YAEA,wBAAwB;YACxB,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY;YAC9C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,eAAe,OAAO;gBAC/B,SAAS,eAAe,OAAO;YACjC;YAEA,MAAM,gBAAgB,MAAM,KAAK,CAAC,CAAA,OAAQ,KAAK,OAAO;YAEtD,OAAO;gBACL,SAAS;gBACT,SAAS,gBACL,sCACA;gBACJ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,qBAGH;QACD,IAAI;YACF,kEAAkE;YAClE,+CAA+C;YAC/C,OAAO;gBACL,cAAc;gBACd,aAAa,EAAE;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,cAAc;gBACd,aAAa;oBAAC;iBAAyB;YACzC;QACF;IACF;IAEA;;GAEC,GACD,MAAM,iBAGH;QACD,uCAA2C;;QAK3C;QAEA,IAAI;YACF,wDAAwD;YACxD,oCAAoC;YACpC,OAAO,MAAM,IAAI,CAAC,OAAO;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;gBACL,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 5221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/contactForm/get.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { BaseService } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ContactFormSubmission, InquiryType } from '../../types/entities';\r\n\r\nexport class ContactFormGetService extends BaseService {\r\n  /**\r\n   * Get all contact form submissions (Admin only)\r\n   */\r\n  async getAll(): Promise<ContactFormSubmission[]> {\r\n    this.logApiCall('GET', '/contactform');\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<ContactFormSubmission[]>('/contactform')\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get contact form submission by ID\r\n   */\r\n  async getById(id: number): Promise<ContactFormSubmission> {\r\n    this.logApiCall('GET', `/contactform/${id}`);\r\n    \r\n    if (!id || id <= 0) {\r\n      throw new Error('Valid submission ID is required');\r\n    }\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<ContactFormSubmission>(`/contactform/${id}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get recent contact form submissions\r\n   */\r\n  async getRecent(count: number = 10): Promise<ContactFormSubmission[]> {\r\n    this.logApiCall('GET', `/contactform/recent?count=${count}`);\r\n    \r\n    if (count <= 0 || count > 100) {\r\n      throw new Error('Count must be between 1 and 100');\r\n    }\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<ContactFormSubmission[]>(`/contactform/recent?count=${count}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get contact form submissions by inquiry type\r\n   */\r\n  async getByInquiryType(inquiryType: InquiryType): Promise<ContactFormSubmission[]> {\r\n    this.logApiCall('GET', `/contactform/inquiry/${inquiryType}`);\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<ContactFormSubmission[]>(`/contactform/inquiry/${inquiryType}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get contact form submissions by date range\r\n   */\r\n  async getByDateRange(startDate: Date, endDate: Date): Promise<ContactFormSubmission[]> {\r\n    const start = startDate.toISOString();\r\n    const end = endDate.toISOString();\r\n    \r\n    this.logApiCall('GET', `/contactform/date-range?startDate=${start}&endDate=${end}`);\r\n    \r\n    if (startDate > endDate) {\r\n      throw new Error('Start date must be before end date');\r\n    }\r\n    \r\n    return this.handleResponse(\r\n      this.client.get<ContactFormSubmission[]>(`/contactform/date-range?startDate=${start}&endDate=${end}`)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get contact form submissions with pagination\r\n   */\r\n  async getPaginated(page: number = 1, pageSize: number = 20): Promise<{\r\n    submissions: ContactFormSubmission[];\r\n    totalCount: number;\r\n    currentPage: number;\r\n    totalPages: number;\r\n  }> {\r\n    this.logApiCall('GET', `/contactform?page=${page}&pageSize=${pageSize}`);\r\n    \r\n    if (page <= 0) {\r\n      throw new Error('Page must be greater than 0');\r\n    }\r\n    \r\n    if (pageSize <= 0 || pageSize > 100) {\r\n      throw new Error('Page size must be between 1 and 100');\r\n    }\r\n    \r\n    // For now, we'll simulate pagination by getting all and slicing\r\n    // In a real implementation, the backend would handle pagination\r\n    const allSubmissions = await this.getAll();\r\n    const startIndex = (page - 1) * pageSize;\r\n    const endIndex = startIndex + pageSize;\r\n    const submissions = allSubmissions.slice(startIndex, endIndex);\r\n    \r\n    return {\r\n      submissions,\r\n      totalCount: allSubmissions.length,\r\n      currentPage: page,\r\n      totalPages: Math.ceil(allSubmissions.length / pageSize)\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Search contact form submissions\r\n   */\r\n  async search(query: string): Promise<ContactFormSubmission[]> {\r\n    this.logApiCall('GET', `/contactform/search?q=${encodeURIComponent(query)}`);\r\n    \r\n    if (!query || query.trim().length === 0) {\r\n      throw new Error('Search query is required');\r\n    }\r\n    \r\n    // For now, we'll simulate search by filtering all submissions\r\n    // In a real implementation, the backend would handle search\r\n    const allSubmissions = await this.getAll();\r\n    const lowerQuery = query.toLowerCase();\r\n    \r\n    return allSubmissions.filter(submission => \r\n      submission.name.toLowerCase().includes(lowerQuery) ||\r\n      submission.email.toLowerCase().includes(lowerQuery) ||\r\n      submission.company?.toLowerCase().includes(lowerQuery) ||\r\n      submission.message.toLowerCase().includes(lowerQuery) ||\r\n      submission.inquiryDisplayName.toLowerCase().includes(lowerQuery)\r\n    );\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const contactFormGetService = new ContactFormGetService();\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;;AAIO,MAAM,8BAA8B,wIAAA,CAAA,cAAW;IACpD;;GAEC,GACD,MAAM,SAA2C;QAC/C,IAAI,CAAC,UAAU,CAAC,OAAO;QAEvB,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAA0B;IAE7C;IAEA;;GAEC,GACD,MAAM,QAAQ,EAAU,EAAkC;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI;QAE3C,IAAI,CAAC,MAAM,MAAM,GAAG;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAwB,CAAC,aAAa,EAAE,IAAI;IAE/D;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAAE,EAAoC;QACpE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,0BAA0B,EAAE,OAAO;QAE3D,IAAI,SAAS,KAAK,QAAQ,KAAK;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAA0B,CAAC,0BAA0B,EAAE,OAAO;IAEjF;IAEA;;GAEC,GACD,MAAM,iBAAiB,WAAwB,EAAoC;QACjF,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,aAAa;QAE5D,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAA0B,CAAC,qBAAqB,EAAE,aAAa;IAElF;IAEA;;GAEC,GACD,MAAM,eAAe,SAAe,EAAE,OAAa,EAAoC;QACrF,MAAM,QAAQ,UAAU,WAAW;QACnC,MAAM,MAAM,QAAQ,WAAW;QAE/B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,kCAAkC,EAAE,MAAM,SAAS,EAAE,KAAK;QAElF,IAAI,YAAY,SAAS;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAA0B,CAAC,kCAAkC,EAAE,MAAM,SAAS,EAAE,KAAK;IAExG;IAEA;;GAEC,GACD,MAAM,aAAa,OAAe,CAAC,EAAE,WAAmB,EAAE,EAKvD;QACD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,KAAK,UAAU,EAAE,UAAU;QAEvE,IAAI,QAAQ,GAAG;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,YAAY,KAAK,WAAW,KAAK;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,gEAAgE;QAChE,gEAAgE;QAChE,MAAM,iBAAiB,MAAM,IAAI,CAAC,MAAM;QACxC,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,cAAc,eAAe,KAAK,CAAC,YAAY;QAErD,OAAO;YACL;YACA,YAAY,eAAe,MAAM;YACjC,aAAa;YACb,YAAY,KAAK,IAAI,CAAC,eAAe,MAAM,GAAG;QAChD;IACF;IAEA;;GAEC,GACD,MAAM,OAAO,KAAa,EAAoC;QAC5D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,mBAAmB,QAAQ;QAE3E,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,KAAK,GAAG;YACvC,MAAM,IAAI,MAAM;QAClB;QAEA,8DAA8D;QAC9D,4DAA4D;QAC5D,MAAM,iBAAiB,MAAM,IAAI,CAAC,MAAM;QACxC,MAAM,aAAa,MAAM,WAAW;QAEpC,OAAO,eAAe,MAAM,CAAC,CAAA,aAC3B,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,WAAW,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,WAAW,OAAO,EAAE,cAAc,SAAS,eAC3C,WAAW,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC1C,WAAW,kBAAkB,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEzD;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 5313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/contactForm/post.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { BaseService, ServiceUtils } from '../../config/baseService';\r\nimport { ApiEndpoints } from '../../config/apiConfig';\r\nimport { ContactFormSubmission, CreateContactFormSubmissionRequest } from '../../types/entities';\r\n\r\nexport class ContactFormPostService extends BaseService {\r\n  /**\r\n   * Submit a new contact form\r\n   */\r\n\r\n  async create(data: CreateContactFormSubmissionRequest): Promise<ContactFormSubmission> {\r\n    this.logApiCall('POST', '/contactform', data);\r\n    \r\n    // Validate required fields\r\n    this.validateCreateRequest(data);\r\n    \r\n    return this.handleResponse(\r\n      this.client.post<ContactFormSubmission>('/contactform', data)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Submit contact form with additional validation\r\n   */\r\n  async submit(formData: {\r\n    name: string;\r\n    email: string;\r\n    phoneNumber: string;\r\n    company?: string;\r\n    state: string;\r\n    inquiry: number;\r\n    message: string;\r\n  }): Promise<ContactFormSubmission> {\r\n    const data: Create<PERSON>ontactFormSubmissionRequest = {\r\n      name: formData.name.trim(),\r\n      email: formData.email.trim().toLowerCase(),\r\n      phoneNumber: formData.phoneNumber.trim(),\r\n      company: formData.company?.trim() || undefined,\r\n      state: formData.state.trim(),\r\n      inquiry: formData.inquiry,\r\n      message: formData.message.trim()\r\n    };\r\n\r\n    return this.create(data);\r\n  }\r\n\r\n  /**\r\n   * Validate create contact form request\r\n   */\r\n  private validateCreateRequest(data: CreateContactFormSubmissionRequest): void {\r\n    if (!data.name || data.name.trim().length === 0) {\r\n      throw new Error('Name is required');\r\n    }\r\n\r\n    if (data.name.length > 100) {\r\n      throw new Error('Name must be 100 characters or less');\r\n    }\r\n\r\n    if (!data.email || !ServiceUtils.isValidEmail(data.email)) {\r\n      throw new Error('Valid email is required');\r\n    }\r\n\r\n    if (!data.phoneNumber || data.phoneNumber.trim().length === 0) {\r\n      throw new Error('Phone number is required');\r\n    }\r\n\r\n    if (!this.isValidPhoneNumber(data.phoneNumber)) {\r\n      throw new Error('Please enter a valid phone number');\r\n    }\r\n\r\n    if (data.company && data.company.length > 200) {\r\n      throw new Error('Company name must be 200 characters or less');\r\n    }\r\n\r\n    if (!data.state || data.state.trim().length === 0) {\r\n      throw new Error('State is required');\r\n    }\r\n\r\n    if (data.state.length > 100) {\r\n      throw new Error('State must be 100 characters or less');\r\n    }\r\n\r\n    if (!data.inquiry || ![1, 2, 3, 4, 5, 6, 7, 8, 9].includes(data.inquiry)) {\r\n      throw new Error('Please select a valid inquiry type');\r\n    }\r\n\r\n    if (!data.message || data.message.trim().length === 0) {\r\n      throw new Error('Message is required');\r\n    }\r\n\r\n    if (data.message.length < 10) {\r\n      throw new Error('Message must be at least 10 characters long');\r\n    }\r\n\r\n    if (data.message.length > 2000) {\r\n      throw new Error('Message must be 2000 characters or less');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate phone number format\r\n   */\r\n  private isValidPhoneNumber(phone: string): boolean {\r\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\r\n    return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const contactFormPostService = new ContactFormPostService();\r\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AACpD;;AAIO,MAAM,+BAA+B,wIAAA,CAAA,cAAW;IACrD;;GAEC,GAED,MAAM,OAAO,IAAwC,EAAkC;QACrF,IAAI,CAAC,UAAU,CAAC,QAAQ,gBAAgB;QAExC,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO,IAAI,CAAC,cAAc,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAwB,gBAAgB;IAE5D;IAEA;;GAEC,GACD,MAAM,OAAO,QAQZ,EAAkC;QACjC,MAAM,OAA2C;YAC/C,MAAM,SAAS,IAAI,CAAC,IAAI;YACxB,OAAO,SAAS,KAAK,CAAC,IAAI,GAAG,WAAW;YACxC,aAAa,SAAS,WAAW,CAAC,IAAI;YACtC,SAAS,SAAS,OAAO,EAAE,UAAU;YACrC,OAAO,SAAS,KAAK,CAAC,IAAI;YAC1B,SAAS,SAAS,OAAO;YACzB,SAAS,SAAS,OAAO,CAAC,IAAI;QAChC;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB;IAEA;;GAEC,GACD,AAAQ,sBAAsB,IAAwC,EAAQ;QAC5E,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,wIAAA,CAAA,eAAY,CAAC,YAAY,CAAC,KAAK,KAAK,GAAG;YACzD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YAC7D,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,WAAW,GAAG;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,KAAK;YAC7C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACjD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,QAAQ,CAAC,KAAK,OAAO,GAAG;YACxE,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACrD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;YAC5B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,MAAM;YAC9B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAa,EAAW;QACjD,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;IACtD;AACF;AAGO,MAAM,yBAAyB,IAAI", "debugId": null}}, {"offset": {"line": 5406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/api/contactForm/index.ts"], "sourcesContent": ["// Contact Form API Services\r\nexport { contactFormGetService } from './get';\r\nexport { contactFormPostService } from './post';\r\n\r\n// Combined service object for easier imports\r\nexport const contactFormService = {\r\n  get: () => import('./get').then(m => m.contactFormGetService),\r\n  post: () => import('./post').then(m => m.contactFormPostService),\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;AAC5B;AACA;;;AAGO,MAAM,qBAAqB;IAChC,KAAK,IAAM,uIAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,qBAAqB;IAC5D,MAAM,IAAM,wIAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,sBAAsB;AACjE", "debugId": null}}, {"offset": {"line": 5434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/services/index.ts"], "sourcesContent": ["// Configuration and Base Services\r\nexport * from './config/apiConfig';\r\nexport * from './config/httpClient';\r\nexport * from './config/baseService';\r\n\r\n// Type Definitions\r\nexport * from './types/entities';\r\n\r\n// Individual Service Exports\r\nexport * from './api/collections';\r\nexport * from './api/products';\r\nexport * from './api/productSpecifications';\r\nexport * from './api/productDetails';\r\nexport * from './api/downloadableContent';\r\nexport * from './api/orders';\r\nexport * from './api/users';\r\nexport * from './api/cart';\r\nexport * from './api/payments';\r\nexport * from './api/seed';\r\nexport * from './api/contactForm';\r\n\r\n// Combined API Service\r\nimport { collectionService } from './api/collections';\r\nimport { productService } from './api/products';\r\nimport { productSpecificationsService } from './api/productSpecifications';\r\nimport { productDetailsService } from './api/productDetails';\r\nimport { downloadableContentService } from './api/downloadableContent';\r\nimport { orderService } from './api/orders';\r\nimport { userService } from './api/users';\r\nimport { cartService } from './api/cart';\r\nimport { paymentService } from './api/payments';\r\nimport { seedService } from './api/seed';\r\nimport { contactFormService } from './api/contactForm';\r\n\r\nexport class ApiService {\r\n  collections = collectionService;\r\n  products = productService;\r\n  productSpecifications = productSpecificationsService;\r\n  productDetails = productDetailsService;\r\n  downloadableContent = downloadableContentService;\r\n  orders = orderService;\r\n  users = userService;\r\n  cart = cartService;\r\n  payments = paymentService;\r\n  seed = seedService;\r\n  contactForm = contactFormService;\r\n}\r\n\r\n// Export singleton instance\r\nexport const apiService = new ApiService();\r\n\r\n// Default export for convenience\r\nexport default apiService;\r\n\r\n// Re-export commonly used services for direct access\r\nexport {\r\n  collectionService,\r\n  productService,\r\n  orderService,\r\n  userService,\r\n  cartService,\r\n  paymentService,\r\n  seedService,\r\n  contactFormService\r\n};\r\n\r\n// Utility exports\r\nexport { ServiceUtils } from './config/baseService';\r\nexport { buildQueryString, ApiError } from './config/apiConfig';\r\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;;AAClC;AACA;AACA;AAEA,mBAAmB;AACnB;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,uBAAuB;AACvB;AACA;AAIA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,MAAM;IACX,cAAc,8JAAA,CAAA,oBAAiB,CAAC;IAChC,WAAW,2JAAA,CAAA,iBAAc,CAAC;IAC1B,wBAAwB,wJAAA,CAAA,+BAA4B,CAAC;IACrD,iBAAiB,iJAAA,CAAA,wBAAqB,CAAC;IACvC,sBAAsB,sJAAA,CAAA,6BAA0B,CAAC;IACjD,SAAS,yJAAA,CAAA,eAAY,CAAC;IACtB,QAAQ,wJAAA,CAAA,cAAW,CAAC;IACpB,OAAO,uJAAA,CAAA,cAAW,CAAC;IACnB,WAAW,2JAAA,CAAA,iBAAc,CAAC;IAC1B,OAAO,uIAAA,CAAA,cAAW,CAAC;IACnB,cAAc,8JAAA,CAAA,qBAAkB,CAAC;AACnC;AAGO,MAAM,aAAa,IAAI;uCAGf", "debugId": null}}, {"offset": {"line": 5538, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/contexts/CartContext.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n'use client';\r\n\r\nimport React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\r\nimport { Cart, CartItem, AddToCartRequest, UpdateCartItemRequest } from '@/services/types/entities';\r\nimport { cartService } from '@/services';\r\n\r\n// Cart State Interface\r\ninterface CartState {\r\n  cart: Cart | null;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  sessionId: string;\r\n}\r\n\r\n// Cart Actions\r\ntype CartAction =\r\n  | { type: 'SET_LOADING'; payload: boolean }\r\n  | { type: 'SET_ERROR'; payload: string | null }\r\n  | { type: 'SET_CART'; payload: Cart | null }\r\n  | { type: 'SET_SESSION_ID'; payload: string }\r\n  | { type: 'ADD_ITEM'; payload: CartItem }\r\n  | { type: 'UPDATE_ITEM'; payload: { productId: number; quantity: number } }\r\n  | { type: 'REMOVE_ITEM'; payload: number }\r\n  | { type: 'CLEAR_CART' };\r\n\r\n// Cart Context Interface\r\ninterface CartContextType {\r\n  state: CartState;\r\n  addToCart: (productId: number, quantity: number, userId?: number) => Promise<void>;\r\n  updateCartItem: (productId: number, quantity: number) => Promise<void>;\r\n  removeFromCart: (productId: number) => Promise<void>;\r\n  clearCart: () => Promise<void>;\r\n  getCartSummary: () => { totalItems: number; totalAmount: number };\r\n  refreshCart: (userId?: number) => Promise<void>;\r\n}\r\n\r\n// Initial State\r\nconst initialState: CartState = {\r\n  cart: null,\r\n  isLoading: false,\r\n  error: null,\r\n  sessionId: '',\r\n};\r\n\r\n// Cart Reducer\r\nfunction cartReducer(state: CartState, action: CartAction): CartState {\r\n  switch (action.type) {\r\n    case 'SET_LOADING':\r\n      return { ...state, isLoading: action.payload };\r\n    case 'SET_ERROR':\r\n      return { ...state, error: action.payload, isLoading: false };\r\n    case 'SET_CART':\r\n      return { ...state, cart: action.payload, isLoading: false, error: null };\r\n    case 'SET_SESSION_ID':\r\n      return { ...state, sessionId: action.payload };\r\n    case 'ADD_ITEM':\r\n      if (!state.cart) return state;\r\n      const existingItemIndex = state.cart.cartItems.findIndex(\r\n        item => item.productId === action.payload.productId\r\n      );\r\n      if (existingItemIndex >= 0) {\r\n        const updatedItems = [...state.cart.cartItems];\r\n        updatedItems[existingItemIndex] = {\r\n          ...updatedItems[existingItemIndex],\r\n          quantity: updatedItems[existingItemIndex].quantity + action.payload.quantity,\r\n        };\r\n        return {\r\n          ...state,\r\n          cart: { ...state.cart, cartItems: updatedItems },\r\n        };\r\n      } else {\r\n        return {\r\n          ...state,\r\n          cart: {\r\n            ...state.cart,\r\n            cartItems: [...state.cart.cartItems, action.payload],\r\n          },\r\n        };\r\n      }\r\n    case 'UPDATE_ITEM':\r\n      if (!state.cart) return state;\r\n      const updatedItems = state.cart.cartItems.map(item =>\r\n        item.productId === action.payload.productId\r\n          ? { ...item, quantity: action.payload.quantity }\r\n          : item\r\n      );\r\n      return {\r\n        ...state,\r\n        cart: { ...state.cart, cartItems: updatedItems },\r\n      };\r\n    case 'REMOVE_ITEM':\r\n      if (!state.cart) return state;\r\n      const filteredItems = state.cart.cartItems.filter(\r\n        item => item.productId !== action.payload\r\n      );\r\n      return {\r\n        ...state,\r\n        cart: { ...state.cart, cartItems: filteredItems },\r\n      };\r\n    case 'CLEAR_CART':\r\n      return {\r\n        ...state,\r\n        cart: state.cart ? { ...state.cart, cartItems: [], totalItems: 0, totalAmount: 0 } : null,\r\n      };\r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\n// Create Context\r\nconst CartContext = createContext<CartContextType | undefined>(undefined);\r\n\r\n// Generate session ID\r\nfunction generateSessionId(): string {\r\n  return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();\r\n}\r\n\r\n// Cart Provider Component\r\ninterface CartProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function CartProvider({ children }: CartProviderProps) {\r\n  const [state, dispatch] = useReducer(cartReducer, initialState);\r\n\r\n  // Initialize session ID on mount\r\n  useEffect(() => {\r\n    const initializeSession = () => {\r\n      let sessionId = localStorage.getItem('cart_session_id');\r\n      if (!sessionId) {\r\n        sessionId = generateSessionId();\r\n        localStorage.setItem('cart_session_id', sessionId);\r\n      }\r\n      dispatch({ type: 'SET_SESSION_ID', payload: sessionId });\r\n    };\r\n\r\n    initializeSession();\r\n  }, []);\r\n\r\n  // Load cart on session ID change\r\n  useEffect(() => {\r\n    if (state.sessionId && state.sessionId.length > 0) {\r\n      loadCart();\r\n    }\r\n  }, [state.sessionId]);\r\n\r\n  const loadCart = async (userId?: number) => {\r\n    try {\r\n      dispatch({ type: 'SET_LOADING', payload: true });\r\n      let cart: Cart | null = null;\r\n\r\n      if (userId) {\r\n        cart = await cartService.get.getByUserId(userId);\r\n      } else if (state.sessionId && state.sessionId.length > 0) {\r\n        // Use getOrCreate to avoid 404 errors for new sessions\r\n        cart = await cartService.get.getOrCreate(undefined, state.sessionId);\r\n      }\r\n\r\n      dispatch({ type: 'SET_CART', payload: cart });\r\n    } catch (error) {\r\n      console.error('Error loading cart:', error);\r\n      dispatch({ type: 'SET_ERROR', payload: 'Failed to load cart' });\r\n    } finally {\r\n      dispatch({ type: 'SET_LOADING', payload: false });\r\n    }\r\n  };\r\n\r\n  const addToCart = async (productId: number, quantity: number, userId?: number) => {\r\n    try {\r\n      dispatch({ type: 'SET_LOADING', payload: true });\r\n\r\n      // Ensure we have either userId or sessionId\r\n      if (!userId && (!state.sessionId || state.sessionId.length === 0)) {\r\n        throw new Error('No user ID or session ID available for cart operation');\r\n      }\r\n\r\n      const request: AddToCartRequest = {\r\n        productId,\r\n        quantity,\r\n        userId,\r\n        sessionId: userId ? undefined : state.sessionId,\r\n      };\r\n\r\n      const updatedCart = await cartService.post.addToCart(request);\r\n      dispatch({ type: 'SET_CART', payload: updatedCart });\r\n    } catch (error: any) {\r\n      console.error('Error adding to cart:', error);\r\n      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to add item to cart' });\r\n    } finally {\r\n      dispatch({ type: 'SET_LOADING', payload: false });\r\n    }\r\n  };\r\n\r\n  const updateCartItem = async (productId: number, quantity: number) => {\r\n    if (!state.cart) return;\r\n\r\n    try {\r\n      dispatch({ type: 'SET_LOADING', payload: true });\r\n      \r\n      const request: UpdateCartItemRequest = { quantity };\r\n      const updatedCart = await cartService.update.updateCartItem(state.cart.id, productId, request);\r\n      dispatch({ type: 'SET_CART', payload: updatedCart });\r\n    } catch (error: any) {\r\n      console.error('Error updating cart item:', error);\r\n      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to update cart item' });\r\n    }\r\n  };\r\n\r\n  const removeFromCart = async (productId: number) => {\r\n    if (!state.cart) return;\r\n\r\n    try {\r\n      dispatch({ type: 'SET_LOADING', payload: true });\r\n      \r\n      await cartService.delete.removeFromCart(state.cart.id, productId);\r\n      dispatch({ type: 'REMOVE_ITEM', payload: productId });\r\n      dispatch({ type: 'SET_LOADING', payload: false });\r\n    } catch (error: any) {\r\n      console.error('Error removing from cart:', error);\r\n      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to remove item from cart' });\r\n    }\r\n  };\r\n\r\n  const clearCart = async () => {\r\n    if (!state.cart) return;\r\n\r\n    try {\r\n      dispatch({ type: 'SET_LOADING', payload: true });\r\n      \r\n      await cartService.delete.clearCart(state.cart.id);\r\n      dispatch({ type: 'CLEAR_CART' });\r\n      dispatch({ type: 'SET_LOADING', payload: false });\r\n    } catch (error: any) {\r\n      console.error('Error clearing cart:', error);\r\n      dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to clear cart' });\r\n    }\r\n  };\r\n\r\n  const getCartSummary = () => {\r\n    if (!state.cart) {\r\n      return { totalItems: 0, totalAmount: 0 };\r\n    }\r\n    return {\r\n      totalItems: state.cart.totalItems,\r\n      totalAmount: state.cart.totalAmount,\r\n    };\r\n  };\r\n\r\n  const refreshCart = async (userId?: number) => {\r\n    await loadCart(userId);\r\n  };\r\n\r\n  const contextValue: CartContextType = {\r\n    state,\r\n    addToCart,\r\n    updateCartItem,\r\n    removeFromCart,\r\n    clearCart,\r\n    getCartSummary,\r\n    refreshCart,\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider value={contextValue}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n}\r\n\r\n// Custom hook to use cart context\r\nexport function useCart() {\r\n  const context = useContext(CartContext);\r\n  if (context === undefined) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD,8CAA8C;;;;;AAG9C;AAEA;AAAA;AAJA;;;;AAoCA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,OAAO;IACP,WAAW;AACb;AAEA,eAAe;AACf,SAAS,YAAY,KAAgB,EAAE,MAAkB;IACvD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;gBAAE,WAAW;YAAM;QAC7D,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,MAAM,OAAO,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAAK;QACzE,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YACxB,MAAM,oBAAoB,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CACtD,CAAA,OAAQ,KAAK,SAAS,KAAK,OAAO,OAAO,CAAC,SAAS;YAErD,IAAI,qBAAqB,GAAG;gBAC1B,MAAM,eAAe;uBAAI,MAAM,IAAI,CAAC,SAAS;iBAAC;gBAC9C,YAAY,CAAC,kBAAkB,GAAG;oBAChC,GAAG,YAAY,CAAC,kBAAkB;oBAClC,UAAU,YAAY,CAAC,kBAAkB,CAAC,QAAQ,GAAG,OAAO,OAAO,CAAC,QAAQ;gBAC9E;gBACA,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;wBAAE,GAAG,MAAM,IAAI;wBAAE,WAAW;oBAAa;gBACjD;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,KAAK;oBACR,MAAM;wBACJ,GAAG,MAAM,IAAI;wBACb,WAAW;+BAAI,MAAM,IAAI,CAAC,SAAS;4BAAE,OAAO,OAAO;yBAAC;oBACtD;gBACF;YACF;QACF,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YACxB,MAAM,eAAe,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,OAC5C,KAAK,SAAS,KAAK,OAAO,OAAO,CAAC,SAAS,GACvC;oBAAE,GAAG,IAAI;oBAAE,UAAU,OAAO,OAAO,CAAC,QAAQ;gBAAC,IAC7C;YAEN,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,WAAW;gBAAa;YACjD;QACF,KAAK;YACH,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;YACxB,MAAM,gBAAgB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAC/C,CAAA,OAAQ,KAAK,SAAS,KAAK,OAAO,OAAO;YAE3C,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;oBAAE,GAAG,MAAM,IAAI;oBAAE,WAAW;gBAAc;YAClD;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,MAAM,IAAI,GAAG;oBAAE,GAAG,MAAM,IAAI;oBAAE,WAAW,EAAE;oBAAE,YAAY;oBAAG,aAAa;gBAAE,IAAI;YACvF;QACF;YACE,OAAO;IACX;AACF;AAEA,iBAAiB;AACjB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,sBAAsB;AACtB,SAAS;IACP,OAAO,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK,MAAM,KAAK,GAAG;AAC9E;AAOO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI,YAAY,aAAa,OAAO,CAAC;YACrC,IAAI,CAAC,WAAW;gBACd,YAAY;gBACZ,aAAa,OAAO,CAAC,mBAAmB;YAC1C;YACA,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAU;QACxD;QAEA;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;YACjD;QACF;IACF,GAAG;QAAC,MAAM,SAAS;KAAC;IAEpB,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAC9C,IAAI,OAAoB;YAExB,IAAI,QAAQ;gBACV,OAAO,MAAM,uJAAA,CAAA,cAAW,CAAC,GAAG,CAAC,WAAW,CAAC;YAC3C,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;gBACxD,uDAAuD;gBACvD,OAAO,MAAM,uJAAA,CAAA,cAAW,CAAC,GAAG,CAAC,WAAW,CAAC,WAAW,MAAM,SAAS;YACrE;YAEA,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAK;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAsB;QAC/D,SAAU;YACR,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;QACjD;IACF;IAEA,MAAM,YAAY,OAAO,WAAmB,UAAkB;QAC5D,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAE9C,4CAA4C;YAC5C,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG;gBACjE,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAA4B;gBAChC;gBACA;gBACA;gBACA,WAAW,SAAS,YAAY,MAAM,SAAS;YACjD;YAEA,MAAM,cAAc,MAAM,uJAAA,CAAA,cAAW,CAAC,IAAI,CAAC,SAAS,CAAC;YACrD,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAY;QACpD,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO,IAAI;YAA6B;QACvF,SAAU;YACR,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;QACjD;IACF;IAEA,MAAM,iBAAiB,OAAO,WAAmB;QAC/C,IAAI,CAAC,MAAM,IAAI,EAAE;QAEjB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAE9C,MAAM,UAAiC;gBAAE;YAAS;YAClD,MAAM,cAAc,MAAM,uJAAA,CAAA,cAAW,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,WAAW;YACtF,SAAS;gBAAE,MAAM;gBAAY,SAAS;YAAY;QACpD,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO,IAAI;YAA6B;QACvF;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,MAAM,IAAI,EAAE;QAEjB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAE9C,MAAM,uJAAA,CAAA,cAAW,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE;YACvD,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAU;YACnD,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;QACjD,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO,IAAI;YAAkC;QAC5F;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,MAAM,IAAI,EAAE;QAEjB,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAK;YAE9C,MAAM,uJAAA,CAAA,cAAW,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;YAChD,SAAS;gBAAE,MAAM;YAAa;YAC9B,SAAS;gBAAE,MAAM;gBAAe,SAAS;YAAM;QACjD,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;gBAAE,MAAM;gBAAa,SAAS,MAAM,OAAO,IAAI;YAAuB;QACjF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,IAAI,EAAE;YACf,OAAO;gBAAE,YAAY;gBAAG,aAAa;YAAE;QACzC;QACA,OAAO;YACL,YAAY,MAAM,IAAI,CAAC,UAAU;YACjC,aAAa,MAAM,IAAI,CAAC,WAAW;QACrC;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,SAAS;IACjB;IAEA,MAAM,eAAgC;QACpC;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5860, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Header/header.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"Headerroot\": \"header-module__zhIT0W__Headerroot\",\n  \"active\": \"header-module__zhIT0W__active\",\n  \"cartBadge\": \"header-module__zhIT0W__cartBadge\",\n  \"cartBadgeAppear\": \"header-module__zhIT0W__cartBadgeAppear\",\n  \"cartContainer\": \"header-module__zhIT0W__cartContainer\",\n  \"cartIconWrapper\": \"header-module__zhIT0W__cartIconWrapper\",\n  \"cartLink\": \"header-module__zhIT0W__cartLink\",\n  \"container\": \"header-module__zhIT0W__container\",\n  \"dropdown\": \"header-module__zhIT0W__dropdown\",\n  \"dropdownContainer\": \"header-module__zhIT0W__dropdownContainer\",\n  \"dropdownIcon\": \"header-module__zhIT0W__dropdownIcon\",\n  \"dropdownItem\": \"header-module__zhIT0W__dropdownItem\",\n  \"dropdownLink\": \"header-module__zhIT0W__dropdownLink\",\n  \"dropdownList\": \"header-module__zhIT0W__dropdownList\",\n  \"dropdownSlideIn\": \"header-module__zhIT0W__dropdownSlideIn\",\n  \"header\": \"header-module__zhIT0W__header\",\n  \"loadingIcon\": \"header-module__zhIT0W__loadingIcon\",\n  \"logo\": \"header-module__zhIT0W__logo\",\n  \"logoLink\": \"header-module__zhIT0W__logoLink\",\n  \"logoSubtext\": \"header-module__zhIT0W__logoSubtext\",\n  \"logoText\": \"header-module__zhIT0W__logoText\",\n  \"nav\": \"header-module__zhIT0W__nav\",\n  \"navButton\": \"header-module__zhIT0W__navButton\",\n  \"navItem\": \"header-module__zhIT0W__navItem\",\n  \"navLink\": \"header-module__zhIT0W__navLink\",\n  \"navList\": \"header-module__zhIT0W__navList\",\n  \"rotated\": \"header-module__zhIT0W__rotated\",\n  \"scrolled\": \"header-module__zhIT0W__scrolled\",\n  \"subDropdownItem\": \"header-module__zhIT0W__subDropdownItem\",\n  \"subDropdownLink\": \"header-module__zhIT0W__subDropdownLink\",\n  \"subDropdownList\": \"header-module__zhIT0W__subDropdownList\",\n  \"subSubDropdownItem\": \"header-module__zhIT0W__subSubDropdownItem\",\n  \"subSubDropdownLink\": \"header-module__zhIT0W__subSubDropdownLink\",\n  \"subSubDropdownList\": \"header-module__zhIT0W__subSubDropdownList\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 5902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/shared/Header/Header.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\r\n'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport Link from 'next/link';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport { collectionGetService } from '../../../services/api/collections';\r\nimport { CollectionHierarchy } from '../../../services/types/entities';\r\nimport { DropdownItem } from '../../../types';\r\nimport styles from './header.module.css';\r\nimport { usePathname } from 'next/navigation';\r\n\r\n\r\ninterface HeaderProps {\r\n  title?: string;\r\n}\r\n\r\n\r\nconst Header: React.FC<HeaderProps> = ({ title = \"Cast Stone\" }) => {\r\n  \r\n  const { getCartSummary } = useCart();\r\n  const [collections, setCollections] = useState<CollectionHierarchy[]>([]);\r\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});\r\n  const pathname = usePathname();\r\n\r\n\r\n     if (pathname.startsWith('/admin')) {\r\n    return null;\r\n  }\r\n\r\n\r\n  // Hide header on admin dashboard routes\r\n \r\n  \r\n  // Company dropdown items\r\n  const companyItems: DropdownItem[] = [\r\n    { label: 'Contact Us', href: '/contact' },\r\n    { label: 'Our Story', href: '/our-story' },\r\n    { label: 'Retail Locator', href: '/retail-locator' },\r\n    { label: 'Wholesale Signup', href: '/wholesale-signup' }\r\n  ];\r\n\r\n  // Discover dropdown items\r\n  const discoverItems: DropdownItem[] = [\r\n    { label: 'Catalog', href: '/catalog' },\r\n    { label: 'Finishes', href: '/finishes' },\r\n    { label: 'Videos', href: '/videos' },\r\n    { label: 'Technical Info', href: '/technical-info' },\r\n    { label: 'FAQ', href: '/faq' }\r\n  ];\r\n\r\n  \r\n  // Handle scroll effect\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsScrolled(window.scrollY > 50);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    \r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  // Fetch collections on component mount\r\n  useEffect(() => {\r\n    const fetchCollections = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        const hierarchyData = await collectionGetService.getHierarchy();\r\n        setCollections(hierarchyData);\r\n      } catch (error) {\r\n        console.error('Failed to fetch collections:', error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCollections();\r\n  }, []);\r\n\r\n  // Handle dropdown toggle\r\n  const handleDropdownToggle = (dropdownName: string) => {\r\n    setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);\r\n  };\r\n\r\n  // Handle click outside to close dropdown\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      const target = event.target as Node;\r\n      const isClickInsideDropdown = Object.values(dropdownRefs.current).some(\r\n        ref => ref && ref.contains(target)\r\n      );\r\n\r\n      if (!isClickInsideDropdown) {\r\n        setActiveDropdown(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => document.removeEventListener('mousedown', handleClickOutside);\r\n  }, []);\r\n\r\n  // Convert collections to dropdown items\r\n  const collectionsToDropdownItems = (collections: CollectionHierarchy[]): DropdownItem[] => {\r\n    return collections.map(collection => ({\r\n      label: collection.name,\r\n      href: `/collections/${collection.id}`,\r\n      children: collection.children.length > 0\r\n        ? collectionsToDropdownItems(collection.children)\r\n        : undefined\r\n    }));\r\n  };\r\n\r\n  const collectionItems = collectionsToDropdownItems(collections);\r\n\r\n  return (\r\n    <header className={`${styles.header} ${isScrolled ? styles.scrolled : ''}`}>\r\n      <div className={styles.container}>\r\n        {/* Logo */}\r\n        <div className={styles.logo}>\r\n          <Link href=\"/\" className={styles.logoLink}>\r\n            <span className={styles.logoText}>{title}</span>\r\n            <span className={styles.logoSubtext}>Interiors & Decorations</span>\r\n          </Link>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className={styles.nav}>\r\n          <ul className={styles.navList}>\r\n            {/* Company Dropdown */}\r\n            <li className={styles.navItem}>\r\n              <div\r\n                className={styles.dropdownContainer}\r\n                ref={el => { dropdownRefs.current['company'] = el; }}\r\n              >\r\n                <button\r\n                  className={`${styles.navButton} ${activeDropdown === 'company' ? styles.active : ''}`}\r\n                  onClick={() => handleDropdownToggle('company')}\r\n                  aria-expanded={activeDropdown === 'company'}\r\n                >\r\n                  Company\r\n                  <span className={`${styles.dropdownIcon} ${activeDropdown === 'company' ? styles.rotated : ''}`}>\r\n                    <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\r\n                      <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    </svg>\r\n                  </span>\r\n                </button>\r\n                {activeDropdown === 'company' && (\r\n                  <div className={styles.dropdown}>\r\n                    <ul className={styles.dropdownList}>\r\n                      {companyItems.map((item, index) => (\r\n                        <li key={index} className={styles.dropdownItem}>\r\n                          <Link href={item.href} className={styles.dropdownLink}>\r\n                            {item.label}\r\n                          </Link>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </li>\r\n\r\n            {/* Products */}\r\n            <li className={styles.navItem}>\r\n              <Link href=\"/products\" className={styles.navLink}>\r\n                Products\r\n              </Link>\r\n            </li>\r\n\r\n            {/* Collections Dropdown */}\r\n            <li className={styles.navItem}>\r\n              <div\r\n                className={styles.dropdownContainer}\r\n                ref={el => {dropdownRefs.current['collections'] = el}}\r\n              >\r\n                <button\r\n                  className={`${styles.navButton} ${activeDropdown === 'collections' ? styles.active : ''}`}\r\n                  onClick={() => handleDropdownToggle('collections')}\r\n                  aria-expanded={activeDropdown === 'collections'}\r\n                  disabled={isLoading}\r\n                >\r\n                  Collections\r\n                  {isLoading ? (\r\n                    <span className={styles.loadingIcon}>\r\n                      <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\">\r\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeDasharray=\"31.416\" strokeDashoffset=\"31.416\">\r\n                          <animate attributeName=\"stroke-dasharray\" dur=\"2s\" values=\"0 31.416;15.708 15.708;0 31.416\" repeatCount=\"indefinite\"/>\r\n                          <animate attributeName=\"stroke-dashoffset\" dur=\"2s\" values=\"0;-15.708;-31.416\" repeatCount=\"indefinite\"/>\r\n                        </circle>\r\n                      </svg>\r\n                    </span>\r\n                  ) : (\r\n                    <span className={`${styles.dropdownIcon} ${activeDropdown === 'collections' ? styles.rotated : ''}`}>\r\n                      <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\r\n                        <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                      </svg>\r\n                    </span>\r\n                  )}\r\n                </button>\r\n                {activeDropdown === 'collections' && !isLoading && (\r\n                  <div className={styles.dropdown}>\r\n                    <ul className={styles.dropdownList}>\r\n                      {collectionItems.map((item, index) => (\r\n                        <li key={index} className={styles.dropdownItem}>\r\n                          <Link href={item.href} className={styles.dropdownLink}>\r\n                            {item.label}\r\n                          </Link>\r\n                          {item.children && item.children.length > 0 && (\r\n                            <ul className={styles.subDropdownList}>\r\n                              {item.children.map((child, childIndex) => (\r\n                                <li key={childIndex} className={styles.subDropdownItem}>\r\n                                  <Link href={child.href} className={styles.subDropdownLink}>\r\n                                    {child.label}\r\n                                  </Link>\r\n                                  {child.children && child.children.length > 0 && (\r\n                                    <ul className={styles.subSubDropdownList}>\r\n                                      {child.children.map((grandChild, grandChildIndex) => (\r\n                                        <li key={grandChildIndex} className={styles.subSubDropdownItem}>\r\n                                          <Link href={grandChild.href} className={styles.subSubDropdownLink}>\r\n                                            {grandChild.label}\r\n                                          </Link>\r\n                                        </li>\r\n                                      ))}\r\n                                    </ul>\r\n                                  )}\r\n                                </li>\r\n                              ))}\r\n                            </ul>\r\n                          )}\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </li>\r\n\r\n            {/* Completed Projects */}\r\n            <li className={styles.navItem}>\r\n              <Link href=\"/completed-projects\" className={styles.navLink}>\r\n                Completed Projects\r\n              </Link>\r\n            </li>\r\n\r\n            {/* Discover Dropdown */}\r\n            <li className={styles.navItem}>\r\n              <div\r\n                className={styles.dropdownContainer}\r\n                ref={el => {dropdownRefs.current['discover'] = el}}\r\n              >\r\n                <button\r\n                  className={`${styles.navButton} ${activeDropdown === 'discover' ? styles.active : ''}`}\r\n                  onClick={() => handleDropdownToggle('discover')}\r\n                  aria-expanded={activeDropdown === 'discover'}\r\n                >\r\n                  Discover\r\n                  <span className={`${styles.dropdownIcon} ${activeDropdown === 'discover' ? styles.rotated : ''}`}>\r\n                    <svg width=\"12\" height=\"8\" viewBox=\"0 0 12 8\" fill=\"none\">\r\n                      <path d=\"M1 1.5L6 6.5L11 1.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\r\n                    </svg>\r\n                  </span>\r\n                </button>\r\n                {activeDropdown === 'discover' && (\r\n                  <div className={styles.dropdown}>\r\n                    <ul className={styles.dropdownList}>\r\n                      {discoverItems.map((item, index) => (\r\n                        <li key={index} className={styles.dropdownItem}>\r\n                          <Link href={item.href} className={styles.dropdownLink}>\r\n                            {item.label}\r\n                          </Link>\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n\r\n        {/* Cart Icon */}\r\n        <div className={styles.cartContainer}>\r\n          <Link href=\"/cart\" className={styles.cartLink}>\r\n            <div className={styles.cartIconWrapper}>\r\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1.5\">\r\n                <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\r\n              </svg>\r\n              {getCartSummary().totalItems > 0 && (\r\n                <span className={styles.cartBadge}>\r\n                  {getCartSummary().totalItems}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAG7C;AACA;AACA;AACA;AAAA;AAGA;AACA;AATA;;;;;;;;AAiBA,MAAM,SAAgC,CAAC,EAAE,QAAQ,YAAY,EAAE;IAE7D,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA4C,CAAC;IACvE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAGxB,IAAI,SAAS,UAAU,CAAC,WAAW;QACpC,OAAO;IACT;IAGA,wCAAwC;IAGxC,yBAAyB;IACzB,MAAM,eAA+B;QACnC;YAAE,OAAO;YAAc,MAAM;QAAW;QACxC;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAkB,MAAM;QAAkB;QACnD;YAAE,OAAO;YAAoB,MAAM;QAAoB;KACxD;IAED,0BAA0B;IAC1B,MAAM,gBAAgC;QACpC;YAAE,OAAO;YAAW,MAAM;QAAW;QACrC;YAAE,OAAO;YAAY,MAAM;QAAY;QACvC;YAAE,OAAO;YAAU,MAAM;QAAU;QACnC;YAAE,OAAO;YAAkB,MAAM;QAAkB;QACnD;YAAE,OAAO;YAAO,MAAM;QAAO;KAC9B;IAGD,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,aAAa;gBACb,MAAM,gBAAgB,MAAM,4IAAA,CAAA,uBAAoB,CAAC,YAAY;gBAC7D,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,mBAAmB,eAAe,OAAO;IAC7D;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,wBAAwB,OAAO,MAAM,CAAC,aAAa,OAAO,EAAE,IAAI,CACpE,CAAA,MAAO,OAAO,IAAI,QAAQ,CAAC;YAG7B,IAAI,CAAC,uBAAuB;gBAC1B,kBAAkB;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,6BAA6B,CAAC;QAClC,OAAO,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBACpC,OAAO,WAAW,IAAI;gBACtB,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;gBACrC,UAAU,WAAW,QAAQ,CAAC,MAAM,GAAG,IACnC,2BAA2B,WAAW,QAAQ,IAC9C;YACN,CAAC;IACH;IAEA,MAAM,kBAAkB,2BAA2B;IAEnD,qBACE,8OAAC;QAAO,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,2JAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;kBACxE,cAAA,8OAAC;YAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;8BAE9B,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;8BACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;;0CACvC,8OAAC;gCAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG;;;;;;0CACnC,8OAAC;gCAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;0CAAE;;;;;;;;;;;;;;;;;8BAKzC,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,GAAG;8BACxB,cAAA,8OAAC;wBAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;;0CAE3B,8OAAC;gCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,8OAAC;oCACC,WAAW,2JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA;wCAAQ,aAAa,OAAO,CAAC,UAAU,GAAG;oCAAI;;sDAEnD,8OAAC;4CACC,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,YAAY,2JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACrF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;;gDACnC;8DAEC,8OAAC;oDAAK,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,YAAY,2JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DAC7F,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,8OAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAIhH,mBAAmB,2BAClB,8OAAC;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,8OAAC;gDAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;wDAAe,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;kEAC5C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sEAClD,KAAK,KAAK;;;;;;uDAFN;;;;;;;;;;;;;;;;;;;;;;;;;;0CAarB,8OAAC;gCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;0CAMpD,8OAAC;gCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,8OAAC;oCACC,WAAW,2JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA;wCAAO,aAAa,OAAO,CAAC,cAAc,GAAG;oCAAE;;sDAEpD,8OAAC;4CACC,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,gBAAgB,2JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACzF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;4CAClC,UAAU;;gDACX;gDAEE,0BACC,8OAAC;oDAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;8DACjC,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,MAAK;kEACnD,cAAA,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,iBAAgB;4DAAS,kBAAiB;;8EACnI,8OAAC;oEAAQ,eAAc;oEAAmB,KAAI;oEAAK,QAAO;oEAAkC,aAAY;;;;;;8EACxG,8OAAC;oEAAQ,eAAc;oEAAoB,KAAI;oEAAK,QAAO;oEAAoB,aAAY;;;;;;;;;;;;;;;;;;;;;yEAKjG,8OAAC;oDAAK,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,gBAAgB,2JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DACjG,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,8OAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAKlH,mBAAmB,iBAAiB,CAAC,2BACpC,8OAAC;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,8OAAC;gDAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC;wDAAe,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;;0EAC5C,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,KAAK,IAAI;gEAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;0EAClD,KAAK,KAAK;;;;;;4DAEZ,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,8OAAC;gEAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,eAAe;0EAClC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC;wEAAoB,WAAW,2JAAA,CAAA,UAAM,CAAC,eAAe;;0FACpD,8OAAC,4JAAA,CAAA,UAAI;gFAAC,MAAM,MAAM,IAAI;gFAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,eAAe;0FACtD,MAAM,KAAK;;;;;;4EAEb,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACzC,8OAAC;gFAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,kBAAkB;0FACrC,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,YAAY,gCAC/B,8OAAC;wFAAyB,WAAW,2JAAA,CAAA,UAAM,CAAC,kBAAkB;kGAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;4FAAC,MAAM,WAAW,IAAI;4FAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,kBAAkB;sGAC9D,WAAW,KAAK;;;;;;uFAFZ;;;;;;;;;;;uEAPR;;;;;;;;;;;uDAPR;;;;;;;;;;;;;;;;;;;;;;;;;;0CAmCrB,8OAAC;gCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAsB,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;8CAAE;;;;;;;;;;;0CAM9D,8OAAC;gCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;0CAC3B,cAAA,8OAAC;oCACC,WAAW,2JAAA,CAAA,UAAM,CAAC,iBAAiB;oCACnC,KAAK,CAAA;wCAAO,aAAa,OAAO,CAAC,WAAW,GAAG;oCAAE;;sDAEjD,8OAAC;4CACC,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,aAAa,2JAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;4CACtF,SAAS,IAAM,qBAAqB;4CACpC,iBAAe,mBAAmB;;gDACnC;8DAEC,8OAAC;oDAAK,WAAW,GAAG,2JAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,mBAAmB,aAAa,2JAAA,CAAA,UAAM,CAAC,OAAO,GAAG,IAAI;8DAC9F,cAAA,8OAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAI,SAAQ;wDAAW,MAAK;kEACjD,cAAA,8OAAC;4DAAK,GAAE;4DAAsB,QAAO;4DAAe,aAAY;4DAAM,eAAc;4DAAQ,gBAAe;;;;;;;;;;;;;;;;;;;;;;wCAIhH,mBAAmB,4BAClB,8OAAC;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;sDAC7B,cAAA,8OAAC;gDAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;wDAAe,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;kEAC5C,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI;4DAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sEAClD,KAAK,KAAK;;;;;;uDAFN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAezB,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,aAAa;8BAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAQ,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;kCAC3C,cAAA,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,eAAe;;8CACpC,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;oCAAO,QAAO;oCAAe,aAAY;8CAC5F,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;gCAET,iBAAiB,UAAU,GAAG,mBAC7B,8OAAC;oCAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;8CAC9B,iBAAiB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;uCAEe", "debugId": null}}, {"offset": {"line": 6548, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Footer/footer.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bottom\": \"footer-module__94Lf8a__bottom\",\n  \"brand\": \"footer-module__94Lf8a__brand\",\n  \"brandDescription\": \"footer-module__94Lf8a__brandDescription\",\n  \"brandName\": \"footer-module__94Lf8a__brandName\",\n  \"contactDetails\": \"footer-module__94Lf8a__contactDetails\",\n  \"contactInfo\": \"footer-module__94Lf8a__contactInfo\",\n  \"contactItem\": \"footer-module__94Lf8a__contactItem\",\n  \"contactLabel\": \"footer-module__94Lf8a__contactLabel\",\n  \"contactTitle\": \"footer-module__94Lf8a__contactTitle\",\n  \"contactValue\": \"footer-module__94Lf8a__contactValue\",\n  \"container\": \"footer-module__94Lf8a__container\",\n  \"content\": \"footer-module__94Lf8a__content\",\n  \"copyright\": \"footer-module__94Lf8a__copyright\",\n  \"footer\": \"footer-module__94Lf8a__footer\",\n  \"legalLink\": \"footer-module__94Lf8a__legalLink\",\n  \"legalLinks\": \"footer-module__94Lf8a__legalLinks\",\n  \"link\": \"footer-module__94Lf8a__link\",\n  \"linkGroup\": \"footer-module__94Lf8a__linkGroup\",\n  \"linkGroupTitle\": \"footer-module__94Lf8a__linkGroupTitle\",\n  \"linkList\": \"footer-module__94Lf8a__linkList\",\n  \"socialLink\": \"footer-module__94Lf8a__socialLink\",\n  \"socialLinks\": \"footer-module__94Lf8a__socialLinks\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 6578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stone%20v2/cast-stonev2/Frontend/cast-stone-frontend/src/components/shared/Footer/Footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport styles from './footer.module.css';\r\nimport { usePathname } from 'next/navigation';\r\n\r\ninterface FooterProps {\r\n  companyName?: string;\r\n}\r\n\r\nconst Footer: React.FC<FooterProps> = ({ companyName = \"Cast Stone\" }) => {\r\n  const currentYear = new Date().getFullYear();\r\n  const pathname = usePathname();\r\n  \r\n  \r\n    if (pathname.startsWith('/admin')) {\r\n      return null;\r\n    }\r\n\r\n  return (\r\n    <footer className={styles.footer}>\r\n      <div className={styles.container}>\r\n        <div className={styles.content}>\r\n          {/* Brand Section */}\r\n          <div className={styles.brand}>\r\n            <h3 className={styles.brandName}>{companyName}</h3>\r\n            <p className={styles.brandDescription}>\r\n              Creating timeless beauty with handcrafted cast stone elements for over 25 years.\r\n            </p>\r\n\r\n            <div className={styles.socialLinks}>\r\n              <a href=\"#\" className={styles.socialLink} aria-label=\"Facebook\">\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n                </svg>\r\n              </a>\r\n              <a href=\"#\" className={styles.socialLink} aria-label=\"Instagram\">\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\r\n                </svg>\r\n              </a>\r\n              <a href=\"#\" className={styles.socialLink} aria-label=\"LinkedIn\">\r\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\r\n                </svg>\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Company Links */}\r\n          <div className={styles.linkGroup}>\r\n            <h4 className={styles.linkGroupTitle}>Company</h4>\r\n            <ul className={styles.linkList}>\r\n              <li><Link href=\"/contact\" className={styles.link}>Contact Us</Link></li>\r\n              <li><Link href=\"/our-story\" className={styles.link}>Our Story</Link></li>\r\n              <li><Link href=\"/retail-locator\" className={styles.link}>Retail Locator</Link></li>\r\n              <li><Link href=\"/wholesale-signup\" className={styles.link}>Wholesale Sign-up</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Products Links */}\r\n          <div className={styles.linkGroup}>\r\n            <h4 className={styles.linkGroupTitle}>Products</h4>\r\n            <ul className={styles.linkList}>\r\n              <li><Link href=\"/products/architectural\" className={styles.link}>Architectural Products</Link></li>\r\n              <li><Link href=\"/products/designer\" className={styles.link}>Designer Products</Link></li>\r\n              <li><Link href=\"/products/limited-edition\" className={styles.link}>Limited Edition</Link></li>\r\n              <li><Link href=\"/products/sealers\" className={styles.link}>Cast Stone Sealers</Link></li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Discover Links */}\r\n          <div className={styles.linkGroup}>\r\n            <h4 className={styles.linkGroupTitle}>Discover</h4>\r\n            <ul className={styles.linkList}>\r\n              <li><Link href=\"/catalog\" className={styles.link}>Catalog</Link></li>\r\n              <li><Link href=\"/collections\" className={styles.link}>Collections</Link></li>\r\n              <li><Link href=\"/completed-projects\" className={styles.link}>Completed Projects</Link></li>\r\n              <li><Link href=\"/videos\" className={styles.link}>Videos</Link></li>\r\n              <li><Link href=\"/faq\" className={styles.link}>FAQs</Link></li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Contact Info */}\r\n        <div className={styles.contactInfo}>\r\n          <h4 className={styles.contactTitle}>Contact Info</h4>\r\n          <div className={styles.contactDetails}>\r\n            <div className={styles.contactItem}>\r\n              <span className={styles.contactLabel}>Address:</span>\r\n              <span className={styles.contactValue}>\r\n                123 Artisan Way<br />\r\n                Craftsman City, CC 12345\r\n              </span>\r\n            </div>\r\n            <div className={styles.contactItem}>\r\n              <span className={styles.contactLabel}>Phone:</span>\r\n              <span className={styles.contactValue}>(*************</span>\r\n            </div>\r\n            <div className={styles.contactItem}>\r\n              <span className={styles.contactLabel}>Email:</span>\r\n              <span className={styles.contactValue}><EMAIL></span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className={styles.bottom}>\r\n          <p className={styles.copyright}>\r\n            © {currentYear} {companyName}. All rights reserved.\r\n          </p>\r\n          <div className={styles.legalLinks}>\r\n            <Link href=\"/privacy\" className={styles.legalLink}>Privacy Policy</Link>\r\n            <Link href=\"/terms\" className={styles.legalLink}>Terms of Service</Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,cAAc,YAAY,EAAE;IACnE,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAGzB,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,OAAO;IACT;IAEF,qBACE,8OAAC;QAAO,WAAW,2JAAA,CAAA,UAAM,CAAC,MAAM;kBAC9B,cAAA,8OAAC;YAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;8BAC9B,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,OAAO;;sCAE5B,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,KAAK;;8CAC1B,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;8CAAG;;;;;;8CAClC,8OAAC;oCAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,gBAAgB;8CAAE;;;;;;8CAIvC,8OAAC;oCAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAE,MAAK;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,UAAU;4CAAE,cAAW;sDACnD,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,UAAU;4CAAE,cAAW;sDACnD,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,UAAU;4CAAE,cAAW;sDACnD,cAAA,8OAAC;gDAAI,OAAM;gDAAK,QAAO;gDAAK,SAAQ;gDAAY,MAAK;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,cAAc;8CAAE;;;;;;8CACtC,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC5B,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDAClD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACpD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAkB,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACzD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,cAAc;8CAAE;;;;;;8CACtC,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC5B,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA0B,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACjE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDAC5D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA4B,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACnE,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,cAAc;8CAAE;;;;;;8CACtC,8OAAC;oCAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,QAAQ;;sDAC5B,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDAClD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACtD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDAC7D,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;sDACjD,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAW,2JAAA,CAAA,UAAM,CAAC,IAAI;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMpD,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sCAChC,8OAAC;4BAAG,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sCAAE;;;;;;sCACpC,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,cAAc;;8CACnC,8OAAC;oCAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;sDACtC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;;gDAAE;8DACrB,8OAAC;;;;;gDAAK;;;;;;;;;;;;;8CAIzB,8OAAC;oCAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;sDACtC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,WAAW;;sDAChC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;sDACtC,8OAAC;4CAAK,WAAW,2JAAA,CAAA,UAAM,CAAC,YAAY;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,MAAM;;sCAC3B,8OAAC;4BAAE,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;;gCAAE;gCAC3B;gCAAY;gCAAE;gCAAY;;;;;;;sCAE/B,8OAAC;4BAAI,WAAW,2JAAA,CAAA,UAAM,CAAC,UAAU;;8CAC/B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;8CACnD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAW,2JAAA,CAAA,UAAM,CAAC,SAAS;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7D;uCAEe", "debugId": null}}]}