{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductGrid/productGrid.module.css"], "sourcesContent": ["/* Product Grid Styles - Magazine/Editorial Theme */\r\n.productGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  padding: 2rem 0;\r\n}\r\n\r\n.loadingGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n}\r\n\r\n.loadingCard {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  animation: pulse 1.5s ease-in-out infinite;\r\n}\r\n\r\n.loadingImage {\r\n  width: 100%;\r\n  height: 250px;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n}\r\n\r\n.loadingContent {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.loadingTitle {\r\n  height: 1.5rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.loadingDescription {\r\n  height: 1rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.5rem;\r\n  width: 80%;\r\n}\r\n\r\n.loadingPrice {\r\n  height: 1.25rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 1rem;\r\n  width: 60%;\r\n}\r\n\r\n.loadingButton {\r\n  height: 2.5rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Empty State */\r\n.emptyContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4rem 2rem;\r\n  text-align: center;\r\n  min-height: 300px;\r\n}\r\n\r\n.emptyIcon {\r\n  width: 80px;\r\n  height: 80px;\r\n  color: #d1d5db;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.emptyIcon svg {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.emptyTitle {\r\n  color: #4a3728;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.emptyMessage {\r\n  color: #6b5b4d;\r\n  font-size: 1rem;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n  max-width: 400px;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1200px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 1rem;\r\n    padding: 1rem 0;\r\n  }\r\n  \r\n  .emptyContainer {\r\n    padding: 3rem 1rem;\r\n  }\r\n  \r\n  .emptyIcon {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .emptyTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .emptyMessage {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AASA;EACE;;;;;;AAOF;EACE;;;;;;EAOA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE"}}]}