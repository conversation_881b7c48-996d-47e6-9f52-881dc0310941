/* [project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css [app-client] (css) */
.collectionsCarousel-module____MCqq__collectionsSection {
  background: linear-gradient(135deg, #fff 0%, #faf9f7 100%);
  padding: 6rem 0;
}

.collectionsCarousel-module____MCqq__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.collectionsCarousel-module____MCqq__header {
  justify-content: space-between;
  align-items: flex-end;
  gap: 2rem;
  margin-bottom: 3rem;
  display: flex;
}

.collectionsCarousel-module____MCqq__headerContent {
  flex: 1;
}

.collectionsCarousel-module____MCqq__title {
  color: #1e3a8a;
  letter-spacing: -.02em;
  margin-bottom: 1rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 3rem;
  font-weight: 700;
}

.collectionsCarousel-module____MCqq__subtitle {
  color: #1e40af;
  max-width: 600px;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

.collectionsCarousel-module____MCqq__navigation {
  gap: .5rem;
  display: flex;
}

.collectionsCarousel-module____MCqq__navButton {
  color: #1e3a8a;
  cursor: pointer;
  background: #fff;
  border: 2px solid #4a37281a;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: flex;
  box-shadow: 0 4px 12px #4a37281a;
}

.collectionsCarousel-module____MCqq__navButton:hover {
  color: #fff;
  background: #1e3a8a;
  border-color: #1e3a8a;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px #4a372833;
}

.collectionsCarousel-module____MCqq__carouselContainer {
  margin-bottom: 3rem;
  position: relative;
}

.collectionsCarousel-module____MCqq__carousel {
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
  gap: 1.5rem;
  padding: 1rem 0;
  display: flex;
  overflow-x: auto;
}

.collectionsCarousel-module____MCqq__carousel::-webkit-scrollbar {
  display: none;
}

.collectionsCarousel-module____MCqq__collectionCard {
  background: #fff;
  border-radius: 16px;
  flex: 0 0 320px;
  height: 400px;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px #1e0bac1a;
}

.collectionsCarousel-module____MCqq__collectionCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px #4a372826;
}

.collectionsCarousel-module____MCqq__cardLink {
  width: 100%;
  height: 100%;
  color: inherit;
  text-decoration: none;
  display: block;
}

.collectionsCarousel-module____MCqq__imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.collectionsCarousel-module____MCqq__collectionImage {
  object-fit: cover;
  transition: transform .6s cubic-bezier(.4, 0, .2, 1);
}

.collectionsCarousel-module____MCqq__collectionCard:hover .collectionsCarousel-module____MCqq__collectionImage {
  transform: scale(1.05);
}

.collectionsCarousel-module____MCqq__imageOverlay {
  z-index: 1;
  background: linear-gradient(135deg, #19234166 0%, #141e3c59 50%, #1e284666 100%);
  position: absolute;
  inset: 0;
}

.collectionsCarousel-module____MCqq__noImagePlaceholder {
  background: linear-gradient(135deg, #1e0bac 0%, #4a3728 100%);
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.collectionsCarousel-module____MCqq__noImageText {
  color: #fffc;
  text-transform: uppercase;
  letter-spacing: .1em;
  font-size: 1rem;
  font-weight: 500;
}

.collectionsCarousel-module____MCqq__cardContent {
  color: #fff;
  z-index: 2;
  background: linear-gradient(to top, #19234166 0%, #141e3c59 50%, #1e284666 100% transparent 100%);
  padding: 2rem;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.collectionsCarousel-module____MCqq__productCount {
  text-transform: uppercase;
  letter-spacing: .1em;
  color: #1e3a8a;
  margin-bottom: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .8rem;
  font-weight: 500;
}

.collectionsCarousel-module____MCqq__collectionName {
  color: #fff;
  margin-bottom: .75rem;
  font-family: Georgia, Times New Roman, serif;
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.2;
}

.collectionsCarousel-module____MCqq__collectionDescription {
  color: #ffffffe6;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 1rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  line-height: 1.4;
  display: -webkit-box;
  overflow: hidden;
}

.collectionsCarousel-module____MCqq__cardAction {
  text-transform: uppercase;
  letter-spacing: .05em;
  color: #1e3a8a;
  align-items: center;
  gap: .5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .85rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.collectionsCarousel-module____MCqq__collectionCard:hover .collectionsCarousel-module____MCqq__cardAction {
  color: #fff;
  transform: translateX(4px);
}

.collectionsCarousel-module____MCqq__actionText {
  transition: transform .3s;
}

.collectionsCarousel-module____MCqq__viewAllContainer {
  text-align: center;
}

.collectionsCarousel-module____MCqq__viewAllButton {
  color: #1e3a8a;
  text-transform: uppercase;
  letter-spacing: .05em;
  background: none;
  border: 2px solid #1e3a8a;
  border-radius: 50px;
  align-items: center;
  gap: .75rem;
  padding: 1rem 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
}

.collectionsCarousel-module____MCqq__viewAllButton:hover {
  color: #fff;
  background: #1e3a8a;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px #4a37284d;
}

.collectionsCarousel-module____MCqq__loadingContainer, .collectionsCarousel-module____MCqq__errorContainer {
  text-align: center;
  color: #8b7355;
  padding: 4rem 2rem;
}

.collectionsCarousel-module____MCqq__loadingSpinner {
  border: 3px solid #4a37281a;
  border-top-color: #1e3a8a;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin: 0 auto 1rem;
  animation: 1s linear infinite collectionsCarousel-module____MCqq__spin;
}

@keyframes collectionsCarousel-module____MCqq__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (width <= 1024px) {
  .collectionsCarousel-module____MCqq__collectionsSection {
    padding: 4rem 0;
  }

  .collectionsCarousel-module____MCqq__container {
    padding: 0 1.5rem;
  }

  .collectionsCarousel-module____MCqq__title {
    font-size: 2.5rem;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 280px;
    height: 350px;
  }
}

@media (width <= 768px) {
  .collectionsCarousel-module____MCqq__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }

  .collectionsCarousel-module____MCqq__navigation {
    align-self: flex-end;
  }

  .collectionsCarousel-module____MCqq__title {
    font-size: 2rem;
  }

  .collectionsCarousel-module____MCqq__subtitle {
    font-size: 1rem;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 260px;
    height: 320px;
  }

  .collectionsCarousel-module____MCqq__cardContent {
    padding: 1.5rem;
  }
}

@media (width <= 480px) {
  .collectionsCarousel-module____MCqq__container {
    padding: 0 1rem;
  }

  .collectionsCarousel-module____MCqq__header {
    margin-bottom: 2rem;
  }

  .collectionsCarousel-module____MCqq__navigation {
    display: none;
  }

  .collectionsCarousel-module____MCqq__collectionCard {
    flex: 0 0 240px;
    height: 300px;
  }

  .collectionsCarousel-module____MCqq__cardContent {
    padding: 1.25rem;
  }

  .collectionsCarousel-module____MCqq__collectionName {
    font-size: 1.25rem;
  }
}

/*# sourceMappingURL=src_components_Home_CollectionsCarousel_collectionsCarousel_module_css_f9ee138c._.single.css.map*/