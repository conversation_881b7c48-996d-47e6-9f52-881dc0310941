/* [project]/src/components/shared/Header/header.module.css [app-client] (css) */
.header-module__zhIT0W__Headerroot {
  --cast-stone-blue: #2563eb;
  --cast-stone-light-blue: #3b82f6;
  --cast-stone-blue-50: #eff6ff;
  --cast-stone-white: #fff;
  --cast-stone-dark-text: #1f2937;
  --cast-stone-gray-text: #4b5563;
  --cast-stone-shadow: #2563eb1a;
  --cast-stone-shadow-hover: #2563eb26;
  --transition-smooth: all .3s cubic-bezier(.4, 0, .2, 1);
  --transition-fast: all .2s cubic-bezier(.4, 0, .2, 1);
}

.header-module__zhIT0W__header {
  backdrop-filter: none;
  z-index: 1000;
  background: none;
  border: none;
  padding: 0;
  transition: all .3s;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.header-module__zhIT0W__header.header-module__zhIT0W__scrolled {
  backdrop-filter: none;
  background: none;
}

.header-module__zhIT0W__header.header-module__zhIT0W__nonHomePage {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  backdrop-filter: none;
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 2px 10px #2563eb1a;
}

.header-module__zhIT0W__header.header-module__zhIT0W__nonHomePage.header-module__zhIT0W__scrolled {
  background: linear-gradient(135deg, var(--cast-stone-blue), var(--cast-stone-light-blue));
  box-shadow: 0 4px 20px #2563eb26;
}

.header-module__zhIT0W__container {
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  height: 70px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
}

.header-module__zhIT0W__logo {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.header-module__zhIT0W__logoLink {
  color: #fffffff2;
  text-shadow: 2px 2px 4px #000000b3;
  flex-direction: column;
  align-items: flex-start;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__logoLink {
  color: #fffffff2;
  text-shadow: 2px 2px 4px #000000b3;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__logoLink {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px #0000004d;
}

.header-module__zhIT0W__logoLink:hover {
  transform: translateY(-1px);
}

.header-module__zhIT0W__logoText {
  letter-spacing: .1em;
  text-transform: uppercase;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1;
}

.header-module__zhIT0W__logoSubtext {
  letter-spacing: .1em;
  text-transform: uppercase;
  color: #fffc;
  margin-top: 2px;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .75rem;
  font-weight: 400;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__logoSubtext {
  color: #ffffffe6;
}

.header-module__zhIT0W__nav {
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__navList {
  align-items: center;
  gap: 0;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
}

.header-module__zhIT0W__navItem {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
  color: #ffffffe6;
  cursor: pointer;
  letter-spacing: .05em;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px #000000b3;
  background: none;
  border: none;
  align-items: center;
  gap: .5rem;
  padding: 1rem 1.5rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .3s;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton {
  color: #ffffffe6;
  text-shadow: 2px 2px 4px #000000b3;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navLink, .header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navButton {
  color: var(--cast-stone-white);
  text-shadow: 1px 1px 3px #0000004d;
}

.header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__navButton:hover {
  color: #fff;
  text-shadow: 2px 2px 6px #000c;
  transform: translateY(-1px);
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton:hover {
  color: #fff;
  text-shadow: 2px 2px 6px #000c;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navLink:hover, .header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navButton:hover {
  color: #fff;
  text-shadow: 1px 1px 4px #0006;
}

.header-module__zhIT0W__navLink:after, .header-module__zhIT0W__navButton:after {
  content: "";
  background: #ffffffe6;
  width: 0;
  height: 2px;
  transition: all .3s;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 4px #00000080;
}

.header-module__zhIT0W__scrolled .header-module__zhIT0W__navLink:after, .header-module__zhIT0W__scrolled .header-module__zhIT0W__navButton:after {
  background: #ffffffe6;
  box-shadow: 0 0 4px #00000080;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navLink:after, .header-module__zhIT0W__nonHomePage .header-module__zhIT0W__navButton:after {
  background: #ffffffe6;
  box-shadow: 0 0 4px #0000004d;
}

.header-module__zhIT0W__navLink:hover:after, .header-module__zhIT0W__navButton:hover:after, .header-module__zhIT0W__navButton.header-module__zhIT0W__active:after {
  width: 80%;
}

.header-module__zhIT0W__dropdownContainer {
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__dropdownIcon {
  transition: var(--transition-smooth);
  color: #ffffffb3;
  text-shadow: 1px 1px 2px #00000080;
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdownIcon.header-module__zhIT0W__rotated {
  color: #ffffffe6;
  transform: rotate(180deg);
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__dropdownIcon {
  color: #fffc;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__dropdownIcon.header-module__zhIT0W__rotated {
  color: var(--cast-stone-white);
}

.header-module__zhIT0W__loadingIcon {
  color: #ffffffb3;
  text-shadow: 1px 1px 2px #00000080;
  justify-content: center;
  align-items: center;
  display: flex;
}

.header-module__zhIT0W__dropdown {
  background: var(--cast-stone-white);
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  min-width: 280px;
  transition: var(--transition-smooth);
  backdrop-filter: blur(10px);
  border: 1px solid #4a37281a;
  border-radius: 8px;
  animation: .3s cubic-bezier(.4, 0, .2, 1) forwards header-module__zhIT0W__dropdownSlideIn;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%)translateY(-10px);
}

@keyframes header-module__zhIT0W__dropdownSlideIn {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%)translateY(-10px);
  }

  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%)translateY(0);
  }
}

.header-module__zhIT0W__dropdownList {
  margin: 0;
  padding: .5rem 0;
  list-style: none;
}

.header-module__zhIT0W__dropdownItem {
  position: relative;
}

.header-module__zhIT0W__dropdownLink {
  color: var(--cast-stone-dark-text);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .75rem 1.25rem;
  font-size: .9rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
}

.header-module__zhIT0W__dropdownLink:hover {
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-blue);
  background: #2563eb0a;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownList {
  background: #2563eb05;
  border-top: 1px solid #2563eb14;
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-module__zhIT0W__subDropdownItem {
  position: relative;
}

.header-module__zhIT0W__subDropdownLink {
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
  border-left: 3px solid #0000;
  padding: .6rem 1.25rem .6rem 2rem;
  font-size: .85rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
  position: relative;
}

.header-module__zhIT0W__subDropdownLink:before {
  content: "→  ";
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
  font-size: .7rem;
  position: absolute;
  left: .75rem;
}

.header-module__zhIT0W__subDropdownLink:hover {
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  background: #2563eb0f;
  transform: translateX(2px);
}

.header-module__zhIT0W__subDropdownLink:hover:before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

.header-module__zhIT0W__subSubDropdownList {
  background: #2563eb0a;
  border-top: 1px solid #2563eb1f;
  margin: 0;
  padding: 0;
  list-style: none;
}

.header-module__zhIT0W__subSubDropdownItem {
  position: relative;
}

.header-module__zhIT0W__subSubDropdownLink {
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
  border-left: 6px solid #0000;
  padding: .5rem 1.25rem .5rem 2.5rem;
  font-size: .8rem;
  font-weight: 400;
  text-decoration: none;
  display: block;
  position: relative;
}

.header-module__zhIT0W__subSubDropdownLink:before {
  content: "⤷  ";
  color: var(--cast-stone-gray-text);
  transition: var(--transition-fast);
  font-size: .65rem;
  position: absolute;
  left: 1.5rem;
}

.header-module__zhIT0W__subSubDropdownLink:hover {
  color: var(--cast-stone-blue);
  border-left-color: var(--cast-stone-light-blue);
  background: #2563eb14;
  transform: translateX(2px);
}

.header-module__zhIT0W__subSubDropdownLink:hover:before {
  color: var(--cast-stone-blue);
  transform: translateX(2px);
}

.header-module__zhIT0W__cartContainer {
  align-items: center;
  margin-left: 1rem;
  display: flex;
}

.header-module__zhIT0W__cartLink {
  color: #ffffffe6;
  width: 44px;
  height: 44px;
  transition: var(--transition-smooth);
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__cartLink {
  color: var(--cast-stone-white);
}

.header-module__zhIT0W__cartLink:hover {
  color: #fff;
  background: #ffffff1a;
  transform: translateY(-1px);
}

.header-module__zhIT0W__nonHomePage .header-module__zhIT0W__cartLink:hover {
  color: var(--cast-stone-white);
  background: #ffffff26;
}

.header-module__zhIT0W__cartIconWrapper {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.header-module__zhIT0W__cartBadge {
  color: #fff;
  background: #dc2626;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  height: 20px;
  padding: 0 4px;
  font-size: .75rem;
  font-weight: 600;
  animation: .3s ease-out header-module__zhIT0W__cartBadgeAppear;
  display: flex;
  position: absolute;
  top: -8px;
  right: -8px;
  box-shadow: 0 2px 4px #0003;
}

@keyframes header-module__zhIT0W__cartBadgeAppear {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media (width <= 1024px) {
  .header-module__zhIT0W__container {
    padding: 0 1.5rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.5rem 1rem;
    font-size: .9rem;
  }
}

@media (width <= 768px) {
  .header-module__zhIT0W__container {
    height: 70px;
    padding: 0 1rem;
  }

  .header-module__zhIT0W__logoText {
    font-size: 1.75rem;
  }

  .header-module__zhIT0W__logoSubtext {
    font-size: .7rem;
  }

  .header-module__zhIT0W__navList {
    gap: 0;
  }

  .header-module__zhIT0W__navLink, .header-module__zhIT0W__navButton {
    padding: 1.25rem .75rem;
    font-size: .85rem;
  }

  .header-module__zhIT0W__dropdown {
    min-width: 200px;
  }
}

@media (width <= 640px) {
  .header-module__zhIT0W__nav {
    display: none;
  }

  .header-module__zhIT0W__container {
    justify-content: space-between;
  }
}

/*# sourceMappingURL=src_components_shared_Header_header_module_css_f9ee138c._.single.css.map*/