/* [project]/src/components/cart/CartSummary/cartSummary.module.css [app-client] (css) */
.cartSummary-module__kqlNYa__cartSummary {
  background: #fff;
  border-radius: 8px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.cartSummary-module__kqlNYa__title {
  color: #4a3728;
  text-align: center;
  border-bottom: 2px solid #f3f4f6;
  margin: 0 0 1.5rem;
  padding-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__summaryDetails {
  margin-bottom: 1.5rem;
}

.cartSummary-module__kqlNYa__summaryRow {
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  display: flex;
}

.cartSummary-module__kqlNYa__label {
  color: #6b5b4d;
  font-size: 1rem;
  font-weight: 500;
}

.cartSummary-module__kqlNYa__value {
  color: #4a3728;
  font-size: 1rem;
  font-weight: 600;
}

.cartSummary-module__kqlNYa__freeShipping {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.cartSummary-module__kqlNYa__divider {
  background: #e5e7eb;
  height: 1px;
  margin: 1rem 0;
}

.cartSummary-module__kqlNYa__totalRow {
  border-top: 2px solid #4a3728;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding: 1rem 0;
  display: flex;
}

.cartSummary-module__kqlNYa__totalLabel {
  color: #4a3728;
  font-size: 1.25rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__totalValue {
  color: #4a3728;
  font-size: 1.5rem;
  font-weight: 700;
}

.cartSummary-module__kqlNYa__shippingNotice {
  color: #0369a1;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  margin-bottom: 1.5rem;
  padding: .75rem;
  font-size: .9rem;
  display: flex;
}

.cartSummary-module__kqlNYa__infoIcon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

.cartSummary-module__kqlNYa__actionButtons {
  flex-direction: column;
  gap: .75rem;
  margin-bottom: 1.5rem;
  display: flex;
}

.cartSummary-module__kqlNYa__checkoutBtn {
  color: #fff;
  cursor: pointer;
  background: #8b7355;
  border: none;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: flex;
}

.cartSummary-module__kqlNYa__checkoutBtn:hover {
  background: #6d5a47;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px #8b73554d;
}

.cartSummary-module__kqlNYa__checkoutIcon {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cartSummary-module__kqlNYa__clearBtn {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: 2px solid #dc2626;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.cartSummary-module__kqlNYa__clearBtn:hover:not(:disabled) {
  color: #fff;
  background: #dc2626;
}

.cartSummary-module__kqlNYa__clearBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.cartSummary-module__kqlNYa__clearIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.cartSummary-module__kqlNYa__securityNotice {
  color: #059669;
  text-align: center;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  font-size: .9rem;
  font-weight: 600;
  display: flex;
}

.cartSummary-module__kqlNYa__securityIcon {
  stroke-width: 2px;
  width: 16px;
  height: 16px;
}

@media (width <= 1024px) {
  .cartSummary-module__kqlNYa__cartSummary {
    margin-top: 2rem;
    position: static;
  }
}

@media (width <= 768px) {
  .cartSummary-module__kqlNYa__cartSummary {
    margin-top: 1.5rem;
    padding: 1.5rem;
  }

  .cartSummary-module__kqlNYa__title {
    margin-bottom: 1rem;
    font-size: 1.25rem;
  }

  .cartSummary-module__kqlNYa__totalLabel {
    font-size: 1.1rem;
  }

  .cartSummary-module__kqlNYa__totalValue {
    font-size: 1.25rem;
  }

  .cartSummary-module__kqlNYa__checkoutBtn {
    padding: .875rem 1.25rem;
    font-size: .95rem;
  }

  .cartSummary-module__kqlNYa__actionButtons {
    gap: .5rem;
  }
}

@media (width <= 480px) {
  .cartSummary-module__kqlNYa__cartSummary {
    padding: 1rem;
  }

  .cartSummary-module__kqlNYa__summaryRow {
    margin-bottom: .5rem;
  }

  .cartSummary-module__kqlNYa__label, .cartSummary-module__kqlNYa__value {
    font-size: .9rem;
  }

  .cartSummary-module__kqlNYa__totalRow {
    padding: .75rem 0;
  }

  .cartSummary-module__kqlNYa__checkoutBtn {
    padding: .75rem 1rem;
    font-size: .9rem;
  }

  .cartSummary-module__kqlNYa__clearBtn {
    padding: .625rem .875rem;
    font-size: .85rem;
  }
}

/*# sourceMappingURL=src_components_cart_CartSummary_cartSummary_module_css_f9ee138c._.single.css.map*/